package com.mrk.yudong.equipment.biz.scale.service;

import com.mrk.yudong.core.service.BaseService;
import com.mrk.yudong.equipment.infrastructure.scale.model.ScaleRecord;

import java.util.List;

/**
 * <p>
 * 测量信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-17
 */
public interface IScaleRecordService extends BaseService<ScaleRecord> {

    /**
     * 查询最新数据
     * @param measureIdList  测量id集合
     * @param seachType  查询类型
     * <AUTHOR>
     * @date 20:00 2022/7/7
     * @return java.util.List<com.mrk.yudong.equipment.infrastructure.scale.model.ScaleRecord>
     **/
    List<ScaleRecord> minWeightRecordList(List<Long> measureIdList,Integer seachType);

}
