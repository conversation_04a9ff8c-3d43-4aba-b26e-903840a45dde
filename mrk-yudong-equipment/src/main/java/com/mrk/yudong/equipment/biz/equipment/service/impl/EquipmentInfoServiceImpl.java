package com.mrk.yudong.equipment.biz.equipment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.merach.sun.common.lang.exception.BusinessException;
import com.merach.sun.common.layer.web.PageDTO;
import com.merach.sun.device.api.DeviceApi;
import com.merach.sun.device.dto.cmd.device.BindDeviceCmd;
import com.merach.sun.device.dto.cmd.device.MigrateDeviceBindCmd;
import com.merach.sun.device.dto.cmd.device.UnbindDeviceCmd;
import com.merach.sun.device.dto.cmd.model.SetUniqueModelIdentify;
import com.merach.sun.device.dto.qry.DeviceQry;
import com.merach.sun.device.dto.qry.UserDeviceQry;
import com.merach.sun.device.dto.resp.device.BindDeviceDTO;
import com.merach.sun.device.dto.resp.device.DeviceDTO;
import com.merach.sun.device.dto.resp.device.UserDeviceDTO;
import com.merach.sun.device.enums.FirmwareCharacteristicEnum;
import com.merach.sun.device.enums.ProductEnum;
import com.merach.sun.user.api.MemberApi;
import com.merach.sun.user.api.UserTaskApi;
import com.merach.sun.user.dto.vip.cmd.OpenVipCmd;
import com.merach.sun.user.enums.TaskStrategyEnum;
import com.merach.sun.user.enums.vip.VipFlowOperationTypeEnum;
import com.merach.sun.user.enums.vip.VipFlowTypeEnum;
import com.merach.sun.user.enums.vip.VipTypeEnum;
import com.merach.sun.user.form.user.task.TaskForm;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.equipment.api.equipment.dto.NoviceTutorialEquipTypeDTO;
import com.mrk.yudong.equipment.api.equipment.vo.UpdateEquipVO;
import com.mrk.yudong.equipment.biz.equipment.constant.FirmwareCharacteristcConstant;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentInfoService;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentTypeService;
import com.mrk.yudong.equipment.biz.equipment.service.IFirmwareVersionService;
import com.mrk.yudong.equipment.infrastructure.equipment.mapper.EquipmentInfoMapper;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType;
import com.mrk.yudong.equipment.infrastructure.equipment.model.FirmwareVersion;
import com.mrk.yudong.equipment.properties.NoviceTutorialProperties;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.share.constant.ResponseConstant;
import com.mrk.yudong.share.constant.equipment.EquipmentConstant;
import com.mrk.yudong.share.dto.equip.EquipInfoDTO;
import com.mrk.yudong.share.enums.ConnectionOperationEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EquipmentInfoServiceImpl extends BaseServiceImpl<EquipmentInfoMapper, EquipmentInfo> implements IEquipmentInfoService {

    private final EquipmentInfoMapper equipmentInfoMapper;
    private final IEquipmentTypeService equipmentTypeService;
    private final DeviceApi deviceApi;
    private final UserTaskApi userTaskApi;
    private final IFirmwareVersionService firmwareVersionService;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final StringRedisTemplate redisTemplate;
    private final MemberApi memberApi;
    private final NoviceTutorialProperties noviceTutorialProperties;


    @Override
    public List<EquipmentInfo> getNameByUserId(Long userId) {
        QueryWrapper<EquipmentInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id", "one_level_type_id", "two_level_type_id", "code", "name", "update_time").eq("user_by", userId);
        queryWrapper.eq("bind_status", 1);
        queryWrapper.orderByDesc("create_time");
        return equipmentInfoMapper.selectList(queryWrapper);
    }

    @Override
    public List<EquipmentInfo> getHomePage(Long userId) {
        return baseMapper.getHomePage(userId);
    }

    @Override
    public Integer deleteById(Long id) {
        return baseMapper.physicsDeleteById(id);
    }

    @Override
    public List<EquipmentInfo> connectSort(Long userId) {
        return baseMapper.connectSort(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R bind(EquipmentInfo equipmentInfo) {
        log.info("绑定设备信息:{}", JSONObject.toJSONString(equipmentInfo));
        if (StrUtil.isEmpty(equipmentInfo.getName())) {
            log.info("设备名称为空");
            return R.fail(ResponseConstant.BINDING_FAIL);
        }
        Long userId = SessionUtil.getId();
        String lockKey = RedisKeyConstant.EQUIPMENT_ADD.replace("${userId}", userId.toString()).replace("${equipmentId}", equipmentInfo.getName());
        boolean lockAcquired = Boolean.TRUE.equals(
                redisTemplate.opsForValue().setIfAbsent(lockKey,
                        "1", Duration.ofMillis(10000)));
        if (!lockAcquired) {
            log.info("正在绑定中");
            return R.fail(ResponseConstant.BINDING_PROGRESS);
        }

        try {
            equipmentInfo.setCreateBy(userId);
            equipmentInfo.setUserBy(userId);
            LocalDateTime now = LocalDateTime.now();

            //判断该设备名称是否在该用户的数据中存在
            EquipmentInfo equipmentBindDetail = getBindingOne(equipmentInfo, null, userId);

            if (null != equipmentBindDetail && ObjectUtil.isNull(equipmentInfo.getOneLevelTypeId())) {
                equipmentInfo.setOneLevelTypeId(equipmentBindDetail.getOneLevelTypeId());
            }
            //去数据库中查出对应型号数据
            List<EquipmentType> equipmentTypes;
            if (isUseModelId(equipmentInfo)) {
                equipmentTypes = List.of(equipmentTypeService.getById(equipmentInfo.getTwoLevelTypeId()));
            } else {
                //前缀就是数据的信息
                equipmentTypes = equipmentTypeService.getModel(equipmentInfo.getName(), equipmentInfo.getOneLevelTypeId());
            }

            if (CollUtil.isEmpty(equipmentTypes)) {
                log.info("equipmentTypes 为null");
                return R.fail(ResponseConstant.BINDING_SUPPORTED);
            }
            //柏群如果不传递一级类型id会查询出多条信息，取第一条信息，正常情况只会存在一条信息
            EquipmentType equipmentType = equipmentTypes.get(0);
            if (null != equipmentType.getId()) {
                equipmentInfo.setTwoLevelTypeId(equipmentType.getId());
            }

            if (StrUtil.isNotBlank(equipmentInfo.getRemark())) {
                equipmentType = equipmentTypeService.equipmentTypeChange(equipmentType, JSONObject.parseObject(equipmentInfo.getRemark()).getString(EquipmentConstant.MERIT_EIGEN_VALUE), equipmentInfo.getName());
            }
            //一级类型id
            if (equipmentType.getParentId() == null) {
                log.info("equipmentType.getParentId() 为null");
                return R.fail(ResponseConstant.BINDING_SUPPORTED);
            }
            equipmentInfo.setOneLevelTypeId(equipmentType.getParentId());

            setEquiomentInfoProperty(equipmentInfo, equipmentType.getEigenValue());
            //查询ota类型
            List<FirmwareVersion> firmwareversionlist = getFirmwareVersions(equipmentInfo.getRemark(), equipmentType.getId());
            if (equipmentBindDetail == null) {
                return createEquipInfo(equipmentInfo, userId, now, equipmentType, firmwareversionlist);
            }
            return updateEquipInfo(equipmentInfo, now, equipmentBindDetail, equipmentType, firmwareversionlist);
        } catch (Exception e) {
            log.error("绑定失败 {}", e.getMessage(), e);
            throw new BusinessException(ResponseConstant.BINDING_FAIL);
        } finally {
            redisTemplate.delete(lockKey);
        }
    }

    private void setEquiomentInfoProperty(EquipmentInfo equipmentInfo, Integer eigenValue) {
        if (StrUtil.isBlank(equipmentInfo.getCharacteristic())) {
            return;
        }
        List<SetUniqueModelIdentify> setUniqueModelIdentifies = JSONArray.parseArray(equipmentInfo.getCharacteristic(), SetUniqueModelIdentify.class);

        JSONObject remark = new JSONObject();
        boolean isSetEigenValue = !Objects.isNull(eigenValue);
        boolean isSetRemark = StrUtil.isEmpty(equipmentInfo.getRemark());
        for (SetUniqueModelIdentify setUniqueModelIdentify : setUniqueModelIdentifies) {
            if (isSetEigenValue && (setUniqueModelIdentify.getService().contains(FirmwareCharacteristcConstant.Firmware_180a) || setUniqueModelIdentify.getService().contains(FirmwareCharacteristcConstant.Firmware_180A))) {
                if (FirmwareCharacteristicEnum.CHARACTERISTIC_2A26.getCode().equals(eigenValue) &&
                        (setUniqueModelIdentify.getCharacteristicProperties().contains(FirmwareCharacteristcConstant.Firmware_2a26)
                                || setUniqueModelIdentify.getCharacteristicProperties().contains(FirmwareCharacteristcConstant.Firmware_2A26))) {
                    equipmentInfo.setUserVersion(setUniqueModelIdentify.getCharacteristicValue());
                } else if (FirmwareCharacteristicEnum.CHARACTERISTIC_2A28.getCode().equals(eigenValue) &&
                        (setUniqueModelIdentify.getCharacteristicProperties().contains(FirmwareCharacteristcConstant.Firmware_2a28)
                                || setUniqueModelIdentify.getCharacteristicProperties().contains(FirmwareCharacteristcConstant.Firmware_2A28))) {
                    equipmentInfo.setUserVersion(setUniqueModelIdentify.getCharacteristicValue());
                }
            }

            if (isSetRemark) {
                remark.put(setUniqueModelIdentify.getCharacteristicProperties(), setUniqueModelIdentify.getCharacteristicValue());
            }
        }
        if (isSetRemark && !remark.isEmpty()) {
            equipmentInfo.setRemark(remark.toJSONString());
        }
    }

    @Override
    public void cacheLatestConnectionsModel(EquipmentInfo equipmentInfo, Integer expirTime) {
        try {
            String key = String.format(RedisKeyConstant.EQUIPMENT_MODEL_LATEST, equipmentInfo.getUserBy(), equipmentInfo.getTwoLevelTypeId());
            redisTemplate.opsForValue().set(key, JSONObject.toJSONString(equipmentInfo), Duration.ofMinutes(expirTime));
        } catch (Exception e) {
            log.error("bind -> cacheLatestConnectionsModel error", e);
        }
    }

    @Override
    public BindDeviceDTO doubleWrite(EquipmentType equipmentType, EquipmentInfo equipmentInfo) {
        try {
            BindDeviceCmd bindDeviceCmd = new BindDeviceCmd();
            bindDeviceCmd.setServerCharacteristic(equipmentInfo.getRemark());
            bindDeviceCmd.setCharacteristic(equipmentInfo.getCharacteristic());
            bindDeviceCmd.setUserId(equipmentInfo.getUserBy());
            bindDeviceCmd.setMac(equipmentInfo.getCode());
            bindDeviceCmd.setFirmwareVersion(equipmentInfo.getFirmwareVersion());
            bindDeviceCmd.setBluetoothName(equipmentInfo.getName());
            if (equipmentInfo.getBluetoothAlias() != null) {
                bindDeviceCmd.setDeviceAlias(equipmentInfo.getBluetoothAlias());
            }
            bindDeviceCmd.setModelId(equipmentInfo.getTwoLevelTypeId());
            bindDeviceCmd.setProductId(equipmentInfo.getOneLevelTypeId());
            bindDeviceCmd.setDeviceUserRelId(equipmentInfo.getId());
            bindDeviceCmd.setCode(equipmentType.getCode());
            bindDeviceCmd.setBluetoothPrefix(equipmentType.getPrefixId());
            bindDeviceCmd.setSnCode(equipmentInfo.getSnCode());

            return deviceApi.bindDevice(bindDeviceCmd);
        } catch (Exception e) {
            log.warn("绑定写入device失败 equipmentInfo：{}", JSONObject.toJSONString(equipmentInfo));
        }
        return null;
    }

    @Override
    public void migrationEquipInfoToDevice(Long prouctId, Long productModelId, Integer isTest) {
        List<EquipmentInfo> equipmentInfoList = listEquipmentInfos(prouctId, productModelId, isTest, null);
        if (CollUtil.isEmpty(equipmentInfoList)) {
            return;
        }
        List<EquipmentInfo> needCreateDevices = new ArrayList<>();
        List<EquipmentInfo> otherInfos = equipmentInfoList.stream().filter(v -> !v.getOneLevelTypeId().equals(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_41)).collect(
                collectingAndThen(toCollection(() -> new TreeSet<>(Comparator.comparing(EquipmentInfo::getName))), ArrayList::new)
        );
        List<EquipmentInfo> bodyFatScales = equipmentInfoList.stream().filter(v -> v.getOneLevelTypeId().equals(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_41) && null != v.getCode()).collect(
                collectingAndThen(toCollection(() -> new TreeSet<>(Comparator.comparing(EquipmentInfo::getCode))), ArrayList::new)
        );

        if (CollUtil.isNotEmpty(otherInfos)) {
            needCreateDevices.addAll(otherInfos);
        }
        if (CollUtil.isNotEmpty(bodyFatScales)) {
            needCreateDevices.addAll(bodyFatScales);
        }
        CollUtil.split(Objects.requireNonNull(needCreateDevices).stream().map(this::getMigrateDeviceBindCmd).collect(Collectors.toList()), 100).
                forEach(v -> CompletableFuture.runAsync(() -> {
                    // 新增目标活动分数
                    deviceApi.migrateDevice(v);
                }, threadPoolTaskExecutor).join());
        log.info("产品型号:{} 设备插入完毕", prouctId);
    }

    @Override
    public void migrationBindingToDevice(Long prouctId, Long productModelId, Integer isTest, List<Long> relIds) {
        CollUtil.split(Objects.requireNonNull(listEquipmentInfos(prouctId, productModelId, isTest, relIds)).stream().map(this::getMigrateDeviceBindCmd).collect(Collectors.toList()), 100).
                forEach(v -> CompletableFuture.runAsync(() -> {
                    // 新增目标活动分数
                    deviceApi.migrateDeviceBindInfo(v);
                }, threadPoolTaskExecutor).join());
        log.info("产品型号:{} 设备绑定信息插入完毕", prouctId);
    }

    @Override
    public Long getUserFirstEquipByTypeIds(List<Long> typeIds) {

        QueryWrapper<EquipmentInfo> queryWrap = new QueryWrapper<>();
        queryWrap.eq("user_by", SessionUtil.getId());
        queryWrap.in("one_level_type_id", typeIds);
        List<EquipmentInfo> list = this.list(queryWrap);
        if (list == null || list.isEmpty()) {
            return null;
        }
        Comparator<EquipmentInfo> comparator = Comparator.comparing(EquipmentInfo::getCreateTime);
        return list.stream().min(comparator).get().getOneLevelTypeId();
    }

    @Override
    public EquipmentInfo getLastConnectModelId(Long userId, Long equipTypeId) {
        if (null == userId) {
            return null;
        }
        LambdaQueryWrapper<EquipmentInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(EquipmentInfo::getBindStatus, BaseConstant.INT_TRUE).eq(EquipmentInfo::getUserBy, userId);
        if (null != equipTypeId) {
            lambdaQueryWrapper.eq(EquipmentInfo::getOneLevelTypeId, equipTypeId);
        }
        lambdaQueryWrapper.orderByDesc(EquipmentInfo::getConnectTime).last("limit 1");

        return baseMapper.selectOne(lambdaQueryWrapper);
    }

    private List<EquipmentInfo> listEquipmentInfos(Long prouctId, Long productModelId, Integer isTest, List<Long> relIds) {
        LambdaQueryWrapper<EquipmentInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(null != prouctId, EquipmentInfo::getOneLevelTypeId, prouctId);
        queryWrapper.eq(null != productModelId, EquipmentInfo::getTwoLevelTypeId, productModelId);
        queryWrapper.eq(BaseConstant.INT_TRUE == isTest, EquipmentInfo::getUserBy, SessionUtil.getId());
        queryWrapper.in(CollUtil.isNotEmpty(relIds), EquipmentInfo::getId, relIds);
        return baseMapper.selectList(queryWrapper);
    }

    private MigrateDeviceBindCmd getMigrateDeviceBindCmd(EquipmentInfo info) {
        MigrateDeviceBindCmd migrateDevice = BeanUtil.copyProperties(info, MigrateDeviceBindCmd.class);
        migrateDevice.setMac(info.getCode());
        migrateDevice.setUserId(info.getUserBy());
        migrateDevice.setCategoryId(info.getOneLevelTypeId());
        migrateDevice.setModelId(info.getTwoLevelTypeId());
        return migrateDevice;
    }

    private List<FirmwareVersion> getFirmwareVersions(String remark, Long id) {
        BaseQuery<FirmwareVersion> baseQuery = new BaseQuery<>();
        baseQuery.eq("equip_model_id", id);
        if (StrUtil.isNotBlank(remark)) {
            JSONObject jsonObject = JSONObject.parseObject(remark);
            String agreementEigenValue = jsonObject.getString("2A24");
            if (StrUtil.isNotBlank(agreementEigenValue)) {
                baseQuery.eq("code", agreementEigenValue);
            }
        }
        return firmwareVersionService.list(baseQuery);
    }

    private R createEquipInfo(EquipmentInfo equipmentInfo, Long userId, LocalDateTime now, EquipmentType model, List<FirmwareVersion> firmwareversionlist) {
        if (equipmentInfo.getOneLevelTypeId().equals(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_100)) {
            DeviceQry deviceQry = new DeviceQry();
            deviceQry.setUserId(userId);
            deviceQry.setProductId(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_100);
            DeviceDTO latestDevice = deviceApi.getLatestDevice(deviceQry);
            if (null != latestDevice && null != latestDevice.getDeviceId()) {
                return R.fail("心率设备只能绑定一台，请勿重复绑定");
            }
        }

        //绑定状态为1
        equipmentInfo.setBindStatus(1);
        equipmentInfo.setUpdateTime(now);
        equipmentInfo.setConnectTime(now);
        equipmentInfo.setBindTime(now);
        if (model.getCommunicationProtocol() != null) {
            equipmentInfo.setCommunicationProtocol(model.getCommunicationProtocol());
        }
        if (model.getEigenValue() != null) {
            equipmentInfo.setEigenValue(model.getEigenValue());
        }
        equipmentInfo.setTwoLevelImage(model.getTypeImages());
        FirmwareVersion firmwareVersion = null;
        if (CollUtil.isNotEmpty(firmwareversionlist)) {
            firmwareVersion = firmwareversionlist.get(0);
            equipmentInfo.setOtaType(firmwareVersion.getOtaType());
        }
        int count = count("user_by", ConditionEnum.EQ, userId);
        Integer autoReward = equipmentInfo.getAutoReward();
        int insert;
        try {
            insert = baseMapper.insert(equipmentInfo);
        } catch (Exception e) {
            /**
             * todo 待优化，不应该触发绑定 （同一个人切换不同账号连接设备时）
             * @see com.mrk.yudong.app.api.device.controller.DeviceUserController#connectionDevice
             * @see deviceUserRelId relOriginId
             */
            Throwable rootCause = ExceptionUtils.getRootCause(e);
            if (rootCause instanceof java.sql.SQLIntegrityConstraintViolationException) {
                log.warn("Failed to insert equipment_info. {}", rootCause.getMessage());
            } else {
                log.error("Failed to insert equipment_info. {}", e.getMessage(), e);
            }
            return R.fail(ResponseConstant.BINDING_FAIL);
        }

        equipmentInfo = equipmentInfoMapper.selectById(equipmentInfo.getId());
        boolean saveStatus = insert > 0;
        if (saveStatus) {
            //组装返回数据
            UpdateEquipVO updateEquip = getUpdateEquipVO(model, firmwareVersion);
            updateEquip.setIsBind(BaseConstant.INT_TRUE);
            updateEquip.setOneLevelTypeId(model.getParentId());
            updateEquip.setTwoLevelTypeId(model.getId());
            updateEquip.setId(equipmentInfo.getId());
            equipmentInfo.setOneLevelTypeId(model.getParentId());
            equipmentInfo.setTwoLevelTypeId(model.getId());
            equipmentInfo.setTwoLevelTypeName(model.getTypeName());
            updateEquip.setTwoLevelName(model.getTypeName());
            EquipmentType product = equipmentTypeService.getById(model.getParentId());
            updateEquip.setProductName(product.getTypeName());
            updateEquip.setProductType(product.getType());
            updateEquip.setHelpCenter(model.getHelpCenter());
            updateEquip.setProductManual(model.getProductManual());
            updateEquip.setIsMerit(model.getIsElectromagneticControl());
            updateEquip.setFirmwareVersion(equipmentInfo.getUserVersion());
            updateEquip.setName(equipmentInfo.getName());
            updateEquip.setIsOta(model.getIsOta());
            BindDeviceDTO bindDeviceDTO = doubleWrite(model, equipmentInfo);
            if (null == bindDeviceDTO) {
                log.warn("用户:{}双写失败", SessionUtil.getId());
                throw new BusinessException(ResponseConstant.BINDING_FAIL);
            }
            updateEquip.setProductKey(bindDeviceDTO.getProductKey());
            updateEquip.setDeviceName(bindDeviceDTO.getDeviceName());
            updateEquip.setDeviceSecret(bindDeviceDTO.getDeviceSecret());
            updateEquip.setFeatureDescription(bindDeviceDTO.getFeatureDescription());
            updateEquip.setOtaType(bindDeviceDTO.getOtaType());
            updateEquip.setCommunicationProtocol(bindDeviceDTO.getCommunicationProtocol());
            updateEquip.setCommunicationType(bindDeviceDTO.getCommunicationType());
            updateEquip.setEigenValue(bindDeviceDTO.getEigenValue());
            updateEquip.setIsMerit(bindDeviceDTO.getIsMerit());
            updateEquip.setElectrodeType(bindDeviceDTO.getElectrodeType());
            updateEquip.setSnCode(bindDeviceDTO.getSnCode());

            try {
                //首次绑定
                if (count == 0) {
                    //赠送十天会员
                    if (Objects.isNull(autoReward) || autoReward.equals(BaseConstant.INT_TRUE)) {
                        openVip(equipmentInfo.getTerminal(), userId);
                    }
                    redisTemplate.opsForValue().set(RedisKeyConstant.USER_GUIDE_MEMBER_BANNER + userId, "0");
                    updateEquip.setOperationRecordStatus(ConnectionOperationEnum.FIRST_BINDING.getCode());
                }
                //上传任务数据
                uploadTaskData(equipmentInfo, userId, now);
                cacheLatestConnectionsModel(equipmentInfo, 60);
            } catch (Exception e) {
                log.warn("用户:{}生成数据失败", SessionUtil.getId());
                return R.fail(ResponseConstant.BINDING_FAIL);
            }
            return R.ok(updateEquip);
        } else {
            return R.fail(ResponseConstant.BINDING_FAIL);
        }
    }

    private void openVip(Integer terminal, Long userId) {
        OpenVipCmd openVipCmd = new OpenVipCmd()
                .setUserId(userId)
                .setType(VipFlowTypeEnum.NEWBIE.getCode())
                .setTitle(VipFlowTypeEnum.NEWBIE.getDesc())
                .setPackageType(null)
                .setVipType(VipTypeEnum.VIP.getCode())
                .setOperationType(VipFlowOperationTypeEnum.OPEN.getCode())
                .setDays(10)
                .setTerminal(terminal)
                .setBizId(null)
                .setSubAccount(true);
        memberApi.openVip(openVipCmd);
    }

    private R updateEquipInfo(EquipmentInfo equipmentInfo, LocalDateTime now, EquipmentInfo binding, EquipmentType model, List<FirmwareVersion> firmwareversionlist) {
        int isBind = binding.getBindStatus() == 0 ? 1 : 0;
        if (StrUtil.isNotBlank(equipmentInfo.getCode())) {
            binding.setCode(equipmentInfo.getCode());
        }
        binding.setBindStatus(1);
        binding.setTwoLevelTypeId(model.getId());
        binding.setOneLevelTypeId(model.getParentId());
        if (model.getCommunicationProtocol() != null) {
            binding.setCommunicationProtocol(model.getCommunicationProtocol());
        }
        if (model.getEigenValue() != null) {
            binding.setEigenValue(model.getEigenValue());
        }
        FirmwareVersion firmwareVersion = null;
        if (CollUtil.isNotEmpty(firmwareversionlist)) {
            firmwareVersion = firmwareversionlist.get(0);
            binding.setOtaType(firmwareVersion.getOtaType());
        }
        binding.setTwoLevelImage(model.getTypeImages());
        if (StrUtil.isNotBlank(equipmentInfo.getRemark())) {
            binding.setRemark(equipmentInfo.getRemark());
        }
        if (StrUtil.isNotBlank(equipmentInfo.getFirmwareVersion())) {
            binding.setFirmwareVersion(equipmentInfo.getFirmwareVersion());
        }
        binding.setConnectTime(now);
        binding.setUserVersion(equipmentInfo.getUserVersion());
        binding.setBindTime(now);
        binding.setSnCode(equipmentInfo.getSnCode());

        int updateNum = baseMapper.updateById(binding);
        boolean updateStatus = updateNum > 0;
        if (updateStatus) {
            UpdateEquipVO updateEquip = getUpdateEquipVO(model, firmwareVersion);
            updateEquip.setIsBind(isBind);
            updateEquip.setOneLevelTypeId(binding.getOneLevelTypeId());
            updateEquip.setTwoLevelTypeId(binding.getTwoLevelTypeId());
            equipmentInfo.setTwoLevelTypeName(model.getTypeName());
            updateEquip.setTwoLevelName(model.getTypeName());
            updateEquip.setId(binding.getId());
            updateEquip.setName(binding.getName());
            EquipmentType product = equipmentTypeService.getById(binding.getOneLevelTypeId());
            updateEquip.setProductName(product.getTypeName());
            updateEquip.setProductType(product.getType());
            updateEquip.setHelpCenter(model.getHelpCenter());
            updateEquip.setProductManual(model.getProductManual());
            updateEquip.setIsMerit(model.getIsElectromagneticControl());
            updateEquip.setIsOta(model.getIsOta());
            updateEquip.setFirmwareVersion(binding.getUserVersion());
            updateEquip.setBluetoothAlias(binding.getBluetoothAlias());
            updateEquip.setOperationRecordStatus(ConnectionOperationEnum.CONNECT.getCode());
            if (StrUtil.isNotBlank(equipmentInfo.getCharacteristic())) {
                binding.setCharacteristic(equipmentInfo.getCharacteristic());
            }
            BindDeviceDTO bindDeviceDTO = doubleWrite(model, binding);
            if (null == bindDeviceDTO) {
                log.warn("用户:{}双写失败", SessionUtil.getId());
                throw new BusinessException(ResponseConstant.BINDING_FAIL);
            }
            updateEquip.setProductKey(bindDeviceDTO.getProductKey());
            updateEquip.setDeviceName(bindDeviceDTO.getDeviceName());
            updateEquip.setDeviceSecret(bindDeviceDTO.getDeviceSecret());
            updateEquip.setDeviceId(bindDeviceDTO.getDeviceId());
            updateEquip.setFeatureDescription(bindDeviceDTO.getFeatureDescription());
            updateEquip.setOtaType(bindDeviceDTO.getOtaType());
            updateEquip.setCommunicationProtocol(bindDeviceDTO.getCommunicationProtocol());
            updateEquip.setCommunicationType(bindDeviceDTO.getCommunicationType());
            updateEquip.setEigenValue(bindDeviceDTO.getEigenValue());
            updateEquip.setIsMerit(bindDeviceDTO.getIsMerit());
            updateEquip.setElectrodeType(bindDeviceDTO.getElectrodeType());
            updateEquip.setSnCode(bindDeviceDTO.getSnCode());

            cacheLatestConnectionsModel(equipmentInfo, 60);
            return R.ok(updateEquip);
        } else {
            return R.fail(ResponseConstant.BINDING_FAIL);
        }
    }

    @Override
    public Long getUserLastEquipIdByType(Long equipType) {

        QueryWrapper<EquipmentInfo> queryWrap = new QueryWrapper<>();
        queryWrap.eq("user_by", SessionUtil.getId());
        queryWrap.eq("one_level_type_id", equipType);
        queryWrap.eq("bind_status", 1);
        List<EquipmentInfo> list = this.list(queryWrap);
        if (list == null || list.isEmpty()) {
            return null;
        }
        Comparator<EquipmentInfo> comparator = Comparator.comparing(EquipmentInfo::getCreateTime);
        return list.stream().max(comparator).get().getId();
    }

    @Override
    public Long getUserLastEquipIdByTypes(List<Long> equipTypes) {
        QueryWrapper<EquipmentInfo> queryWrap = new QueryWrapper<>();
        queryWrap.eq("user_by", SessionUtil.getId());
        queryWrap.in("one_level_type_id", equipTypes);
        queryWrap.eq("bind_status", 1);
        List<EquipmentInfo> list = this.list(queryWrap);
        if (list == null || list.isEmpty()) {
            return null;
        }
        Comparator<EquipmentInfo> comparator = Comparator.comparing(EquipmentInfo::getCreateTime);
        return list.stream().max(comparator).get().getOneLevelTypeId();
    }

    @Override
    public String getProductGroup(Long userId) {
        LambdaQueryWrapper<EquipmentInfo> baseQuery = new LambdaQueryWrapper<>();
        baseQuery.eq(EquipmentInfo::getUserBy, userId).eq(EquipmentInfo::getBindStatus, 1).in(EquipmentInfo::getOneLevelTypeId, List.of(1, 2, 5, 6));
        List<EquipmentInfo> equipmentInfos = list(baseQuery);
        if (CollUtil.isEmpty(equipmentInfos)) {
            return "";
        }
        Map<Long, String> product = ProductEnum.getProduct();
        Map<Long, List<EquipmentInfo>> infos = equipmentInfos.stream().collect(Collectors.groupingBy(EquipmentInfo::getOneLevelTypeId, Collectors.toList()));
        Map<String, String> map = new HashMap<>();

        infos.forEach((k, v) -> {
            map.put(product.get(k), JSONObject.toJSONString(v.stream().map(EquipmentInfo::getName).collect(Collectors.toList())));
        });

        return map.toString();
    }

    @Override
    public UserDeviceDTO getDeviceByInfoId(Long infoId) {
        UserDeviceQry deviceQry = new UserDeviceQry();
        deviceQry.setUserId(SessionUtil.getId());
        deviceQry.setRelOriginId(infoId);
        PageDTO<UserDeviceDTO> myDevices = deviceApi.getMyDevices(deviceQry);
        List<UserDeviceDTO> records = myDevices.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            return records.get(0);
        }
        return null;
    }

    @Override
    public List<EquipmentInfo> findEquipmentByProductId(Long productId, Long userId) {
        LambdaQueryWrapper<EquipmentInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EquipmentInfo::getOneLevelTypeId, productId);
        queryWrapper.eq(EquipmentInfo::getBindStatus, BaseConstant.INT_TRUE);
        queryWrapper.eq(EquipmentInfo::getUserBy, userId);
        return list(queryWrapper);
    }

    /**
     * 根据条件获取设备信息
     *
     * @param equipInfoDTO
     * @return
     */
    @Override
    public EquipmentInfo getEquipmentInfoByConditions(EquipInfoDTO equipInfoDTO) {

        return equipmentInfoMapper.getByConditions(equipInfoDTO.getUserId(), equipInfoDTO.getOneLevelTypeId(), equipInfoDTO.getTwoLevelTypeId(), equipInfoDTO.getName());
    }

    @Override
    public Boolean disconnectionDevice(Long id) {
        EquipmentInfo equipmentInfo = new EquipmentInfo();
        equipmentInfo.setBindStatus(BaseConstant.INT_FALSE);
        equipmentInfo.setId(id);
        boolean updateStatus = updateById(equipmentInfo);
        log.info("update info->id:{} result:{}", id, updateStatus);
        if (updateStatus) {
            UnbindDeviceCmd unbindDevice = new UnbindDeviceCmd();
            unbindDevice.setDeviceUserRelId(id);
            Boolean isUnbind = deviceApi.unbindDevice(unbindDevice);
            log.info("update device>id:{} result:{}", id, isUnbind);
            return isUnbind;
        }
        return Boolean.FALSE;
    }

    @Override
    public EquipmentInfo getByName(String name) {
        LambdaQueryWrapper<EquipmentInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EquipmentInfo::getName, name);
        queryWrapper.eq(EquipmentInfo::getBindStatus, BaseConstant.INT_TRUE);
        queryWrapper.eq(EquipmentInfo::getUserBy, SessionUtil.getId());
        queryWrapper.last("limit 1");
        return getOne(queryWrapper);
    }

    public EquipmentInfo getBindingOne(EquipmentInfo equipmentInfo, Integer bindStatus, Long userId) {
        BaseQuery<EquipmentInfo> baseQuery = new BaseQuery<>();
        if (bindStatus != null) {
            baseQuery.eq("bind_status", bindStatus);
        }
        baseQuery.eq("name", equipmentInfo.getName()).eq("user_by", userId);
        List<EquipmentInfo> list = baseMapper.selectList(baseQuery);
        if (list.size() > 1) {
            List<EquipmentInfo> sorted = list.stream().sorted(Comparator.comparing(EquipmentInfo::getCreateTime)).collect(Collectors.toList());
            baseMapper.deleteById(sorted.get(0).getId());
            return list.get(0);
        } else if (list.size() == 1) {
            return list.get(0);
        }
        return null;
    }

    private void uploadTaskData(EquipmentInfo equipmentInfo, Long userId, LocalDateTime now) {
        TaskForm taskForm = new TaskForm();
        taskForm.setEquipmentType(equipmentInfo.getOneLevelTypeId());
        taskForm.setType(TaskStrategyEnum.BIND_EQUIP.getType());
        taskForm.setNow(now);
        taskForm.setBusinessValue(equipmentInfo.getId().toString());
        taskForm.setUserId(userId);
        userTaskApi.uploadTaskData(taskForm);
    }

    private UpdateEquipVO getUpdateEquipVO(EquipmentType equipmentType, FirmwareVersion firmwareVersion) {
        UpdateEquipVO updateEquip = new UpdateEquipVO();
        updateEquip.setEigenValue(equipmentType.getEigenValue());
        updateEquip.setCommunicationProtocol(equipmentType.getCommunicationProtocol());
        updateEquip.setTwoLevelImage(equipmentType.getTypeImages());
        if (firmwareVersion != null) {
            updateEquip.setOtaType(firmwareVersion.getOtaType());
        }
        return updateEquip;
    }

    private boolean isUseModelId(EquipmentInfo equipmentInfo) {
        return equipmentInfo.getTwoLevelTypeId() != null
                && (ProductEnum.BODY_SCALE.getId().equals(equipmentInfo.getTwoLevelTypeId())
                || ProductEnum.HEART_RATE_MONITOR.getId().equals(equipmentInfo.getTwoLevelTypeId()));
    }

    @Override
    public List<NoviceTutorialEquipTypeDTO> getNoviceTutorial() {
        EquipmentInfo equipmentInfo = equipmentInfoMapper.getNewestBind(SessionUtil.getId());
        if (Objects.isNull(equipmentInfo)) {
            return buildNoviceTutorialEquipType();
        }

        List<NoviceTutorialEquipTypeDTO> equipTypeDTOS = buildNoviceTutorialEquipType();
        List<NoviceTutorialEquipTypeDTO> newestBindEquipType = equipTypeDTOS.stream()
                .filter(dto -> Objects.equals(dto.getEquipTypeId(), equipmentInfo.getOneLevelTypeId()))
                .collect(Collectors.toList());

        equipTypeDTOS.removeAll(newestBindEquipType);
        equipTypeDTOS.addAll(0, newestBindEquipType);

        return equipTypeDTOS;
    }

    private List<NoviceTutorialEquipTypeDTO> buildNoviceTutorialEquipType() {
        List<NoviceTutorialEquipTypeDTO> list = new LinkedList<>();
        list.add(new NoviceTutorialEquipTypeDTO(ProductEnum.INDOOR_BIKE.getId(), ProductEnum.INDOOR_BIKE.getDesc(), noviceTutorialProperties.getCourseThemeIdList().get(0)));
        list.add(new NoviceTutorialEquipTypeDTO(ProductEnum.CROSS_TRAINER.getId(), ProductEnum.CROSS_TRAINER.getDesc(), noviceTutorialProperties.getCourseThemeIdList().get(1)));
        list.add(new NoviceTutorialEquipTypeDTO(ProductEnum.TREADMILL.getId(), ProductEnum.TREADMILL.getDesc(), noviceTutorialProperties.getCourseThemeIdList().get(2)));
        list.add(new NoviceTutorialEquipTypeDTO(ProductEnum.ROWER.getId(), ProductEnum.ROWER.getDesc(), noviceTutorialProperties.getCourseThemeIdList().get(3)));
        return list;
    }
}
