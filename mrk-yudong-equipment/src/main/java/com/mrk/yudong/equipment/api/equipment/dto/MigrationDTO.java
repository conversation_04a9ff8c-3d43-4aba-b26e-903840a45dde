package com.mrk.yudong.equipment.api.equipment.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import lombok.Data;

import java.util.List;

@Data
public class MigrationDTO {
    private Long prouctId;
    private Long productModelId;
    private Integer isTest;
    @JSONField(serializeUsing = ToStringSerializer.class)
    private List<Long> relIds;
}
