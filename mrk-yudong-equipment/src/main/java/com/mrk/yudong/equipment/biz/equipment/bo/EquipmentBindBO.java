package com.mrk.yudong.equipment.biz.equipment.bo;

import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

@Data
public class EquipmentBindBO {

    private Long equipmentId;

    private String name;

    @NotNull(message = "请选择操作类型")
    @Range(min = BaseConstant.INT_FALSE, max = BaseConstant.INT_TRUE, message = "操作类型不合法")
    private Integer operation;

    private Long bindId;

    private String mac;

}
