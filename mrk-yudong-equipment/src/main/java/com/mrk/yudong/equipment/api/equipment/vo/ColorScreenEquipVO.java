package com.mrk.yudong.equipment.api.equipment.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 彩屏端设备信息
 * <AUTHOR>
 * @create 2022?05-26 11:14
 */
@AllArgsConstructor
@Data
public class ColorScreenEquipVO {

    private static final long serialVersionUID = 1L;
    /**
     * 分类id
     */
    private Long equipTypeId;

    /**
     * 型号Id
     */
    private Long equipModelId;

    /**
     * 型号名称
     */
    private String equipModelName;

    /**
     * 型号图片
     */
    private String equipModelImage;

    /**
     * 最高速度
     */
    private Integer maxSpeed;

    /**
     * 最高坡度
     */
    private Integer maxSlope;
    /**
     * 最高坡度
     */
    private Integer maxResistance;
}
