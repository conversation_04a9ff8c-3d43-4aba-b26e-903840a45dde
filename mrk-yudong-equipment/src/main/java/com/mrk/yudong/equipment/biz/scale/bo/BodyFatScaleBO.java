package com.mrk.yudong.equipment.biz.scale.bo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:
 * @author: ljx
 * @create: 2023/8/3 15:46
 * @Version 1.0
 **/
@Data
public class BodyFatScaleBO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 业务ID
     */
    private Long bizId;

    /**
     * 型号ID
     */
    private Long modelId;

    /**
     * 电极类型 1-4电极，2-8电极
     */
    private Integer electrodeType;

    /**
     * 体重
     */
    private BigDecimal weight;

    /**
     * 身高
     */
    private BigDecimal height;

    /**
     * 身体得分（健康评分）
     */
    private Integer score;

    /**
     * BMI
     */
    private BigDecimal bmi;

    /**
     * 体型（身体类型）
     */
    private String bodyType;

    /**
     * 肌肉（肌肉量）
     */
    private BigDecimal muscle;

    /**
     * 去脂体重
     */
    private BigDecimal leanBodyMass;

    /**
     * 体脂率（脂肪率）
     */
    private BigDecimal bodyFatRate;

    /**
     * 皮下脂肪率
     */
    private BigDecimal subcutaneousFat;

    /**
     * 内脏脂肪（内脏脂肪等级）
     */
    private BigDecimal visceralFat;

    /**
     * 基础代谢
     */
    private BigDecimal basalMetabolism;

    /**
     * 骨骼肌量
     */
    private BigDecimal skeletalMuscleRatio;

    /**
     * 蛋白率（蛋白质率）
     */
    private BigDecimal protein;

    /**
     * 水分（体水分率）
     */
    private BigDecimal waterContent;

    /**
     * 身体年龄
     */
    private Integer bodyAge;

    /**
     * 心率
     */
    private BigDecimal heartRate;

    /**
     * 身体形态 （字段暂无使用）
     */
    private String bodyShape;

    /**
     * 骨量
     */
    private BigDecimal boneMass;

    /**
     * 左上肢脂肪
     */
    private BigDecimal leftArmFat;

    /**
     * 右上肢脂肪
     */
    private BigDecimal rightArmFat;

    /**
     * 左下肢脂肪
     */
    private BigDecimal leftLegFat;

    /**
     * 右下肢脂肪
     */
    private BigDecimal rightLegFat;

    /**
     * 左上肢骨骼肌
     */
    private BigDecimal leftArmSkeletalMuscle;

    /**
     * 右上肢骨骼肌
     */
    private BigDecimal rigthArmSkeletalMuscle;

    /**
     * 左下肢骨骼肌
     */
    private BigDecimal leftLegSkeletalMuscle;

    /**
     * 右下肢骨骼肌
     */
    private BigDecimal righLegSkeletalMuscle;

    /**
     * 四肢骨骼肌质量指数
     */
    private BigDecimal limbsSkeletalMuscle;

    /**
     * 脂肪均衡
     */
    private BigDecimal fatBalance;

    /**
     * 肌肉均衡
     */
    private BigDecimal muscleBalance;

    /**
     * 推测腰臀比
     */
    private BigDecimal waistHipRate;

    /**
     * 躯干脂肪量
     */
    private BigDecimal allBodyFat;


    /**
     * 体脂量（脂肪量）
     */
    private BigDecimal bodyFatMass;

    /**
     * 肌肉率
     */
    private BigDecimal muscleRate;

    /**
     * 建议卡路里摄入
     */
    private BigDecimal suggestCalorieIntake;

    /**
     * 肥胖水平（肥胖度）
     */
    private BigDecimal obesityLevel;

    /**
     * 理想体重
     */
    private BigDecimal idealBodyWeight;

    /**
     * 控制体重
     */
    private BigDecimal controlBodyWeight;

    /**
     * 水分量（体水分量）
     */
    private BigDecimal waterMass;

    /**
     * 蛋白量（蛋白质量）
     */
    private BigDecimal proteinMass;

    /**
     * 无机盐量
     */
    private BigDecimal inorganicSaltMass;

    /**
     * 身体细胞量
     */
    private BigDecimal bodyCellMass;

    /**
     * 细胞外水量
     */
    private BigDecimal extracellularWaterVolume;

    /**
     * 细胞内水量
     */
    private BigDecimal intracellularWaterVolume;

    /**
     * 皮下脂肪量
     */
    private BigDecimal subcutaneousFatMass;

    /**
     * 骨骼肌率
     */
    private BigDecimal skeletalMuscleRate;

    /**
     * 骨骼肌质量指数
     */
    private BigDecimal skeletalMuscleMassExponent;

    /**
     * 脂肪控制量
     */
    private BigDecimal fatControl;

    /**
     * 肌肉控制量
     */
    private BigDecimal muscleControl;

    /**
     * 左上肢脂肪率
     */
    private BigDecimal leftArmFatRate;

    /**
     * 右上肢脂肪率
     */
    private BigDecimal rightArmFatRate;

    /**
     * 左下肢脂肪率
     */
    private BigDecimal leftLegFatRate;

    /**
     * 右下肢脂肪率
     */
    private BigDecimal rightLegFatRate;

    /**
     * 躯干脂肪率
     */
    private BigDecimal allBodyFatRate;

    /**
     * 左上肢肌肉
     */
    private BigDecimal leftArmMuscle;

    /**
     * 左上肢肌肉率
     */
    private BigDecimal leftArmMuscleRate;

    /**
     * 右上肢肌肉
     */
    private BigDecimal rightArmMuscle;

    /**
     * 右上肢肌肉率
     */
    private BigDecimal rightArmMuscleRate;

    /**
     * 左下肢肌肉
     */
    private BigDecimal leftLegMuscle;

    /**
     * 左下肢肌肉率
     */
    private BigDecimal leftLegMuscleRate;

    /**
     * 右下肢肌肉
     */
    private BigDecimal rightLegMuscle;

    /**
     * 右下肢肌肉率
     */
    private BigDecimal rightLegMuscleRate;

    /**
     * 躯干肌肉
     */
    private BigDecimal trunkMuscle;

    /**
     * 躯干肌肉率
     */
    private BigDecimal trunkMuscleRate;

    /**
     * 左上肢阻抗
     */
    private BigDecimal leftArmImpedance;

    /**
     * 右上肢阻抗
     */
    private BigDecimal rightArmImpedance;

    /**
     * 左下肢阻抗
     */
    private BigDecimal leftLegImpedance;

    /**
     * 右下肢阻抗
     */
    private BigDecimal rightLegImpedance;

    /**
     * 躯干阻抗
     */
    private BigDecimal bodyImpedance;

}
