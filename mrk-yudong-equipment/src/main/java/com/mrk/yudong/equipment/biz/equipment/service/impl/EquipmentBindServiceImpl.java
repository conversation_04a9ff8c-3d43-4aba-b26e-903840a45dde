package com.mrk.yudong.equipment.biz.equipment.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.merach.sun.device.api.DeviceApi;
import com.merach.sun.device.dto.cmd.device.MigrateDeviceBindCmd;
import com.merach.sun.device.dto.cmd.device.UnbindDeviceCmd;
import com.merach.sun.device.dto.resp.model.ProductModelDetailDTO;
import com.merach.sun.device.dto.resp.product.ProductDetailDTO;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.equipment.api.equipment.dto.EquipmentBindDTO;
import com.mrk.yudong.equipment.api.equipment.dto.UpdateEquipDTO;
import com.mrk.yudong.equipment.biz.equipment.bo.EquipmentBindBO;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentBindService;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentInfoService;
import com.mrk.yudong.equipment.feign.UserFeign;
import com.mrk.yudong.equipment.infrastructure.equipment.mapper.EquipmentBindMapper;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentBind;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentCategory;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentOther;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.BaseTipConstant;
import com.mrk.yudong.share.constant.ResponseConstant;
import com.mrk.yudong.share.constant.equipment.EquipmentConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 设备绑定明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class EquipmentBindServiceImpl extends BaseServiceImpl<EquipmentBindMapper, EquipmentBind> implements IEquipmentBindService {

    private final UserFeign userFeign;

    private final IEquipmentInfoService equipmentInfoService;

    private final DeviceApi deviceApi;

    /**
     * 绑定 / 解绑设备
     *
     * @param equipmentBindBO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R bind(ProductModelDetailDTO productModel, EquipmentBindBO equipmentBindBO) {
        Integer operation = equipmentBindBO.getOperation();

        if (operation.equals(BaseConstant.INT_TRUE)) {
            // 绑定设备
            return this.saveEquipmentBind(productModel, equipmentBindBO);
        } else {
            // 解绑设备
            return this.removeEquipmentBind(equipmentBindBO.getBindId());
        }
    }

    /**
     * 绑定设备
     *
     * @param productModel
     * @param equipmentBindBO
     * @return
     */
    private R saveEquipmentBind(ProductModelDetailDTO productModel, EquipmentBindBO equipmentBindBO) {
        EquipmentInfo equipmentInfo = new EquipmentInfo();
        equipmentInfo.setOneLevelTypeId(productModel.getProductId());
        equipmentInfo.setName(equipmentBindBO.getName());
        R bind = equipmentInfoService.bind(equipmentInfo);
        if (R.isFail(bind)) {
            return bind;
        }
        UpdateEquipDTO data = Convert.convert(UpdateEquipDTO.class, bind.getData());
        EquipmentBindDTO equipmentBindDTO = new EquipmentBindDTO();
        equipmentBindDTO.setCategoryId(data.getOneLevelTypeId());
        equipmentBindDTO.setCategoryName(data.getName());
        equipmentBindDTO.setCategoryCover(data.getTwoLevelImage());
        R trainMeta = userFeign.isTrainMeta(SessionUtil.getId());
        Integer isMeta = (int) trainMeta.getData() > 0 ? BaseConstant.INT_TRUE : BaseConstant.INT_FALSE;
        equipmentBindDTO.setIsMeta(isMeta);

        if (data.getId() != null) {
            equipmentBindDTO.setBindId(data.getId());
        }
        if (StrUtil.isNotBlank(data.getName())) {
            equipmentBindDTO.setEquipmentName(data.getName());
        }
        if (StrUtil.isNotBlank(data.getCode())) {
            equipmentBindDTO.setMac(data.getCode());
        }
        if (productModel != null) {
            equipmentBindDTO.setEquipmentId(productModel.getModelId());
            equipmentBindDTO.setBluetoothName(productModel.getModelName());
            equipmentBindDTO.setEquipmentCover(productModel.getCover());
            equipmentBindDTO.setModelCode(productModel.getModelCode());
            equipmentBindDTO.setIsLinkApp(BaseConstant.INT_TRUE);
        }

        return R.ok(equipmentBindDTO);
    }

    /**
     * 解绑设备
     *
     * @param bindId
     * @return
     */
    private R removeEquipmentBind(Long bindId) {
        if (bindId == null) {
            return R.paramFail(BaseTipConstant.PARAM_FAIL);
        }

        UnbindDeviceCmd unbindDevice = new UnbindDeviceCmd();
        unbindDevice.setDeviceUserRelId(bindId);
        Boolean update = deviceApi.unbindDevice(unbindDevice);

        EquipmentInfo equipmentInfo = new EquipmentInfo();
        equipmentInfo.setUpdateTime(LocalDateTime.now());
        equipmentInfo.setBindStatus(BaseConstant.INT_FALSE);
        equipmentInfo.setId(bindId);
        equipmentInfoService.updateById(equipmentInfo);
        if (Boolean.TRUE.equals(update)) {
            return R.ok();
        }
        return R.fail(ResponseConstant.COMMON_OPERATION_FAILED);
    }

    /**
     * 根据用户ID和设备分类ID获取最新的绑定信息
     *
     * @param userId
     * @param categoryId
     * @param bindStatus
     * @return
     */
    @Override
    public EquipmentBind getTopInfo(Long userId, Long categoryId, Integer bindStatus) {
        return baseMapper.getTopInfo(userId, categoryId, bindStatus);
    }

    /**
     * 构建绑定信息返回值
     *
     * @param equipmentCategory
     * @param equipmentOther
     * @param bindId
     * @param bluetoothName
     * @param mac
     * @return
     */
    @Override
    public EquipmentBindDTO buildBindDTO(EquipmentCategory equipmentCategory, EquipmentOther equipmentOther, Long bindId, String bluetoothName, String mac) {
        EquipmentBindDTO equipmentBindDTO = new EquipmentBindDTO();
        equipmentBindDTO.setCategoryId(equipmentCategory.getType().longValue());
        equipmentBindDTO.setCategoryName(equipmentCategory.getName());
        equipmentBindDTO.setCategoryCover(equipmentCategory.getCover());
        equipmentBindDTO.setCategoryIcon(equipmentCategory.getIcon());

        R trainMeta = userFeign.isTrainMeta(SessionUtil.getId());
        Integer isMeta = (int) trainMeta.getData() > 0 ? BaseConstant.INT_TRUE : BaseConstant.INT_FALSE;
        equipmentBindDTO.setIsMeta(isMeta);

        if (bindId != null) {
            equipmentBindDTO.setBindId(bindId);
        }
        if (StrUtil.isNotBlank(bluetoothName)) {
            equipmentBindDTO.setEquipmentName(bluetoothName);
        }
        if (StrUtil.isNotBlank(mac)) {
            equipmentBindDTO.setMac(mac);
        }
        if (equipmentOther != null) {
            equipmentBindDTO.setEquipmentId(equipmentOther.getId());
            equipmentBindDTO.setBluetoothName(equipmentOther.getName());
            equipmentBindDTO.setEquipmentCover(equipmentOther.getCover());
            equipmentBindDTO.setModelCode(equipmentOther.getModelCode());
            equipmentBindDTO.setBluetoothCode(equipmentOther.getBluetoothCode());
            equipmentBindDTO.setIsLinkApp(equipmentOther.getIsLinkApp());
        }

        return equipmentBindDTO;
    }

    @Override
    public EquipmentBindDTO newBuildBindDTO(ProductDetailDTO productDetail, ProductModelDetailDTO productModel, Long bindId, String bluetoothName, String mac) {
        EquipmentBindDTO equipmentBindDTO = new EquipmentBindDTO();
        equipmentBindDTO.setCategoryId(productDetail.getId());
        equipmentBindDTO.setCategoryName(productDetail.getName());
        equipmentBindDTO.setCategoryCover(productDetail.getCover());
        equipmentBindDTO.setCategoryIcon(productDetail.getCover());

        R trainMeta = userFeign.isTrainMeta(SessionUtil.getId());
        Integer isMeta = (int) trainMeta.getData() > 0 ? BaseConstant.INT_TRUE : BaseConstant.INT_FALSE;
        equipmentBindDTO.setIsMeta(isMeta);

        if (bindId != null) {
            equipmentBindDTO.setBindId(bindId);
        }
        if (StrUtil.isNotBlank(bluetoothName)) {
            equipmentBindDTO.setEquipmentName(bluetoothName);
        }
        if (StrUtil.isNotBlank(mac)) {
            equipmentBindDTO.setMac(mac);
        }
        if (productModel != null) {
            equipmentBindDTO.setEquipmentId(productModel.getModelId());
            equipmentBindDTO.setBluetoothName(productModel.getModelName());
            equipmentBindDTO.setEquipmentCover(productModel.getCover());
            equipmentBindDTO.setModelCode(productModel.getModelCode());
            equipmentBindDTO.setIsLinkApp(BaseConstant.INT_TRUE);
        }

        return equipmentBindDTO;
    }

    @Override
    public Boolean heartRateDataMigrationToInfo(Long prouctId, Long productModelId, Integer isTest) {
        List<EquipmentBind> equipmentBinds = getEquipmentBinds(prouctId, productModelId, isTest);

        if (CollUtil.isNotEmpty(equipmentBinds)) {
            return saveEquipmentInfo(equipmentBinds);
        }

        return false;
    }

    @Override
    public void heartRateDataMigrationToDevice(Long prouctId, Long productModelId, Integer isTest) {
        List<EquipmentBind> equipmentBinds = getEquipmentBinds(prouctId, productModelId, isTest);

        if (CollUtil.isNotEmpty(equipmentBinds)) {
            saveDeviceInfo(equipmentBinds);
        }

    }

    private List<EquipmentBind> getEquipmentBinds(Long prouctId, Long productModelId, Integer isTest) {
        LambdaQueryWrapper<EquipmentBind> queryWrapper = new LambdaQueryWrapper<>();
        if (null != prouctId) {
            queryWrapper.eq(EquipmentBind::getCategoryId, prouctId);
        }
        if (null != productModelId) {
            queryWrapper.eq(EquipmentBind::getEquipmentId, productModelId);
        }
        if (BaseConstant.INT_TRUE == isTest) {
            queryWrapper.eq(EquipmentBind::getUserId, SessionUtil.getId());
        }

        List<EquipmentBind> equipmentBinds = baseMapper.selectList(queryWrapper);
        return equipmentBinds;
    }

    private Boolean saveEquipmentInfo(List<EquipmentBind> equipmentBinds) {
        log.info("Start assembly info data");
        List<EquipmentInfo> existEquipmentInfos = equipmentInfoService.list("two_level_type_id", ConditionEnum.EQ, equipmentBinds.get(0).getEquipmentId());
        Map<Long, EquipmentInfo> existEquipmentInfoMap = null;
        if (CollUtil.isNotEmpty(existEquipmentInfos)) {
            existEquipmentInfoMap = existEquipmentInfos.stream().collect(Collectors.toMap(EquipmentInfo::getId, v -> v, (v1, v2) -> v1));
        }

        Map<Long, EquipmentInfo> finalExistEquipmentInfoMap = existEquipmentInfoMap;
        List<EquipmentInfo> equipmentInfoList = equipmentBinds.stream().filter(v -> null == MapUtil.get(finalExistEquipmentInfoMap, v.getId(), EquipmentInfo.class)).map(addInfo -> {
            log.info("当前数据：{}", JSONObject.toJSONString(addInfo));
            EquipmentInfo equipmentInfo = new EquipmentInfo();
            equipmentInfo.setName(addInfo.getName());
            equipmentInfo.setCode(addInfo.getMac());
            equipmentInfo.setBindStatus(addInfo.getBindStatus());
            equipmentInfo.setId(addInfo.getId());
            equipmentInfo.setOneLevelTypeId(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_100);
            equipmentInfo.setTwoLevelTypeId(addInfo.getEquipmentId());
            equipmentInfo.setUserBy(addInfo.getUserId());
            equipmentInfo.setCreateBy(addInfo.getUserId());
            equipmentInfo.setUpdateBy(addInfo.getUserId());
            equipmentInfo.setCreateTime(addInfo.getCreateTime());
            equipmentInfo.setUpdateTime(addInfo.getUpdateTime());
            equipmentInfo.setConnectTime(addInfo.getUpdateTime());
            equipmentInfo.setBindTime(addInfo.getCreateTime());
            return equipmentInfo;
        }).collect(Collectors.toList());
        log.info("Start Saving equipment info");
        return equipmentInfoService.saveBatch(equipmentInfoList);
    }

    private void saveDeviceInfo(List<EquipmentBind> equipmentBinds) {
        log.info("Start assembly device data");
        List<MigrateDeviceBindCmd> bindChangeCMDList =
                equipmentBinds.stream()
                        .map(
                                addInfo -> {
                                    log.info("当前数据：{}", JSONObject.toJSONString(addInfo));
                                    MigrateDeviceBindCmd MigrateDeviceBindCmd = new MigrateDeviceBindCmd();
                                    MigrateDeviceBindCmd.setName(addInfo.getName());
                                    MigrateDeviceBindCmd.setMac(addInfo.getMac());
                                    MigrateDeviceBindCmd.setBindStatus(addInfo.getBindStatus());
                                    MigrateDeviceBindCmd.setCategoryId(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_100);
                                    MigrateDeviceBindCmd.setId(addInfo.getId());
                                    MigrateDeviceBindCmd.setModelId(addInfo.getEquipmentId());
                                    MigrateDeviceBindCmd.setCreateTime(addInfo.getCreateTime());
                                    MigrateDeviceBindCmd.setUpdateTime(addInfo.getUpdateTime());
                                    MigrateDeviceBindCmd.setUserId(addInfo.getUserId());
                                    return MigrateDeviceBindCmd;
                                }).collect(Collectors.toList());

        log.info("Saving device info");
        deviceApi.migrateDevice(bindChangeCMDList);
    }
}
