package com.mrk.yudong.equipment.api.equipment.vo;

import lombok.Data;

/**
 * @description: 搜索设备列表
 * @author: ljx
 * @create: 2022/6/14 19:41
 * @Version 1.0
 **/
@Data
public class EquipSearchListVO {

    /**
     * 型号id
     */
    private Long id;
    /**
     * 展示图片
     */
    private String image;
    /**
     * 设备型号名称
     */
    private String equipName;

    /**
     * 通信协议：1:麦瑞克,2:FTMS,3:智健,4:柏群,5:FTMS+智健
     */
    private Integer communicationProtocol;
    /**
     * 是否是电磁控设备 1是0否
     */
    private Integer isMerach;
    /**
     * 一级类型id
     */
    private Long oneLevelTypeId;

    /**
     * 二级类型id
     */
    private Long twoLevelTypeId;

    /**
     * 文案信息
     */
    private String copywriting;

    /**
     * 文案图片
     */
    private String copywritingImage;


    /**
     * 电极信息，1:4电极，2:8电极
     */
    private Integer electrodeType;
}
