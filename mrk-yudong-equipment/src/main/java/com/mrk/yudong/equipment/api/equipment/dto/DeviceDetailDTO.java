package com.mrk.yudong.equipment.api.equipment.dto;

import lombok.Data;

/**
 * @description:
 * @author: ljx
 * @create: 2022/9/15 13:12
 * @Version 1.0
 **/
@Data
public class DeviceDetailDTO {

    /**
     * 设备用户关联id
     */
    private Long deviceUserRelId;

    /**
     * 起源id
     */
    private Long originId;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 设备uuid
     */
    private Long uuid;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 型号id
     */
    private Long modelId;

    /**
     * 型号名称
     */
    private String modelName;

    /**
     * 型号封面
     */
    private String modelCover;

    /**
     * 型号封面
     */
    private String cover;

    /**
     * mac地址
     */
    private String mac;

    /**
     * 蓝牙广播名
     */
    private String bluetoothName;

    /**
     * 蓝牙广播名
     */
    private Integer communicationProtocol;
}
