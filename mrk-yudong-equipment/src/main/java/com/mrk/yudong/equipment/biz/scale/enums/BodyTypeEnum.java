package com.mrk.yudong.equipment.biz.scale.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * @description: 体型
 * @author: ljx
 * @create: 2022/6/29 20:55
 * @Version 1.0
 **/
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum BodyTypeEnum {
    type_1_1(1, 1, "XXXL斗士", 1, 7, "偏低", "偏胖,肥胖,偏高"),
    type_2_1(2, 2, "肉肉男孩", 1, 8, "标准", "偏胖,肥胖,偏高"),
    type_3_1(3, 3, "潜力男神", 1, 9, "偏高", "偏胖,肥胖,偏高"),
    type_4_1(4, 4, "刚好先生", 1, 4, "偏低", "标准"),
    type_5_1(5, 5, "刚刚好先生", 1, 5, "标准", "标准"),
    type_6_1(6, 6, "形体偶像", 1, 6, "偏高", "标准"),
    type_7_1(7, 7, "漫画男孩", 1, 1, "偏低", "偏瘦,偏低"),
    type_8_1(8, 8, "紧致型男", 1, 2, "标准", "偏瘦,偏低"),
    type_9_1(9, 9, "健体男神", 1, 3, "偏高", "偏瘦,偏低"),

    type_1_2(10, 1, "丰满佳人", 2, 7, "偏低", "偏胖,肥胖,偏高"),
    type_2_2(11, 2, "肉肉女孩", 2, 8, "标准", "偏胖,肥胖,偏高"),
    type_3_2(12, 3, "潜力女神", 2, 9, "偏高", "偏胖,肥胖,偏高"),
    type_4_2(13, 4, "骨感女孩", 2, 4, "偏低", "标准"),
    type_5_2(14, 5, "满分辣妹", 2, 5, "标准", "标准"),
    type_6_2(15, 6, "维密天使", 2, 6, "偏高", "标准"),
    type_7_2(16, 7, "小辣椒", 2, 1, "偏低", "偏瘦,偏低"),
    type_8_2(17, 8, "诱惑女神", 2, 2, "标准", "偏瘦,偏低"),
    type_9_2(18, 9, "魔鬼身材", 2, 3, "偏高", "偏瘦,偏低"),
    ;
    /**
     * 唯一码
     */
    private Integer id;
    /**
     * 编号
     */
    private Integer code;
    /**
     * 展示名称
     */
    private String name;
    /**
     * 性别1男2女
     */
    private Integer sex;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 肌肉率
     */
    private String muscle;
    /**
     * 体脂率
     */
    private String bodyFatRate;

    public static BodyTypeEnum calculateBodyType(Integer sex, String muscle, String bodyFatRate) {
        if (StrUtil.isBlank(muscle) || StrUtil.isBlank(bodyFatRate)) {
            return null;
        }
        return Arrays.stream(BodyTypeEnum.values())
                .filter(v -> v.getSex().equals(sex)
                        && v.getMuscle().equals(muscle)
                        && v.getBodyFatRate().contains(bodyFatRate))
                .findFirst().orElse(null);
    }
    public static BodyTypeEnum getBodyType(Integer code,Integer sex) {
        return Arrays.stream(BodyTypeEnum.values())
                .filter(v -> v.getCode().equals(code) && v.getSex().equals(sex))
                .findFirst().orElse(null);
    }
}
