package com.mrk.yudong.equipment.api.scale.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.mrk.yudong.core.annotation.Anonymous;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.equipment.api.scale.dto.cmd.ScaleUserCmd;
import com.mrk.yudong.equipment.biz.scale.service.IScaleUserService;
import com.mrk.yudong.equipment.infrastructure.scale.model.ScaleUser;
import com.mrk.yudong.share.constant.ResponseConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * 体脂秤用户 前端控制器
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/scale-user")
public class ScaleUserController {

    private final IScaleUserService scaleUserService;



    /**
     * 3.2.0安卓上传身高时带上了cm，后台判断如果有cm则去除cm
     *
     * @param scaleUser
     * @return void
     * <AUTHOR>
     * @date 11:03 2023/12/7
     **/
    private static void splitCm(ScaleUserCmd scaleUser) {
        if (StrUtil.isNotBlank(scaleUser.getHeight()) && StrUtil.contains(scaleUser.getHeight(), "cm")) {
            scaleUser.setHeight(scaleUser.getHeight().split("cm")[0]);
        }
    }

    /**
     * 新增体脂秤用户
     *
     * @param scaleUser 用户信息
     * @return com.mrk.yudong.core.model.ResDTO
     * <AUTHOR>
     * @date 18:36 2022/6/14
     **/
    @PostMapping("scaleUser")
    public ResDTO<com.mrk.yudong.equipment.api.scale.dto.resp.ScaleUserDTO> saveScaleUser(@RequestBody @Valid ScaleUserCmd scaleUser) {
        try {
            //如果用户昵称大于15个字符，截取前15个字符
            if (scaleUser.getNickName().length() > 15) {
                scaleUser.setNickName(scaleUser.getNickName().substring(0, 15));
            }
            ScaleUser savedScaleUser = scaleUserService.saveScaleUser(scaleUser);
            if (Objects.nonNull(savedScaleUser)) {
                return ResDTO.ok(BeanUtil.copyProperties(savedScaleUser, com.mrk.yudong.equipment.api.scale.dto.resp.ScaleUserDTO.class));
            } else {
                return ResDTO.fail(ResponseConstant.SAVE_FAIL);
            }
        } catch (Exception e) {
            log.error("saveScaleUser error", e);
            return ResDTO.fail(e.getMessage());
        }



    }

    /**
     * 修改体脂秤用户数据
     *
     * @param scaleUser 用户信息
     * @return com.mrk.yudong.core.model.ResDTO
     * <AUTHOR>
     * @date 18:36 2022/6/14
     **/
    @PutMapping("scaleUser")
    public ResDTO<ScaleUser> updateScaleUser(@RequestBody @Valid ScaleUserCmd scaleUser) {
        if (null == scaleUser.getId()) {
            return ResDTO.fail("无效的体脂秤用户id");
        }
        //如果用户昵称大于15个字符，截取前15个字符
        if (scaleUser.getNickName().length() > 15) {
            scaleUser.setNickName(scaleUser.getNickName().substring(0, 15));
        }
        splitCm(scaleUser);
        return ResDTO.ok(scaleUserService.updateScaleUser(BeanUtil.copyProperties(scaleUser, ScaleUser.class)));
    }

    /**
     * 删除体脂秤用户
     *
     * @param id id
     * @return 删除信息
     */
    @DeleteMapping("scaleUser")
    public ResDTO<String> delScaleUser(Long id) {
        if (id == null) {
            return ResDTO.fail(ResponseConstant.PARAM_ERROR);
        }
        BaseQuery<ScaleUser> scaleRecordBaseQuery = new BaseQuery<>();
        scaleRecordBaseQuery.eq("id", id).eq("is_main", 0);
        boolean remove = scaleUserService.remove(scaleRecordBaseQuery);
        if (remove) {
            return ResDTO.ok();
        } else {
            return ResDTO.fail(ResponseConstant.DEL_FAIL);
        }
    }

    /**
     * 体脂秤用户集合
     *
     * @return com.mrk.yudong.core.model.ResDTO<java.util.List < com.mrk.yudong.equipment.infrastructure.scale.model.ScaleUser>>
     * <AUTHOR>
     * @date 19:01 2022/6/14
     **/
    @GetMapping("scaleUserList")
    public ResDTO<List<ScaleUser>> scaleUserList() {
        Long userId = SessionUtil.getId();
        if (userId == null) {
            return ResDTO.fail(ResponseConstant.COMMON_NOUSER);
        }
        return ResDTO.ok(scaleUserService.list("user_id", ConditionEnum.EQ, userId));
    }

    /**
     * 体脂秤用户详情
     *
     * @param scaleUserId 体脂秤用户id
     * @return com.mrk.yudong.core.model.ResDTO<com.mrk.yudong.equipment.infrastructure.scale.model.ScaleUser>
     * <AUTHOR>
     * @date 11:04 2022/6/17
     **/
    @GetMapping("scaleUserInfo")
    public ResDTO<ScaleUser> scaleUserInfo(Long scaleUserId) {
        if (scaleUserId == null) {
            return ResDTO.fail(ResponseConstant.PARAM_ERROR);
        }
        return ResDTO.ok(scaleUserService.getById(scaleUserId));
    }

    /**
     * 查询是否存在体脂秤账号无权限
     *
     * @param userId 用户id
     * @return com.mrk.yudong.core.model.ResDTO<java.lang.Boolean>
     * <AUTHOR>
     * @date 10:39 2022/6/27
     **/
    @GetMapping("isExistAccount")
    @Anonymous
    public ResDTO<Boolean> isExistAccount(@RequestParam("userId") Long userId) {
        if (userId == null) {
            return ResDTO.fail(ResponseConstant.COMMON_NOUSER);
        }
        return ResDTO.ok(scaleUserService.isExist("user_id", ConditionEnum.EQ, userId));
    }

    /**
     * 查询是否存在体脂秤账号
     *
     * @return com.mrk.yudong.core.model.ResDTO<java.lang.Boolean>
     * <AUTHOR>
     * @date 10:39 2022/6/27
     **/
    @GetMapping("isExistScaleAccount")
    public ResDTO<Boolean> isExistScaleAccount() {
        Long userId = SessionUtil.getId();
        if (userId == null) {
            return ResDTO.fail(ResponseConstant.COMMON_NOUSER);
        }
        return isExistAccount(userId);
    }
}
