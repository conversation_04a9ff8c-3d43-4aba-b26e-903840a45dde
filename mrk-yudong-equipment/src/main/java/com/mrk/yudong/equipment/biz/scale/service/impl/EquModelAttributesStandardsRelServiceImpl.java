package com.mrk.yudong.equipment.biz.scale.service.impl;

import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.equipment.biz.scale.service.IEquModelAttributesStandardsRelService;
import com.mrk.yudong.equipment.infrastructure.scale.mapper.EquModelAttributesStandardsRelMapper;
import com.mrk.yudong.equipment.infrastructure.scale.model.ModelAttributesStandardsRel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 型号属性标准计算关联 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EquModelAttributesStandardsRelServiceImpl extends BaseServiceImpl<EquModelAttributesStandardsRelMapper, ModelAttributesStandardsRel> implements IEquModelAttributesStandardsRelService {

    public static final String BodyFatScaleBO = "model_id";
}
