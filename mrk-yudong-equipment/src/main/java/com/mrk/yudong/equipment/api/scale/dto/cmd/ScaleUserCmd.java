package com.mrk.yudong.equipment.api.scale.dto.cmd;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <p>
 * 体脂秤用户
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
@Data
public class ScaleUserCmd {

    private Long id;

    /**
     * 关联用户id
     */
    private Long userId;

    /**
     * 昵称
     */
    @NotBlank(message = "请填写昵称")
    private String nickName;

    /**
     *  性别 0-未知，1男，2女
     */
    @NotNull(message ="请选择性别")
    private Integer sex;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 身高
     */
    @NotNull(message ="请输入身高")
    private String height;

    /**
     * 生日
     */
    @NotBlank(message = "请选择生日")
    private String birthday;

    /**
     * 是否是主账号，1是0否
     */
    private Integer isMain;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 状态,0正常
     */
    private Integer status;

}
