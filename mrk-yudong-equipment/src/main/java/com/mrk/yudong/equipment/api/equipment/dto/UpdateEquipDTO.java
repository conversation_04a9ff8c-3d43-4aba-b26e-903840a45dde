package com.mrk.yudong.equipment.api.equipment.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * @description: 绑定、更新设备返回对象
 * @author: ljx
 * @create: 2022/6/28 14:37
 * @Version 1.0
 **/
@Data
public class UpdateEquipDTO {

    /**
     * 设备id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 一级设备类型id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long oneLevelTypeId;

    /**
     * 二级设备类型id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long twoLevelTypeId;
    /**
     * 设备用户关联id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deviceUserRelId;
    /**
     * 二级设备类型图片
     */
    private String twoLevelImage;
    /**
     * 通信协议：1:麦瑞克,2:FTMS,3:智健,4:柏群,5:FTMS+智健
     */
    private Integer communicationProtocol;
    /**
     * ota类型
     */
    private Integer otaType;
    /**
     * 特征值
     */
    private Integer eigenValue;
    /**
     * mac地址
     */
    private String code;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备所属产品的ProductKey
     */
    private String productKey;

    /**
     * IoT 设备在产品内的唯一标识符
     */
    private String deviceName;

    /**
     * iot 为设备颁发的设备密钥
     */
    private String deviceSecret;

    /**
     * iot实例id
     */
    private String  iotInstanceId;
}
