package com.mrk.yudong.equipment.biz.equipment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.merach.sun.device.api.ControlMappingApi;
import com.merach.sun.device.dto.qry.ControlMappingConverter;
import com.merach.sun.device.dto.qry.ControlMappingQry;
import com.merach.sun.device.dto.resp.control_mapping.ControlMappingDTO;
import com.merach.sun.device.enums.ControlMappingEnum;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.share.constant.equipment.EquipmentConstant;
import com.mrk.yudong.share.dto.equip.EquipmentTypeDTO;
import com.mrk.yudong.share.dto.equip.LiveMappingConvertDTO;
import com.mrk.yudong.share.vo.equip.LiveMappingConvertVO;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.EquipUtil;
import com.mrk.yudong.core.utils.RedisUtil;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.equipment.api.equipment.dto.AgreementEquipDTO;
import com.mrk.yudong.equipment.api.equipment.vo.EquipBindingVO;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentTypeService;
import com.mrk.yudong.equipment.biz.equipment.service.IFirmwareVersionService;
import com.mrk.yudong.equipment.infrastructure.equipment.mapper.EquipDictMapper;
import com.mrk.yudong.equipment.infrastructure.equipment.mapper.EquipmentInfoMapper;
import com.mrk.yudong.equipment.infrastructure.equipment.mapper.EquipmentTypeMapper;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipDict;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType;
import com.mrk.yudong.equipment.infrastructure.equipment.model.FirmwareVersion;
import com.mrk.yudong.equipment.utils.EquipTypeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EquipmentTypeServiceImpl extends BaseServiceImpl<EquipmentTypeMapper, EquipmentType> implements IEquipmentTypeService {

    private final RedisUtil redisUtil;

    private final EquipDictMapper equipDictMapper;

    private final StringRedisTemplate redisTemplate;

    private final EquipmentInfoMapper equipmentInfoMapper;

    private final IFirmwareVersionService firmwareVersionService;

    private final EquipTypeUtil equipTypeUtil;

    private final ControlMappingApi controlMappingApi;

    @Override
    public List<EquipmentType> getEquipmentTypeName() {
        QueryWrapper<EquipmentType> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id", "type_name", "type_images");
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 项目启动时，初始化到缓存
     */
    @PostConstruct
    public void init() {
        redisUtil.deleteObject(RedisKeyConstant.EQUIP_TYPE_T);
        List<EquipmentType> equipmentTypeName = list();
        equipmentTypeName.forEach(equipmentType -> {
            Integer isElectromagneticControl = equipmentType.getIsElectromagneticControl();
            if (isElectromagneticControl != null && isElectromagneticControl.equals(BaseConstant.INT_TRUE)) {
                equipmentType.setIsSupportConnection(isElectromagneticControl);
            }
            redisTemplate.opsForHash().put(RedisKeyConstant.ALL_EQUIPMENT_TYPE_KEY, equipmentType.getId().toString(), JSON.toJSONString(equipmentType));
            redisUtil.setCacheMapValue(RedisKeyConstant.EQUIPMENT_TYPE_KEY, equipmentType.getId().toString(), JSON.toJSONString(equipmentType, SerializerFeature.WriteMapNullValue));
        });
        BaseQuery<EquipDict> baseQuery2 = new BaseQuery<>();
        baseQuery2.eq("dict_key", RedisKeyConstant.EQUIP_TYPE_T);
        List<EquipDict> equipDictList2 = equipDictMapper.selectList(baseQuery2);
        for (EquipDict equipDict : equipDictList2) {
            redisUtil.setCacheMapValue(RedisKeyConstant.EQUIP_TYPE_T, equipDict.getId().toString(), JSON.toJSONString(equipDict));
        }
    }

    /**
     * 从数据库中获取一级类型名称
     *
     * @param infoList 设备信息
     * @return 转换好的数据
     */
    @Override
    public List<EquipmentTypeDTO> getMysqlEquipType(List<EquipmentInfo> infoList) {
        //获取mysql中的所有一级数据
        QueryWrapper<EquipmentType> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id", "type_name").eq("level", 0);
        List<EquipmentType> oneLevelList = list(queryWrapper);
        Map<Object, Object> oneLevelMap = new HashMap<>(12);
        //将一级信息存入map
        oneLevelList.forEach(oneLevel -> {
            if (StrUtil.isNotEmpty(oneLevel.getTypeName())) {
                oneLevelMap.put(oneLevel.getId(), oneLevel.getTypeName());
            } else {
                oneLevelMap.put(oneLevel.getId(), null);
            }
        });
        //获取二级分类Map
        Map<Object, Object> twoLevelMap = new HashMap<>(30);
        List<EquipmentTypeDTO> returnInfo = new ArrayList<>();
        for (EquipmentInfo info : infoList) {
            EquipmentTypeDTO equipmentTypeDto = new EquipmentTypeDTO();
            if (info.getTwoLevelTypeId() == null) {
                continue;
            }
            //先去找出二级类型
            Long parent;
            if (twoLevelMap.containsKey(info.getTwoLevelTypeId())) {
                parent = (Long) twoLevelMap.get(info.getTwoLevelTypeId());
            } else {
                EquipmentType equipmentType = getById(info.getTwoLevelTypeId());
                parent = equipmentType.getParentId();
                twoLevelMap.put(equipmentType.getId(), parent);
            }
            //查出一级信息
            if (oneLevelMap.containsKey(parent)) {
                equipmentTypeDto.setId(info.getId());
                equipmentTypeDto.setTypeName((String) oneLevelMap.get(parent));
                returnInfo.add(equipmentTypeDto);
            }
        }
        return returnInfo;
    }

    @Override
    public List<EquipmentType> getTypeId(String code, Integer prefix, Long oneLevelTypeId) {
        QueryWrapper<EquipmentType> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("prefix_id", prefix);
        if (StrUtil.isNotBlank(code)) {
            queryWrapper.eq("code", code);
        }
        if (oneLevelTypeId != null) {
            queryWrapper.eq("parent_id", oneLevelTypeId);
        }
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<EquipmentType> getPage(IPage<EquipmentType> page, EquipmentType equipmentType) {
        return baseMapper.getPage(page, equipmentType);
    }

    @Override
    public EquipmentType getOtherInfo() {
        return baseMapper.getOtherInfo();
    }

    /**
     * 刷新设备缓存
     */
    @Override
    public void refreshRedis() {
        this.init();
    }

    @Override
    public List<Dict> supportConnectionEquipList() {
        List<EquipmentType> equipmentTypeList = list("is_tra", ConditionEnum.LE, SessionUtil.getIsTra());
        List<Dict> dictList = new ArrayList<>();
        if (CollUtil.isNotEmpty(equipmentTypeList)) {
            Map<Long, String> typeMap = equipmentTypeList.stream().filter(v -> v.getLevel().equals(0)).collect(Collectors.toMap(EquipmentType::getId, EquipmentType::getTypeName));
            Map<Long, List<EquipmentType>> modelMap = equipmentTypeList.stream().filter(v -> v.getLevel().equals(1) && v.getIsSupportConnection().equals(BaseConstant.INT_TRUE)).collect(Collectors.groupingBy(EquipmentType::getParentId));
            modelMap.forEach((k, v) -> {
                Dict dict = new Dict();
                dict.set("equipTypeId", k);
                String typeName = typeMap.get(k);
                dict.set("equipTypeName", typeName);
                List<Dict> equipModelList = v.stream().map(model -> {
                    Dict modelDict = new Dict();
                    modelDict.set("equipModelImage", model.getTypeImages());
                    modelDict.set("equipModelId", model.getId());
                    modelDict.set("equipModelName", model.getTypeName());
                    return modelDict;
                }).collect(Collectors.toList());
                dict.set("equipModelList", equipModelList);
                dictList.add(dict);
            });
        }
        return dictList;
    }

    @Override
    public EquipmentType equipmentTypeChange(EquipmentType equipmentType, String meritEigenValue, String name) {
        log.info("equipmentTypeChange参数，equipmentType:{},meritEigenValue:{}", equipmentType.getCode(), meritEigenValue);
        //判断是否是需要转换的设备
        if (EquipUtil.isUniqueModel(equipmentType.getCode())) {
            //通过协议特征值获取协议信息
            String remark = equipmentType.getRemark();
            if (StrUtil.isBlank(remark)) {
                return equipmentType;
            }
            if ("581".equals(equipmentType.getCode()) && StrUtil.isNotBlank(name) && 19 == name.length()) {
                meritEigenValue = "581";
            }
            if (StrUtil.isBlank(meritEigenValue)) {
                //没有获取到f8c4的值默认为ftms
                meritEigenValue = "FTMS";
            }
            List<AgreementEquipDTO> agreementEquips = JSONArray.parseArray(remark, AgreementEquipDTO.class);
            for (AgreementEquipDTO equip : agreementEquips) {
                if (meritEigenValue.contains(equip.getMeritEigenValue())) {
                    equipmentType.setIsElectromagneticControl(equip.getIsElectromagneticControl());
                    equipmentType.setShowResistance(equip.getShowResistance());
                    equipmentType.setShowSlope(equip.getShowSlope());
                    equipmentType.setCommunicationProtocol(equip.getCommunicationProtocol());
                }
            }
        }
        return equipmentType;
    }

    @Override
    public EquipBindingVO addEquipBindingVo(String name, String oneLevelTypeId) {
        EquipBindingVO equipBindingVo = null;
        Long userId = SessionUtil.getId();
        //获取蓝牙名称中的型号
        Map<String, Object> typeInfo = equipTypeUtil.getTypeCode(name);
        String typeCode = MapUtil.getStr(typeInfo, "typeCode");
        Integer prefix = MapUtil.getInt(typeInfo, "prefix");
        if (prefix != null) {
            log.info("typeCode:{},prefix:{}", typeCode, prefix);
            //去数据库中查出对应型号数据
            if (StrUtil.isBlank(typeCode)) {
                equipBindingVo = addEquipBindingVo(prefix, null, name, userId, oneLevelTypeId);
            } else {
                //增加设备分类查询
                equipBindingVo = addEquipBindingVo(prefix, typeCode, name, userId, oneLevelTypeId);
            }
        }
        return equipBindingVo;
    }

    @Override
    public List<EquipmentType> getModel(String name, Long productId) {
        Map<String, Object> typeInfo = equipTypeUtil.getTypeCode(name);
        String typeCode = MapUtil.getStr(typeInfo, "typeCode");
        Integer prefix = MapUtil.getInt(typeInfo, "prefix");
        if (prefix == null) {
            return null;
        }

        return getTypeId(typeCode, prefix, productId);
    }

    @Override
    public List<LiveMappingConvertDTO> listModelControlMapping(LiveMappingConvertVO liveMappingConvertVO) {
        if (checkParams(liveMappingConvertVO)) {
            return null;
        }

        List<ControlMappingDTO> lessonNotes = convertLessonNotes(liveMappingConvertVO);
        if (CollUtil.isEmpty(lessonNotes)) {
            return null;
        }

        return getLiveMappingConvertDTOS(liveMappingConvertVO, lessonNotes);
    }

    /**
     * 新增返回数据
     *
     * @param typeCode 类型编号
     * @param name     设备名称（蓝牙）
     * @param userId   用户id
     */
    public EquipBindingVO addEquipBindingVo(Integer prefix, String typeCode, String name, Long userId, String oneLevelTypeId) {
        EquipBindingVO equipBindingVo = new EquipBindingVO();
        //查询类型图片
        LambdaQueryWrapper<EquipmentType> query = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(typeCode)) {
            query.eq(EquipmentType::getCode, typeCode);
        }
        if (prefix != null) {
            query.eq(EquipmentType::getPrefixId, prefix);
        }
        if (StrUtil.isNotBlank(oneLevelTypeId)) {
            query.eq(EquipmentType::getParentId, oneLevelTypeId);
            equipBindingVo.setOneLevelTypeId(Long.valueOf(oneLevelTypeId));
            //分类数据
            EquipmentType oneLevelInfo = baseMapper.selectById(oneLevelTypeId);
            if (oneLevelInfo != null) {
                equipBindingVo.setOneLevelTypeName(oneLevelInfo.getTypeName());
            }
        }
        List<EquipmentType> equipmentTypeInfos = baseMapper.selectList(query);
        EquipmentType equipmentTypeInfo = null;
        if (CollUtil.isNotEmpty(equipmentTypeInfos)) {
            equipmentTypeInfo = equipmentTypeInfos.get(0);
        }
        if (equipmentTypeInfo != null && StrUtil.isNotEmpty(equipmentTypeInfo.getTypeImages())) {
            //查询ota类型
            List<FirmwareVersion> firmwareVersions = firmwareVersionService.list("equip_model_id", ConditionEnum.EQ, equipmentTypeInfo.getId());
            if (CollUtil.isNotEmpty(firmwareVersions)) {
                FirmwareVersion firmwareVersion = firmwareVersions.get(0);
                equipBindingVo.setOtaType(firmwareVersion.getOtaType());
            }
            equipBindingVo.setImage(equipmentTypeInfo.getTypeImages());
            equipBindingVo.setTwoTypeId(equipmentTypeInfo.getId());
            equipBindingVo.setTwoTypeName(equipmentTypeInfo.getTypeName());
            if (equipmentTypeInfo.getEigenValue() != null) {
                equipBindingVo.setEigenValue(equipmentTypeInfo.getEigenValue());
            }
            if (equipmentTypeInfo.getIsElectromagneticControl() != null) {
                //版本判断
                equipBindingVo.setIsMerach(equipmentTypeInfo.getIsElectromagneticControl());
            } else {
                equipBindingVo.setIsMerach(0);
            }
            if (equipBindingVo.getOneLevelTypeId() == null) {
                equipBindingVo.setOneLevelTypeId(equipmentTypeInfo.getParentId());
            }
            if (equipmentTypeInfo.getCommunicationProtocol() != null) {
                equipBindingVo.setCommunicationProtocol(equipmentTypeInfo.getCommunicationProtocol());
            }
        }
        //查询该设备是否已经绑定
        BaseQuery<EquipmentInfo> baseQuery = new BaseQuery<>();
        baseQuery.eq("name", name).eq("bind_status", 1).eq("user_by", userId);
        List<EquipmentInfo> list = equipmentInfoMapper.selectList(baseQuery);
        if (CollUtil.isNotEmpty(list)) {
            EquipmentInfo equipmentInfo = list.get(0);
            equipBindingVo.setId(equipmentInfo.getId());
            equipBindingVo.setUserVersion(equipmentInfo.getUserVersion());
        }
        equipBindingVo.setEquipName(name);
        equipBindingVo.setIsBinding(list.size());

        return equipBindingVo;
    }

    private boolean checkParams(LiveMappingConvertVO liveMappingConvertVO) {
        if (null == liveMappingConvertVO.getModelId()) {
            return true;
        }
        if (null == liveMappingConvertVO.getEquipmentId()) {
            return true;
        }
        return CollUtil.isEmpty(liveMappingConvertVO.getLinks());
    }

    private List<ControlMappingDTO> convertLessonNotes(LiveMappingConvertVO liveMappingConvertVO) {
        boolean isTreadmill = ObjectUtil.equals(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_2, liveMappingConvertVO.getEquipmentId());

        List<ControlMappingConverter> controlMappings = new ArrayList<>();
        ControlMappingQry controlMappingQry = new ControlMappingQry();
        controlMappingQry.setProductModelId(liveMappingConvertVO.getModelId());
        controlMappingQry.setProductId(liveMappingConvertVO.getEquipmentId());
        controlMappingQry.setUserId(SessionUtil.getId());
        liveMappingConvertVO.getLinks()
                .forEach(link -> {
                    log.info("link:{}", JSONObject.toJSONString(link));
                    if (!ObjectUtil.equals(0, link.getAdviseNum())) {
                        if (isTreadmill) {
                            controlMappings.add(addControlMapping(ControlMappingEnum.SLOPE.getCode(), BigDecimal.valueOf(link.getAdviseNum()), link.getLinkId()));
                        } else {
                            controlMappings.add(addControlMapping(ControlMappingEnum.RESISTANCE.getCode(), BigDecimal.valueOf(link.getAdviseNum()), link.getLinkId()));
                        }
                    }

                    if (!ObjectUtil.equals(0, link.getSlopeNum())) {
                        controlMappings.add(addControlMapping(ControlMappingEnum.SLOPE.getCode(), BigDecimal.valueOf(link.getSlopeNum()), link.getLinkId()));
                    }
                });
        controlMappingQry.setControlValuetype(1);
        controlMappingQry.setMappingConverters(controlMappings);
        return controlMappingApi.converterControlMapping(controlMappingQry);
    }

    private ControlMappingConverter addControlMapping(Integer code, BigDecimal oldValue, Long linkId) {
        ControlMappingConverter converterControlMapping = new ControlMappingConverter();
        converterControlMapping.setType(code);
        converterControlMapping.setOldValue(oldValue);
        converterControlMapping.setLinkId(linkId);
        return converterControlMapping;
    }

    private List<LiveMappingConvertDTO> getLiveMappingConvertDTOS(LiveMappingConvertVO liveMappingConvertVO, List<ControlMappingDTO> lessonNotes) {
        Map<Long, List<ControlMappingDTO>> lessonNotesGroup = lessonNotes.stream().collect(Collectors.groupingBy(ControlMappingDTO::getLinkId));

        boolean isTreadmill = ObjectUtil.equals(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_2, liveMappingConvertVO.getEquipmentId());
        return liveMappingConvertVO.getLinks().stream().map(v -> {
            List<ControlMappingDTO> liveMappingConverts = lessonNotesGroup.get(v.getLinkId());
            LiveMappingConvertDTO liveMappingConvert = BeanUtil.copyProperties(v, LiveMappingConvertDTO.class);
            if (CollUtil.isEmpty(liveMappingConverts)) {
                return liveMappingConvert;
            }

            liveMappingConverts.forEach(dto -> {
                liveMappingConvert.setLinkId(dto.getLinkId());
                if (ControlMappingEnum.RESISTANCE.getCode().equals(dto.getType())) {
                    log.info("阻力：{}", JSONObject.toJSONString(dto));
                    liveMappingConvert.setAdviseNum(dto.getNewValue().intValue());
                }
                if (ControlMappingEnum.SLOPE.getCode().equals(dto.getType())) {
                    if (isTreadmill) {
                        log.info("跑步机坡度：{}", JSONObject.toJSONString(dto));
                        liveMappingConvert.setAdviseNum(dto.getNewValue().intValue());
                    } else {
                        log.info("非跑步机坡度：{}", JSONObject.toJSONString(dto));
                        liveMappingConvert.setSlopeNum(dto.getNewValue().intValue());
                    }
                }
            });
            return liveMappingConvert;
        }).collect(Collectors.toList());
    }

}
