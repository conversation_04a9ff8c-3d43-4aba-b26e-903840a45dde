package com.mrk.yudong.equipment.infrastructure.equipment.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.core.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("equ_equipment_info")
public class EquipmentInfo extends BaseModel {

    private static final long serialVersionUID = 1L;

    /**
     * mac地址
     */
    private String code;

    /**
     * 设备名称
     */
    private String name;
    /**
     * 蓝牙别名
     */
    @Size(min = 1, max = 10, message = "备注只支持1-10内的中英文、数字和符号")
    private String bluetoothAlias;
    /**
     * 一级设备类型id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long oneLevelTypeId;

    /**
     * 二级设备类型id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long twoLevelTypeId;

    /**
     * 绑定状态1：绑定，0：未绑定
     */
    private Integer bindStatus;
    /**
     * 绑定时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime bindTime;
    /**
     * 使用人信息
     */
    private Long userBy;

    /**
     * 升级记录版本
     */
    private String userVersion;

    /**
     * 是否推送过该版本的信息
     */
    private String firmwareVersion;

    /**
     * 最近连接时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime connectTime;

    /**
     * 备注信息
     */
    private String remark;
    /**
     * 所有特征值数据
     */
    private String characteristic;

    /**
     * sn码，全局唯一设备标识
     */
    private String snCode;

    /**
     * 一级类型名称
     */
    @TableField(exist = false)
    private String oneLevelTypeName;
    /**
     * 二级类型名称
     */
    @TableField(exist = false)
    private String twoLevelTypeName;
    /**
     * 二级图片地址
     */
    @TableField(exist = false)
    private String twoLevelImage;
    /**
     * 产品说明（http）
     */
    @TableField(exist = false)
    private String productManual;
    /**
     * 帮助中心（http）
     */
    @TableField(exist = false)
    private String helpCenter;
    /**
     * 是否是超燃脂
     */
    @TableField(exist = false)
    private Integer isMerach;

    /**
     * 设备id集合
     */
    @TableField(exist = false)
    private List<Long> equipIds;
    /**
     * 设备code集合
     */
    @TableField(exist = false)
    private List<String> codes;
    /**
     * 设备数量
     */
    @TableField(exist = false)
    private Integer userCount;
    /**
     * 修改类型，1.连接
     */
    @TableField(exist = false)
    private Integer updateType;

    /**
     * 通信协议：1:麦瑞克,2:FTMS,3:智健,4:柏群,5:FTMS+智健
     */
    @TableField(exist = false)
    private Integer communicationProtocol;
    /**
     * ota类型
     */
    @TableField(exist = false)
    private Integer otaType;
    /**
     * 特征值
     */
    @TableField(exist = false)
    private Integer eigenValue;

    /**
     * 型号图片
     */
    @TableField(exist = false)
    private String modelImages;

    /**
     * app类型
     */
    @TableField(exist = false)
    private Integer terminal = 1;

    /**
     * 协议特征值--2A24
     */
    @TableField(exist = false)
    private String agreementEigenValue;
    /**
     * 是否电磁控特征值--F8C4
     */
    @TableField(exist = false)
    private String meritEigenValue;

    /**
     * 设备类型1运动设备，3健康设备
     */
    @TableField(exist = false)
    private Integer type;
    /**
     * 是否支持ota
     */
    @TableField(exist = false)
    private Integer isOta;

    /**
     * 是否自动发放会员
     * 1-是，0-否，不传默认1，兼容老版本
     */
    @TableField(exist = false)
    private Integer autoReward;
}
