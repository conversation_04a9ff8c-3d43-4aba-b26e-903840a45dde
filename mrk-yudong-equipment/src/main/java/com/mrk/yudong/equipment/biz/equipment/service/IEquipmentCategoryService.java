package com.mrk.yudong.equipment.biz.equipment.service;

import com.mrk.yudong.core.service.BaseService;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentCategory;

/**
 * <p>
 * 设备分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
public interface IEquipmentCategoryService extends BaseService<EquipmentCategory> {

    /**
     * 根据类型获取设备分类
     *
     * @param categoryId
     * @param type
     * @return
     */
    EquipmentCategory getInfo(Long categoryId, Integer type);

}
