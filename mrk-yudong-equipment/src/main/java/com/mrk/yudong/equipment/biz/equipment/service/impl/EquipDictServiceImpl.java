package com.mrk.yudong.equipment.biz.equipment.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.SortModel;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipDictService;
import com.mrk.yudong.equipment.infrastructure.equipment.mapper.EquipDictMapper;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipDict;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 设备字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-09
 */
@Service
public class EquipDictServiceImpl extends BaseServiceImpl<EquipDictMapper, EquipDict> implements IEquipDictService {


    @Override
    public List<Dict> option(String code) {
        SortModel sortModel = new SortModel(ConditionEnum.ASC, "sort");
        List<EquipDict> list = this.list("dict_key", ConditionEnum.EQ, code, sortModel, "name", "value");
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>(0);
        }

        return list.stream().map(courseMeta -> {
            Dict dict = new Dict(2);
            dict.put("name", courseMeta.getName());
            dict.put("value", courseMeta.getValue());
            return dict;
        }).collect(Collectors.toList());
    }

}
