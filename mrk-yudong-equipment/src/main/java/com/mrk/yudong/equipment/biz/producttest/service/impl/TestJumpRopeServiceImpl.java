package com.mrk.yudong.equipment.biz.producttest.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.equipment.api.producttest.dto.TestJumpRopeForm;
import com.mrk.yudong.equipment.biz.producttest.service.ITestJumpRopeService;
import com.mrk.yudong.equipment.infrastructure.producttest.mapper.TestJumpRopeMapper;
import com.mrk.yudong.equipment.infrastructure.producttest.model.TestJumpRope;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  产测跳绳 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */

@Slf4j
@Service
public class TestJumpRopeServiceImpl extends BaseServiceImpl<TestJumpRopeMapper, TestJumpRope> implements ITestJumpRopeService {


    /**
     * 创建
     *
     * @param form
     */
    @Override
    public void create(TestJumpRopeForm form) {
        log.info("[TestJumpRopeServiceImpl#create] testJumpRopeFrom={}", JSON.toJSONString(form));
        TestJumpRope testJumpRope = BeanUtil.copyProperties(form, TestJumpRope.class);
        testJumpRope.setCreateId(SessionUtil.getId());
        testJumpRope.setUpdateId(SessionUtil.getId());
        baseMapper.insert(testJumpRope);
    }
}
