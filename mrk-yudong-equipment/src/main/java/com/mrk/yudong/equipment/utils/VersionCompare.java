package com.mrk.yudong.equipment.utils;

/**
 * 比较版本大小
 * <AUTHOR>
 * @create 2021−07-07 6:16 下午
 */
public class VersionCompare {
        /**
         * 该方法用于比较版本大小
         * @param v1 版本v1
         * @param v2 版本v2
         * v1>v2:1
         * v1<v2:-1
         */
        public static Integer compareVersion(String v1,String v2){
            //使用正则表达式分割版本
            String[] qvs1 = v1.split("[vV]");
            String[] qvs2 = v2.split("[vV]");
            String[] v1s;
            if(qvs1.length>1){
                v1s = qvs1[1].split("\\.");
            }else {
                v1s =qvs1[0].split("\\.");
            }
            String[] v2s;
            if(qvs2.length>1){
                v2s = qvs2[1].split("\\.");
            }else {
                v2s = qvs2[0].split("\\.");
            }
            //比较正则表达式分割后的string数组
            Integer result = compareStrings(v1s,v2s);
            //根据结果判断版本大小
            return result;
        }
        /**
         * 该方法用于比较正则表达式分割后的string数组
         */
        public static int compareStrings(String[] v1s,String[] v2s){
            for(int i=0;i<v1s.length;i++){
                //比较单个字符串大小
                Integer v1sInt = Integer.valueOf(v1s[i]);
                Integer v2sInt = Integer.valueOf(v2s[i]);
                if(v1sInt>v2sInt){
                    return 1;
                }else if(v1sInt<v2sInt){
                    return -1;
                }
            }
            return 0;
        }
}
