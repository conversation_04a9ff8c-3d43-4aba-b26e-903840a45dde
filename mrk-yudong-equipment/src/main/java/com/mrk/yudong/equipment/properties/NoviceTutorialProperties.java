package com.mrk.yudong.equipment.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.List;


@Data
@RefreshScope
@ConfigurationProperties(prefix = NoviceTutorialProperties.PREFIX)
public class NoviceTutorialProperties {

    public static final String PREFIX = "com.mrk.novice-tutorial";

    /**
     * 动感单车
     * 椭圆机
     * 跑步机
     * 划船机
     */
    private List<Long> courseThemeIdList;

}
