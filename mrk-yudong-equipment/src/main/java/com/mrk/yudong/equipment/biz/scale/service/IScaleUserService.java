package com.mrk.yudong.equipment.biz.scale.service;

import com.mrk.yudong.core.service.BaseService;
import com.mrk.yudong.equipment.api.scale.dto.cmd.ScaleUserCmd;
import com.mrk.yudong.equipment.infrastructure.scale.model.ScaleUser;

/**
 * <p>
 * 体脂秤用户 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
public interface IScaleUserService extends BaseService<ScaleUser> {

    ScaleUser saveScaleUser(ScaleUserCmd scaleUser);

    ScaleUser updateScaleUser(ScaleUser scaleUser);

    Boolean updateMainScaleUser(ScaleUser scaleUser);

    ScaleUser getMainScaleUser(Long userId);

}
