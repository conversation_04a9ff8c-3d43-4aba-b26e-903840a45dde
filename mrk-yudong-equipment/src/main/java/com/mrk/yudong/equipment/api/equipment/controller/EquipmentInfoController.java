package com.mrk.yudong.equipment.api.equipment.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.merach.sun.device.api.DeviceApi;
import com.merach.sun.device.api.ProductModelApi;
import com.merach.sun.device.dto.cmd.device.UnbindDeviceCmd;
import com.merach.sun.device.dto.qry.DeviceQry;
import com.merach.sun.device.dto.qry.DeviceUserRelQry;
import com.merach.sun.device.dto.resp.device.BindDeviceDTO;
import com.merach.sun.device.dto.resp.device.DeviceDTO;
import com.merach.sun.device.dto.resp.device.UserDeviceDTO;
import com.merach.sun.device.dto.resp.model.ProductModelDetailDTO;
import com.merach.sun.user.api.MemberApi;
import com.merach.sun.user.api.UserTaskApi;
import com.merach.sun.user.dto.vip.cmd.OpenVipCmd;
import com.merach.sun.user.enums.TaskStrategyEnum;
import com.merach.sun.user.enums.vip.VipFlowOperationTypeEnum;
import com.merach.sun.user.enums.vip.VipFlowTypeEnum;
import com.merach.sun.user.enums.vip.VipTypeEnum;
import com.merach.sun.user.form.user.task.TaskForm;
import com.mrk.yudong.core.annotation.Anonymous;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.utils.RedisUtil;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.equipment.api.equipment.dto.DeviceDetailDTO;
import com.mrk.yudong.equipment.api.equipment.dto.EquipTypeInfoDTO;
import com.mrk.yudong.equipment.api.equipment.dto.MigrationDTO;
import com.mrk.yudong.equipment.api.equipment.vo.EquipBindingVO;
import com.mrk.yudong.equipment.api.equipment.vo.UpdateEquipVO;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentInfoService;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentTypeService;
import com.mrk.yudong.equipment.biz.equipment.service.IFirmwareVersionService;
import com.mrk.yudong.equipment.biz.scale.constant.HealthConstant;
import com.mrk.yudong.equipment.feign.UserFeign;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType;
import com.mrk.yudong.equipment.infrastructure.equipment.model.FirmwareVersion;
import com.mrk.yudong.equipment.utils.EquipTypeUtil;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.share.constant.ResponseConstant;
import com.mrk.yudong.share.constant.equipment.EquipmentConstant;
import com.mrk.yudong.share.dto.equip.EquipInfoDTO;
import com.mrk.yudong.share.dto.equip.EquipmentTypeDTO;
import com.mrk.yudong.share.util.WebsocketSignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 设备信息前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
@RestController
@RequestMapping("/equipment/equipmentInfoController")
@RequiredArgsConstructor
@Slf4j
public class EquipmentInfoController {

    private final IEquipmentInfoService equipmentInfoService;
    private final IEquipmentTypeService equipmentTypeService;
    private final RedisUtil redisUtil;
    private final UserFeign userFeign;
    private final EquipTypeUtil equipTypeUtil;
    private final IFirmwareVersionService firmwareVersionService;

    private final UserTaskApi userTaskApi;

    private final DeviceApi deviceApi;

    private final ProductModelApi productModelApi;

    private final StringRedisTemplate redisTemplate;

    private final MemberApi memberApi;

    /**
     * 新增设备信息
     *
     * @param equipmentInfo 设备信息
     * @return R 所有设备信息
     */
    @PostMapping("equipment")
    @Deprecated
    public R saveEquipment(@RequestBody @Valid EquipmentInfo equipmentInfo) {
        log.info("废弃接口使用频率saveEquipment");
        Long userId = SessionUtil.getId();
        if (userId == null) {
            return R.fail(ResponseConstant.COMMON_NOUSER);
        }

        String addKey = RedisKeyConstant.EQUIPMENT_ADD.replace("${userId}", userId.toString()).replace("${equipmentId}", equipmentInfo.getOneLevelTypeId().toString());
        boolean judge = redisUtil.setIfAbsent(addKey, "1", 3L);
        if (!judge) {
            return R.fail(ResponseConstant.BINDING_PROGRESS);
        }

        equipmentInfo.setCreateBy(userId);
        equipmentInfo.setUserBy(userId);
        //判断该设备名称是否在该用户的数据中存在
        EquipmentInfo binding = getBindingOne(equipmentInfo, null, userId);
        if (binding != null && binding.getBindStatus().equals(1)) {
            return R.fail(ResponseConstant.BINDING_REPEAT);
        }

        //新增
        //通过名称设置一二级类型id
        if (CharSequenceUtil.isEmpty(equipmentInfo.getName())) {
            return R.fail(ResponseConstant.BINDING_FAIL);
        }
        //去数据库中查出对应型号数据
        List<EquipmentType> equipmentTypes;
        if (equipmentInfo.getOneLevelTypeId().equals(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_41)) {
            equipmentTypes = List.of(equipmentTypeService.getById(equipmentInfo.getTwoLevelTypeId()));
        } else {
            //获取蓝牙名称中的型号
            equipmentTypes = equipmentTypeService.getModel(equipmentInfo.getName(), equipmentInfo.getOneLevelTypeId());
        }

        if (CollUtil.isEmpty(equipmentTypes)) {
            return R.fail(ResponseConstant.BINDING_SUPPORTED);
        }
        //柏群如果不传递一级类型id会查询出多条信息，取第一条信息，正常情况只会存在一条信息
        EquipmentType equipmentType = equipmentTypes.get(0);
        //二级类型id
        if (equipmentType.getId() == null) {
            return R.fail(ResponseConstant.BINDING_SUPPORTED);
        }
        equipmentInfo.setTwoLevelTypeId(equipmentType.getId());
        //一级类型id
        if (equipmentType.getParentId() == null) {
            return R.fail(ResponseConstant.BINDING_SUPPORTED);
        }
        equipmentInfo.setOneLevelTypeId(equipmentType.getParentId());
        //如果存在则修改
        LocalDateTime now = LocalDateTime.now();
        //查询ota类型
        List<FirmwareVersion> firmwareversionlist = getFirmwareVersions(equipmentInfo.getRemark(), equipmentType.getId());
        if (binding == null) {
            //绑定状态为1
            equipmentInfo.setBindStatus(1);
            equipmentInfo.setUpdateTime(now);
            equipmentInfo.setConnectTime(now);
            equipmentInfo.setBindTime(now);
            if (equipmentType.getCommunicationProtocol() != null) {
                equipmentInfo.setCommunicationProtocol(equipmentType.getCommunicationProtocol());
            }
            if (equipmentType.getEigenValue() != null) {
                equipmentInfo.setEigenValue(equipmentType.getEigenValue());
            }
            equipmentInfo.setTwoLevelImage(equipmentType.getTypeImages());
            FirmwareVersion firmwareVersion = null;
            if (CollUtil.isNotEmpty(firmwareversionlist)) {
                firmwareVersion = firmwareversionlist.get(0);
                equipmentInfo.setOtaType(firmwareVersion.getOtaType());
            }
            int count = equipmentInfoService.count("user_by", ConditionEnum.EQ, userId);
            boolean saveStatus = equipmentInfoService.save(equipmentInfo);
            if (saveStatus) {
                //第一次绑定用户赠送十天会员
                if (count == 0) {
                    //赠送十天会员
                    OpenVipCmd openVipCmd = new OpenVipCmd()
                            .setUserId(userId)
                            .setType(VipFlowTypeEnum.NEWBIE.getCode())
                            .setTitle(VipFlowTypeEnum.NEWBIE.getDesc())
                            .setPackageType(null)
                            .setVipType(VipTypeEnum.VIP.getCode())
                            .setOperationType(VipFlowOperationTypeEnum.OPEN.getCode())
                            .setDays(10)
                            .setTerminal(equipmentInfo.getTerminal())
                            .setBizId(null)
                            .setSubAccount(true);
                    memberApi.openVip(openVipCmd);
                    String redisKey = RedisKeyConstant.USER_GUIDE_MEMBER_BANNER + userId;
                    redisTemplate.opsForValue().set(redisKey, "0");
                }
                //上传任务数据
                uploadTaskData(equipmentInfo, userId, now);
                UpdateEquipVO updateEquip = getUpdateEquipVO(equipmentType, firmwareVersion);
                updateEquip.setOneLevelTypeId(equipmentInfo.getOneLevelTypeId());
                updateEquip.setTwoLevelTypeId(equipmentInfo.getTwoLevelTypeId());
                updateEquip.setId(equipmentInfo.getId());
                if (StrUtil.isNotBlank(equipmentInfo.getCode())) {
                    updateEquip.setCode(equipmentInfo.getCode());
                }
                updateEquip.setName(equipmentInfo.getName());
                BindDeviceDTO bindDeviceDTO = equipmentInfoService.doubleWrite(equipmentType, equipmentInfo);
                if (null != bindDeviceDTO) {
                    updateEquip.setProductKey(bindDeviceDTO.getProductKey());
                    updateEquip.setDeviceName(bindDeviceDTO.getDeviceName());
                    updateEquip.setDeviceSecret(bindDeviceDTO.getDeviceSecret());
                }
                return R.ok(updateEquip);
            } else {
                return R.fail(ResponseConstant.BINDING_FAIL);
            }
        }

        if (StrUtil.isNotBlank(equipmentInfo.getCode())) {
            binding.setCode(equipmentInfo.getCode());
        }
        binding.setBindStatus(1);
        binding.setTwoLevelTypeId(equipmentType.getId());
        binding.setOneLevelTypeId(equipmentType.getParentId());
        if (equipmentType.getCommunicationProtocol() != null) {
            binding.setCommunicationProtocol(equipmentType.getCommunicationProtocol());
        }
        if (equipmentType.getEigenValue() != null) {
            binding.setEigenValue(equipmentType.getEigenValue());
        }
        FirmwareVersion firmwareVersion = null;
        if (CollUtil.isNotEmpty(firmwareversionlist)) {
            firmwareVersion = firmwareversionlist.get(0);
            binding.setOtaType(firmwareVersion.getOtaType());
        }
        binding.setTwoLevelImage(equipmentType.getTypeImages());
        binding.setConnectTime(now);
        binding.setBindTime(now);
        boolean updateStatus = equipmentInfoService.updateById(binding);
        if (updateStatus) {
            //维护设备信息
//            userFeign.keepEquipmentNum(SessionUtil.getId(), BaseConstant.INT_TRUE);
            UpdateEquipVO updateEquip = getUpdateEquipVO(equipmentType, firmwareVersion);
            updateEquip.setOneLevelTypeId(binding.getOneLevelTypeId());
            updateEquip.setTwoLevelTypeId(binding.getTwoLevelTypeId());
            updateEquip.setId(binding.getId());
            if (StrUtil.isNotBlank(binding.getCode())) {
                updateEquip.setCode(binding.getCode());
            }
            updateEquip.setName(binding.getName());
            BindDeviceDTO bindDeviceDTO = equipmentInfoService.doubleWrite(equipmentType, equipmentInfo);
            if (null != bindDeviceDTO) {
                updateEquip.setProductKey(bindDeviceDTO.getProductKey());
                updateEquip.setDeviceName(bindDeviceDTO.getDeviceName());
                updateEquip.setDeviceSecret(bindDeviceDTO.getDeviceSecret());
            }
            return R.ok(updateEquip);
        } else {
            return R.fail(ResponseConstant.BINDING_FAIL);
        }

    }

    /**
     * 新增设备信息v2
     *
     * @param equipmentInfo 设备信息
     * @return R 所有设备信息
     */
    @PostMapping("equipment/v2")
    public R saveEquipmentv2(@RequestBody @Valid EquipmentInfo equipmentInfo) {
        return equipmentInfoService.bind(equipmentInfo);
    }

    /**
     * 新增设备信息v3
     *
     * @param equipmentInfo 设备信息
     * @return R 所有设备信息
     */
    @PostMapping("equipment/v3")
    @Anonymous
    public R saveEquipmentv3(@RequestBody @Valid EquipmentInfo equipmentInfo) {
        return equipmentInfoService.bind(equipmentInfo);
    }

    private void uploadTaskData(EquipmentInfo equipmentInfo, Long userId, LocalDateTime now) {
        TaskForm taskForm = new TaskForm();
        taskForm.setEquipmentType(equipmentInfo.getOneLevelTypeId());
        taskForm.setType(TaskStrategyEnum.BIND_EQUIP.getType());
        taskForm.setNow(now);
        taskForm.setBusinessValue(equipmentInfo.getId().toString());
        taskForm.setUserId(userId);
        userTaskApi.uploadTaskData(taskForm);
    }

    private UpdateEquipVO getUpdateEquipVO(EquipmentType equipmentType, FirmwareVersion firmwareVersion) {
        UpdateEquipVO updateEquip = new UpdateEquipVO();
        updateEquip.setEigenValue(equipmentType.getEigenValue());
        updateEquip.setCommunicationProtocol(equipmentType.getCommunicationProtocol());
        updateEquip.setTwoLevelImage(equipmentType.getTypeImages());
        if (firmwareVersion != null) {
            updateEquip.setOtaType(firmwareVersion.getOtaType());
        }
        return updateEquip;
    }

    /**
     * 绑定的数据
     *
     * @param equipmentInfo 设备信息
     * @param bindStatus    绑定状态
     * @param userId        用户id
     * @return 绑定的数据
     */
    public EquipmentInfo getBindingOne(EquipmentInfo equipmentInfo, Integer bindStatus, Long userId) {
        BaseQuery<EquipmentInfo> baseQuery = new BaseQuery<>();
        if (bindStatus != null) {
            baseQuery.eq("bind_status", bindStatus);
        }
        baseQuery.eq("name", equipmentInfo.getName()).eq("user_by", userId);
        List<EquipmentInfo> list = equipmentInfoService.list(baseQuery);
        if (list.size() > 1) {
            List<EquipmentInfo> sorted = list.stream().sorted(Comparator.comparing(EquipmentInfo::getCreateTime).reversed()).collect(Collectors.toList());
            equipmentInfoService.deleteById(sorted.get(0).getId());
            return list.get(0);
        } else if (list.size() == 1) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 修改设备信息(解绑)
     *
     * @param id 设备id
     * @return R
     */
    @PutMapping("disconnectionDevice")
    public ResDTO<Boolean> disconnectionDevice(@RequestBody Long id) {
        log.info("equipmentInfo->id:{}", id);
        if (null == id) {
            return ResDTO.ok(Boolean.FALSE);
        }
        return ResDTO.ok(equipmentInfoService.disconnectionDevice(id));
    }

    /**
     * 修改设备信息
     *
     * @param equipmentInfo 设备信息
     * @return R
     */
    @PutMapping("updateEquipmentById")
    @Anonymous
    public Boolean updateEquipmentById(@RequestBody EquipmentInfo equipmentInfo) {
        if (equipmentInfo.getId() == null) {
            return Boolean.TRUE;
        }
        equipmentInfo.setUpdateBy(SessionUtil.getId());
        return equipmentInfoService.updateById(equipmentInfo);
    }

    /**
     * 修改设备信息(解绑、连接时间修改,版本信息)
     *
     * @param equipmentInfo 设备信息
     * @return R
     */
    @PutMapping("equipment")
    public ResDTO<UpdateEquipVO> updateEquipment(@RequestBody @Valid EquipmentInfo equipmentInfo) {
        if (equipmentInfo.getId() == null) {
            return ResDTO.fail(ResponseConstant.PARAM_ERROR);
        }
        Long userId = SessionUtil.getId();
        equipmentInfo.setUpdateBy(userId);
        LocalDateTime now = LocalDateTime.now();
        equipmentInfo.setUpdateTime(now);
        if (equipmentInfo.getUpdateType() != null && equipmentInfo.getUpdateType().equals(1)) {
            equipmentInfo.setConnectTime(now);
        }
        boolean updateStatus = equipmentInfoService.updateById(equipmentInfo);
        if (updateStatus) {
            EquipmentInfo oldEqipment = equipmentInfoService.getById(equipmentInfo.getId());
            boolean unbind = equipmentInfo.getBindStatus() != null && equipmentInfo.getBindStatus().equals(0);
            if (unbind) {
                UnbindDeviceCmd unbindDevice = new UnbindDeviceCmd();
                unbindDevice.setDeviceUserRelId(equipmentInfo.getId());
                Boolean isUnbind = deviceApi.unbindDevice(unbindDevice);
                log.info("设备id，{},解绑结果{}", equipmentInfo.getId(), isUnbind);
            }

            UpdateEquipVO updateEquip = new UpdateEquipVO();
            if (oldEqipment == null) {
                return ResDTO.ok(updateEquip);
            }
            EquipmentType type = equipmentTypeService.getById(oldEqipment.getTwoLevelTypeId());
            updateEquip.setEigenValue(type.getEigenValue());
            String remark = StrUtil.isBlank(equipmentInfo.getRemark()) ? oldEqipment.getRemark() : equipmentInfo.getRemark();
            updateEquip.setCommunicationProtocol(type.getCommunicationProtocol());
            if (StrUtil.isNotBlank(remark)) {
                JSONObject jsonObject = JSONObject.parseObject(remark);
                String meritEigenValue = jsonObject.getString(EquipmentConstant.MERIT_EIGEN_VALUE);
                type = equipmentTypeService.equipmentTypeChange(type, meritEigenValue, oldEqipment.getName());
                updateEquip.setCommunicationProtocol(type.getCommunicationProtocol());
                if (!unbind) {
                    equipmentInfoService.doubleWrite(type, oldEqipment);
                }
            }
            List<FirmwareVersion> firmwareversionlist = getFirmwareVersions(remark, type.getId());
            if (CollUtil.isNotEmpty(firmwareversionlist)) {
                updateEquip.setOtaType(firmwareversionlist.get(0).getOtaType());
            }
            updateEquip.setOneLevelTypeId(oldEqipment.getOneLevelTypeId());
            updateEquip.setTwoLevelTypeId(oldEqipment.getTwoLevelTypeId());
            updateEquip.setId(oldEqipment.getId());
            return ResDTO.ok(updateEquip);
        } else {
            return ResDTO.fail(ResponseConstant.UPDATE_FAIL);
        }
    }

    private List<FirmwareVersion> getFirmwareVersions(String remark, Long id) {
        BaseQuery<FirmwareVersion> baseQuery = new BaseQuery<>();
        baseQuery.eq("equip_model_id", id);
        if (StrUtil.isNotBlank(remark)) {
            JSONObject jsonObject = JSONObject.parseObject(remark);
            String agreementEigenValue = jsonObject.getString("2A24");
            if (StrUtil.isNotBlank(agreementEigenValue)) {
                baseQuery.eq("code", agreementEigenValue);
            }
        }
        return firmwareVersionService.list(baseQuery);
    }

    /**
     * 删除设备信息
     *
     * @param id 信息id
     * @return R
     */
    @DeleteMapping("/equipment")
    public R delEquipment(@RequestBody List<Long> id) {
        boolean delStatus = false;
        if (id != null) {
//            userFeign.keepEquipmentNum(SessionUtil.getId(), BaseConstant.INT_FALSE);
            delStatus = equipmentInfoService.removeByIds(id);
        }
        if (delStatus) {
            return R.ok();
        } else {
            return R.fail(ResponseConstant.DEL_FAIL);
        }
    }


    /**
     * 设备分页信息
     *
     * @param param 参数
     * @return R
     */
    @GetMapping("equipmentPage")
    public R getEquipmentPage(@RequestParam Map<String, Object> param) {
        Long userId = SessionUtil.getId();
        if (userId == null) {
            return R.fail(ResponseConstant.COMMON_NOUSER);
        }
        Integer current = MapUtil.getInt(param, "current", 1);
        Integer size = MapUtil.getInt(param, "size", 10);
        BaseQuery<EquipmentInfo> condition = new BaseQuery<>();
        IPage<EquipmentInfo> page = new Page<>(current, size);
        Integer oneLevelTypeId = MapUtil.getInt(param, "oneLevelTypeId");
        if (oneLevelTypeId != null) {
            condition.eq("one_level_type_id", oneLevelTypeId);
        }
        String code = MapUtil.getStr(param, "code");
        if (StrUtil.isNotBlank(code)) {
            condition.eq("code", code);
        }
        Integer type = MapUtil.getInt(param, "type");
        if (type != null) {
            List<Integer> typeList;
            if (type.equals(HealthConstant.HEALTH_TYPE)) {
                typeList = List.of(HealthConstant.HEALTH_TYPE);
            } else {
                typeList = HealthConstant.SPORTS_TYPE_LIST;
            }
            BaseQuery<EquipmentType> infoBaseQuery = new BaseQuery<>();
            infoBaseQuery.in("type", typeList).eq("level", 0);
            List<EquipmentType> list = equipmentTypeService.list(infoBaseQuery);
            condition.in("one_level_type_id", list.stream().map(EquipmentType::getId).collect(Collectors.toList()));
        }
        //查询已绑定的
        condition.eq("bind_status", 1).eq("user_by", SessionUtil.getId());
        condition.orderByDesc("connect_time");
        page = equipmentInfoService.page(page, condition);
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<EquipmentInfo> redisInfo;
            redisInfo = getRedisInfo(page.getRecords());
            page.setRecords(redisInfo);
        }
        return R.ok(page);
    }

    /**
     * 通过typeid中获取一级,二级类型名称，二级图片信息
     *
     * @param infoList 设备信息集合
     * @return List<EquipmentInfo>
     */
    public List<EquipmentInfo> getRedisInfo(List<EquipmentInfo> infoList) {
        //获取redis中的类型数据
        Map<String, EquipmentType> equipmentType = redisUtil.getCacheMap(RedisKeyConstant.EQUIPMENT_TYPE_KEY);
        if (MapUtil.isEmpty(equipmentType)) {
            equipmentTypeService.refreshRedis();
            equipmentType = redisUtil.getCacheMap(RedisKeyConstant.EQUIPMENT_TYPE_KEY);
        }

        //获取ota类型
        List<Long> modelIds = infoList.stream().map(EquipmentInfo::getTwoLevelTypeId).collect(Collectors.toList());
        Map<Long, List<FirmwareVersion>> firmwareMap = firmwareVersionService.list("equip_model_id", ConditionEnum.IN, modelIds).stream().collect(Collectors.groupingBy(FirmwareVersion::getEquipModelId));
        for (EquipmentInfo info : infoList) {
            if (info != null && info.getTwoLevelTypeId() != null && equipmentType.containsKey(String.valueOf(info.getTwoLevelTypeId()))) {
                //获取二级分类信息
                EquipmentType typeInfo = JSON.parseObject(String.valueOf(equipmentType.get(String.valueOf(info.getTwoLevelTypeId()))), EquipmentType.class);
                if (equipmentType.containsKey(String.valueOf(typeInfo.getParentId()))) {
                    //二级分类名称
                    if (StrUtil.isNotEmpty(typeInfo.getTypeName())) {
                        info.setTwoLevelTypeName(typeInfo.getTypeName());
                    }
                    //版本判断
                    String remark = info.getRemark();
                    if (StrUtil.isNotBlank(remark)) {
                        JSONObject jsonObject = JSONObject.parseObject(remark);
                        String meritEigenValue = jsonObject.getString(EquipmentConstant.MERIT_EIGEN_VALUE);
                        typeInfo = equipmentTypeService.equipmentTypeChange(typeInfo, meritEigenValue, info.getName());
                    }
                    info.setIsOta(typeInfo.getIsOta());
                    info.setIsMerach(typeInfo.getIsElectromagneticControl());
                    //二级图片地址
                    if (StrUtil.isNotEmpty(typeInfo.getTypeImages())) {
                        info.setTwoLevelImage(typeInfo.getTypeImages());
                    }
                    //帮助中心
                    if (StrUtil.isNotEmpty(typeInfo.getHelpCenter())) {
                        info.setHelpCenter(typeInfo.getHelpCenter());
                    }
                    //产品说明（http）
                    if (StrUtil.isNotEmpty(typeInfo.getProductManual())) {
                        info.setProductManual(typeInfo.getProductManual());
                    }
                    //获取一级分类信息
                    EquipmentType parentInfo = JSON.parseObject(String.valueOf(equipmentType.get(String.valueOf(typeInfo.getParentId()))), EquipmentType.class);
                    info.setOneLevelTypeName(parentInfo.getTypeName());
                    //通信协议
                    if (typeInfo.getCommunicationProtocol() != null) {
                        info.setCommunicationProtocol(typeInfo.getCommunicationProtocol());
                    }
                    //特征值
                    if (typeInfo.getCommunicationProtocol() != null) {
                        info.setEigenValue(typeInfo.getEigenValue());
                    }
                    //获取ota类型
                    if (MapUtil.isNotEmpty(firmwareMap)) {
                        List<FirmwareVersion> versionList = firmwareMap.get(info.getTwoLevelTypeId());
                        if (CollUtil.isNotEmpty(versionList)) {
                            info.setOtaType(versionList.get(0).getOtaType());
                        }
                    }
                }
            }
        }
        return infoList;
    }

    /**
     * 设备信息集合
     *
     * @return R
     */
    @GetMapping("equipmentList")
    public R getEquipmentList() {
        BaseQuery<EquipmentInfo> condition = new BaseQuery<>();
        condition.orderByDesc("id");
        List<EquipmentInfo> list = equipmentInfoService.list(condition);
        return R.ok(list);
    }

    /**
     * 获取单条设备信息
     *
     * @param id id
     * @return 单条设备具体信息
     */
    @GetMapping("equipmentInfo")
    public R getEquipmentTypeById(Long id) {
        EquipmentInfo info = new EquipmentInfo();
        if (id != null) {
            info = equipmentInfoService.getById(id);
            //获取redis中的类型数据
            Map<String, EquipmentType> equipmentType = redisUtil.getCacheMap(RedisKeyConstant.EQUIPMENT_TYPE_KEY);
            if (info != null && info.getTwoLevelTypeId() != null && equipmentType.containsKey(String.valueOf(info.getTwoLevelTypeId()))) {
                //获取二级分类信息
                EquipmentType typeInfo = JSON.parseObject(String.valueOf(equipmentType.get(String.valueOf(info.getTwoLevelTypeId()))), EquipmentType.class);
                if (equipmentType.containsKey(String.valueOf(typeInfo.getParentId()))) {
                    //二级分类名称
                    if (StrUtil.isNotEmpty(typeInfo.getTypeName())) {
                        info.setTwoLevelTypeName(typeInfo.getTypeName());
                    }

                    //二级图片地址
                    if (StrUtil.isNotEmpty(typeInfo.getTypeImages())) {
                        info.setTwoLevelImage(typeInfo.getTypeImages());
                    }
                    //产品说明（http）
                    if (StrUtil.isNotEmpty(typeInfo.getProductManual())) {
                        info.setProductManual(typeInfo.getProductManual());
                    }
                    //帮助中心（http）
                    if (StrUtil.isNotEmpty(typeInfo.getHelpCenter())) {
                        info.setHelpCenter(typeInfo.getHelpCenter());
                    }
                    //获取一级分类信息
                    EquipmentType parentInfo = JSONObject.parseObject(String.valueOf(equipmentType.get(String.valueOf(typeInfo.getParentId()))), EquipmentType.class);
                    info.setOneLevelTypeName(parentInfo.getTypeName());
                }
            }
        }
        return R.ok(info);
    }


    /**
     * 推荐课程-用户选择课程关联设备
     *
     * @param userId 用户id
     * @return 用户设备信息
     */
    @Anonymous
    @GetMapping("topEquipByUserId")
    public R topEquipByUserId(Long userId) {
        if (userId == null) {
            return R.fail(ResponseConstant.COMMON_NOUSER);
        }
        QueryWrapper<EquipmentInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("MAX(update_time) update_time", "one_level_type_id").eq("user_by", userId).eq("bind_status", 1).groupBy("one_level_type_id").orderByDesc("update_time");
        return R.ok(equipmentInfoService.list(queryWrapper));
    }

    /**
     * 根据用户id 获取该用户设备信息（id，和 顶级类型名称）
     *
     * @param userId 用户id
     * @return 用户设备信息
     */
    @Anonymous
    @GetMapping("getNameByUserId")
    public R getNameByUserId(Long userId) {
        if (userId == null) {
            return R.fail(ResponseConstant.COMMON_NOUSER);
        }
        List<EquipmentInfo> infoList;
        infoList = equipmentInfoService.getNameByUserId(userId);
        if (CollUtil.isEmpty(infoList)) {
            return R.ok(infoList);
        }
        //获取redis中的类型数据
        List<EquipmentTypeDTO> returnInfo = getRedisEquipType(infoList);

        return R.ok(returnInfo);
    }

    /**
     * app-新首页顶级类型查询
     *
     * @return 首页类型
     */
    @GetMapping("homeEquipmentList")
    public R homeEquipmentList() {
        Long userId = SessionUtil.getId();
        if (userId == null) {
            return R.fail(ResponseConstant.COMMON_NOUSER);
        }
        //转换好的数据
        List<EquipmentTypeDTO> redisEquipType = new ArrayList<>();
        List<EquipmentInfo> infoList;
        //根据用户id查询用户所有绑定的设备
        BaseQuery<EquipmentInfo> baseQuery = new BaseQuery<>();
        baseQuery.eq("bind_status", 1).eq("user_by", userId);
        infoList = equipmentInfoService.list(baseQuery);
        //用户没有设备信息
        if (CollUtil.isNotEmpty(infoList)) {
            //用户存在设备
            Integer total = infoList.size();
            //给codes和equipIds 赋值
            infoList.forEach(item -> {
                List<Long> ids = new ArrayList<>();
                ids.add(item.getId());
                List<String> codes = new ArrayList<>();
                if (StrUtil.isNotEmpty(item.getCode())) {
                    codes.add(item.getCode());
                }
                item.setEquipIds(ids);
                item.setCodes(codes);
            });

            //如果一级类型相同,去除重复的数据合并一条
            List<EquipmentInfo> collect = infoList.stream()
                    // 表示id为key， 接着如果有重复的，那么从o1与o2中筛选出一个，这里选择o1，
                    .collect(Collectors.toMap(EquipmentInfo::getOneLevelTypeId, a -> a, (o1, o2) -> {
                        if (o1.getBindTime().isBefore(o2.getBindTime())) {
                            o1.setBindTime(o2.getBindTime());
                        }
                        List<Long> ids = new ArrayList<>();
                        ids.add(o1.getId());
                        ids.add(o2.getId());
                        List<String> codes = new ArrayList<>();
                        if (StrUtil.isNotEmpty(o1.getCode())) {
                            codes.add(o1.getCode());
                        }
                        if (StrUtil.isNotEmpty(o2.getCode())) {
                            codes.add(o2.getCode());
                        }
                        o1.setEquipIds(ids);
                        o1.setCodes(codes);
                        return o1;
                    })).values().stream().sorted(Comparator.comparing(EquipmentInfo::getBindTime).reversed()).collect(Collectors.toList());
            //转换
            redisEquipType = getRedisEquipType(collect);
            //对id，code集合进行赋值
            redisEquipType.forEach(item -> collect.forEach(info -> {
                        if (item.getId().equals(info.getOneLevelTypeId())) {
                            item.setEquipIds(info.getEquipIds());
                            if (CollUtil.isNotEmpty(info.getCodes())) {
                                item.setCode(info.getCodes());
                            }
                        }
                    }
            ));

            Map<String, EquipmentType> equipmentType = redisUtil.getCacheMap(RedisKeyConstant.EQUIPMENT_TYPE_KEY);
            //放入详细信息
            redisEquipType.forEach(item -> {
                List<EquipInfoDTO> equipInfoDTOList = new ArrayList<>();
                infoList.forEach(info -> {
                    if (item.getId().equals(info.getOneLevelTypeId())) {
                        EquipInfoDTO equipInfoDto = new EquipInfoDTO();
                        if (StrUtil.isNotEmpty(info.getCode())) {
                            equipInfoDto.setCode(info.getCode());
                        }
                        equipInfoDto.setEquipId(info.getId());
                        equipInfoDto.setOneLevelTypeId(info.getOneLevelTypeId());
                        equipInfoDto.setConnectTime(info.getConnectTime());
                        equipInfoDto.setTwoLevelTypeId(info.getTwoLevelTypeId());
                        equipInfoDto.setName(info.getName());
                        EquipmentType typeInfo = JSON.parseObject(String.valueOf(equipmentType.get(String.valueOf(info.getTwoLevelTypeId()))), EquipmentType.class);
                        if (typeInfo != null) {
                            equipInfoDto.setTwoLevelImage(typeInfo.getTypeImages());
                        }
                        equipInfoDto.setBluetoothAlias(info.getBluetoothAlias());
                        equipInfoDTOList.add(equipInfoDto);
                    }
                });
                List<EquipInfoDTO> dtos = equipInfoDTOList.stream().filter(v -> v.getConnectTime() != null).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(dtos)) {
                    EquipInfoDTO lastEquipInfoDTO = dtos.stream().sorted(Comparator.comparing(EquipInfoDTO::getConnectTime).reversed()).collect(Collectors.toList()).get(0);
                    item.setTwoLevelImage(lastEquipInfoDTO.getTwoLevelImage());
                    item.setBluetoothAlias(lastEquipInfoDTO.getBluetoothAlias());
                    item.setEquipId(lastEquipInfoDTO.getEquipId());
                    item.setName(lastEquipInfoDTO.getName());
                }
                item.setEquipInfo(equipInfoDTOList);
                item.setTotal(total);
            });
            //将体脂称放最后
            EquipmentTypeDTO scale = redisEquipType.stream().filter(v -> v.getId().equals(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_41)).findFirst().orElse(null);
            if (scale != null) {
                redisEquipType.removeIf(v -> v.getId().equals(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_41));
                redisEquipType.add(scale);
            }
            redisEquipType.removeIf(v -> v.getId().equals(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_100));
        }
        return R.ok(redisEquipType);
    }

    /**
     * 用户设备详情
     *
     * @return 首页类型
     */
    @GetMapping("getBluetoothAlias")
    public R getBluetoothAlias(Long equipId) {
        Dict dict = new Dict();
        if (equipId == null) {
            return R.fail(ResponseConstant.EQUIP_ILLEGAL);
        }
        EquipmentInfo equipmentInfo = equipmentInfoService.getById(equipId);
        if (equipmentInfo == null) {
            return R.fail(ResponseConstant.EQUIP_INVALID);
        }
        dict.put("bluetoothAlias", equipmentInfo.getBluetoothAlias());
        EquipmentType equipmentType = equipmentTypeService.getById(equipmentInfo.getTwoLevelTypeId());
        dict.put("twoLevelImage", equipmentType.getTypeImages());
        dict.put("name", equipmentInfo.getName());
        return R.ok(dict);
    }

    /**
     * 通过用户id和分类获取不同的数据(首页顶级类型查询)
     *
     * @return 首页类型
     */
    @GetMapping("getEquipInfoByUserId")
    @Deprecated
    public R getEquipNameByUserId() {
        Long userId = SessionUtil.getId();
        if (userId == null) {
            return R.fail(ResponseConstant.COMMON_NOUSER);
        }
        //转换好的数据
        List<EquipmentTypeDTO> redisEquipType;
        List<EquipmentInfo> infoList;
        //根据用户id查询用户所有绑定的设备
        BaseQuery<EquipmentInfo> baseQuery = new BaseQuery<>();
        baseQuery.eq("bind_status", 1).eq("user_by", userId);
        infoList = equipmentInfoService.list(baseQuery);
        //用户没有设备信息
        if (CollUtil.isEmpty(infoList)) {
            //返回所有的一级分类信息
            BaseQuery<EquipmentType> condition = new BaseQuery<>();
            condition.select("id", "type_name", "sort").eq("level", 0).eq("type", 1).or().eq("is_show", 1);
            List<EquipmentType> list = equipmentTypeService.list(condition);
            redisEquipType = list.stream().map(item -> {
                EquipmentTypeDTO equipmentTypeDto = new EquipmentTypeDTO();
                equipmentTypeDto.setId(item.getId());
                equipmentTypeDto.setTypeName(item.getTypeName());
                equipmentTypeDto.setSort(item.getSort());
                equipmentTypeDto.setTotal(0);
                return equipmentTypeDto;
            }).collect(Collectors.toList());
        } else {
            //用户存在设备
            Integer total = infoList.size();

            //给codes和equipIds 赋值
            infoList.forEach(item -> {
                List<Long> ids = new ArrayList<>();
                ids.add(item.getId());
                List<String> codes = new ArrayList<>();
                if (StrUtil.isNotEmpty(item.getCode())) {
                    codes.add(item.getCode());
                }
                item.setEquipIds(ids);
                item.setCodes(codes);
            });

            //如果一级类型相同,去除重复的数据合并一条
            List<EquipmentInfo> collect = infoList.stream()
                    // 表示id为key， 接着如果有重复的，那么从o1与o2中筛选出一个，这里选择o1，
                    .collect(Collectors.toMap(EquipmentInfo::getOneLevelTypeId, a -> a, (o1, o2) -> {
                        if (o1.getBindTime().isBefore(o2.getBindTime())) {
                            o1.setBindTime(o2.getBindTime());
                        }
                        List<Long> ids = new ArrayList<>();
                        ids.add(o1.getId());
                        ids.add(o2.getId());
                        List<String> codes = new ArrayList<>();
                        if (StrUtil.isNotEmpty(o1.getCode())) {
                            codes.add(o1.getCode());
                        }
                        if (StrUtil.isNotEmpty(o2.getCode())) {
                            codes.add(o2.getCode());
                        }
                        o1.setEquipIds(ids);
                        o1.setCodes(codes);
                        return o1;
                    })).values().stream().sorted(Comparator.comparing(EquipmentInfo::getBindTime).reversed()).collect(Collectors.toList());
            //转换
            redisEquipType = getRedisEquipType(collect);
            //对id，code集合进行赋值
            redisEquipType.forEach(item -> collect.forEach(info -> {
                        if (item.getId().equals(info.getOneLevelTypeId())) {
                            item.setEquipIds(info.getEquipIds());
                            if (CollUtil.isNotEmpty(info.getCodes())) {
                                item.setCode(info.getCodes());
                            }
                        }
                    }
            ));

            //放入详细信息
            redisEquipType.forEach(item -> {
                List<EquipInfoDTO> equipInfoDTOList = new ArrayList<>();
                infoList.forEach(info -> {
                    if (item.getId().equals(info.getOneLevelTypeId())) {
                        EquipInfoDTO equipInfoDto = new EquipInfoDTO();
                        if (StrUtil.isNotEmpty(info.getCode())) {
                            equipInfoDto.setCode(info.getCode());
                        }
                        equipInfoDto.setEquipId(info.getId());
                        equipInfoDto.setOneLevelTypeId(info.getOneLevelTypeId());
                        equipInfoDto.setTwoLevelTypeId(info.getTwoLevelTypeId());
                        equipInfoDto.setName(info.getName());
                        equipInfoDTOList.add(equipInfoDto);
                    }
                });
                item.setEquipInfo(equipInfoDTOList);
                item.setTotal(total);
            });
        }
        EquipmentTypeDTO otherInfoDto = new EquipmentTypeDTO();
        EquipmentType otherInfo = equipmentTypeService.getOtherInfo();
        otherInfoDto.setId(otherInfo.getId());
        otherInfoDto.setTypeName(otherInfo.getTypeName());
        otherInfoDto.setSort(otherInfo.getSort());
        redisEquipType.add(otherInfoDto);
        return R.ok(redisEquipType);
    }

    /**
     * 从redis中获取一级类型名称
     *
     * @param infoList 设备信息集合
     * @return 类型信息
     */
    public List<EquipmentTypeDTO> getRedisEquipType(List<EquipmentInfo> infoList) {
        //获取redis中的类型数据
        Map<String, EquipmentType> equipmentType = redisUtil.getCacheMap(RedisKeyConstant.EQUIPMENT_TYPE_KEY);
        List<EquipmentTypeDTO> returnInfo = new ArrayList<>();
        for (EquipmentInfo info : infoList) {
            if (info != null && info.getTwoLevelTypeId() != null && equipmentType.containsKey(String.valueOf(info.getTwoLevelTypeId()))) {
                //获取二级分类信息
                EquipmentType typeInfo = JSONObject.parseObject(String.valueOf(equipmentType.get(String.valueOf(info.getTwoLevelTypeId()))), EquipmentType.class);
                if (equipmentType.containsKey(String.valueOf(typeInfo.getParentId()))) {
                    EquipmentTypeDTO equipmentTypeDto = new EquipmentTypeDTO();
                    //二级分类名称
                    equipmentTypeDto.setTwoLevelTypeName(typeInfo.getTypeName());
                    //获取一级分类信息
                    EquipmentType parentInfo = JSONObject.parseObject(String.valueOf(equipmentType.get(String.valueOf(typeInfo.getParentId()))), EquipmentType.class);
                    equipmentTypeDto.setId(parentInfo.getId());
                    equipmentTypeDto.setTypeName(parentInfo.getTypeName());
                    equipmentTypeDto.setName(info.getName());
                    equipmentTypeDto.setSort(parentInfo.getSort());
                    equipmentTypeDto.setBindStatus(info.getBindStatus());
                    equipmentTypeDto.setUserVersion(info.getUserVersion());
                    returnInfo.add(equipmentTypeDto);
                }
            }
        }
        return returnInfo;
    }

    @GetMapping("/getNameByUserId/v1")
    public R getNameByUserId() {
        return this.getNameByUserId(SessionUtil.getId());
    }

    /**
     * 通过大类id获取所有用户id
     *
     * @param typeId 大类id
     * @return 用户id
     */
    @GetMapping("getUserByOneLevel")
    @Anonymous
    public Set<String> getUserByOneLevel(String typeId) {
        BaseQuery<EquipmentInfo> condition = new BaseQuery<>();
        condition.select("user_by").eq("one_level_type_id", typeId).eq("bind_status", 1);
        List<EquipmentInfo> list = equipmentInfoService.list(condition);
        return list.stream().map(info -> String.valueOf(info.getUserBy())).collect(Collectors.toSet());
    }


    /**
     * 判断型号，获取图片，以及该设备与用户的绑定状态(搜索设备)
     *
     * @param params 参数信息
     * @return 型号
     */
    @GetMapping("getBinding")
    public R getBinding(@RequestParam Map<String, String> params) {
        //设备名称，以,分割
        String equipNames = MapUtil.getStr(params, "equipName");
        //一级分类id
        String oneLevelTypeId = MapUtil.getStr(params, "oneLevelTypeId");
        //型号id
        Long equipModelId = MapUtil.getLong(params, "equipModelId");
        //nfc设备搜索逻辑
        Integer isNfc = MapUtil.getInt(params, "isNfc", BaseConstant.INT_FALSE);

        String code = MapUtil.getStr(params, "code");
        if (StrUtil.isBlank(equipNames)) {
            return R.fail(ResponseConstant.PARAM_ERROR);
        }
        log.warn("equipNames:{},oneLevelTypeId:{},code:{}", equipNames, oneLevelTypeId, code);
        List<String> equipName = Arrays.asList(equipNames.split(","));
        List<EquipBindingVO> result = new ArrayList<>();
        equipName.forEach(name -> {
            EquipBindingVO equipBindingVo = equipmentTypeService.addEquipBindingVo(name, oneLevelTypeId);
            log.info("name:{},equipBindingVo:{}", name, JSONObject.toJSONString(equipBindingVo));
            if (equipBindingVo != null && equipBindingVo.getIsBinding().equals(BaseConstant.INT_FALSE) && StrUtil.isNotBlank(equipBindingVo.getTwoTypeName())) {
                if (StrUtil.isNotBlank(code)) {
                    equipBindingVo.setCode(code);
                }
                if (equipModelId == null) {
                    result.add(equipBindingVo);
                } else if (equipModelId.equals(equipBindingVo.getTwoTypeId())) {
                    result.add(equipBindingVo);
                }
            }
            if (equipBindingVo != null && isNfc.equals(BaseConstant.INT_TRUE)) {
                result.add(equipBindingVo);
            }

        });
        return R.ok(result);
    }


    /**
     * 根据用户id获取绑定设备信息
     */
    @GetMapping("getTypeByUserId")
    public R getTypeByUserId(@RequestParam("userId") String userId) {
        if (StrUtil.isEmpty(userId)) {
            return R.fail(ResponseConstant.COMMON_NOUSER);
        }
        BaseQuery<EquipmentInfo> baseQuery = new BaseQuery<>();
        baseQuery.eq("user_by", userId);
//        baseQuery.eq("bind_status", 1);
        List<EquipmentInfo> list = equipmentInfoService.list(baseQuery);
        List<EquipmentTypeDTO> redisEquipType = getRedisEquipType(list);
        return R.ok(redisEquipType);
    }

    /**
     * 通过设备名称及分类id获取所有设备信息
     *
     * @param params 参数信息
     * @return 型号
     */
    @GetMapping("getEquipTypeInfoByName")
    @Deprecated
    public R getEquipTypeInfoByName(@RequestParam Map<String, String> params) {
        //设备名称
        String equipNames = MapUtil.getStr(params, "equipName");
        //一级分类id
        String oneLevelTypeId = MapUtil.getStr(params, "oneLevelTypeId");

        if (StrUtil.isBlank(equipNames) || StrUtil.isBlank(oneLevelTypeId)) {
            return R.fail(ResponseConstant.PARAM_ERROR);
        }
        //根据名称和一级类型判断具体数据
        BaseQuery<EquipmentType> baseQuery = new BaseQuery<>();
        try {
            List<EquipmentType> types = equipmentTypeService.getModel(equipNames, Long.valueOf(oneLevelTypeId));
            if (CollUtil.isEmpty(types)) {
                return R.fail(ResponseConstant.UNABLE_LOCATE);
            }
            EquipmentType type = getOriginEquipmququentType(types);
            BaseQuery<EquipmentInfo> infoBaseQuery = new BaseQuery<>();
            infoBaseQuery.eq("name", equipNames).eq("two_level_type_id", type.getId()).eq("user_by", SessionUtil.getId());
            EquipmentInfo info = equipmentInfoService.getOne(infoBaseQuery);
            if (info != null && StrUtil.isNotBlank(info.getBluetoothAlias())) {
                type.setBluetoothAlias(info.getBluetoothAlias());
            }
            //速度
            if (type.getMinSpeed() == null) {
                type.setMinSpeed(0);
            }
            if (type.getMaxSpeed() == null) {
                type.setMaxSpeed(0);
            }
            //版本判断
            return R.ok(type);
        } catch (Exception e) {
            log.warn("无法定位具体型号:" + e.getMessage());
            return R.fail(ResponseConstant.UNABLE_LOCATE);
        }
    }

    /**
     * 通过设备名称及分类id获取所有设备信息
     *
     * @param params 参数信息
     * @return 型号
     */
    @GetMapping("getEquipTypeInfoByName/v2")
    public R getEquipTypeInfoByNameV2(@RequestParam Map<String, String> params) {
        //设备名称
        String equipNames = MapUtil.getStr(params, "equipName");
        //一级分类id
        String oneLevelTypeId = MapUtil.getStr(params, "oneLevelTypeId");

        if (StrUtil.isBlank(equipNames) || StrUtil.isBlank(oneLevelTypeId)) {
            return R.fail(ResponseConstant.PARAM_ERROR);
        }
        //根据名称和一级类型判断具体数据
        BaseQuery<EquipmentType> baseQuery = new BaseQuery<>();
        try {
            Map<String, Object> typeInfo = equipTypeUtil.getTypeCode(equipNames);
            String typeCode = MapUtil.getStr(typeInfo, "typeCode");
            Integer prefix = MapUtil.getInt(typeInfo, "prefix");
            if (prefix == null) {
                return R.fail(ResponseConstant.UNABLE_LOCATE);
            }
            if (StrUtil.isBlank(typeCode)) {
                baseQuery.eq("prefix_id", prefix).eq("parent_id", oneLevelTypeId).last("limit 1");
            } else {
                baseQuery.eq("code", typeCode).eq("prefix_id", prefix).last("limit 1");
            }
            List<EquipmentType> types = equipmentTypeService.list(baseQuery);
            EquipmentType type = null;
            if (CollUtil.isEmpty(types)) {
                //因安卓传错参数，后端做临时兼容 oneLevelTypeId 为设备id
                LambdaQueryWrapper<EquipmentInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(EquipmentInfo::getId, oneLevelTypeId).eq(EquipmentInfo::getUserBy, SessionUtil.getId());
                EquipmentInfo info = equipmentInfoService.getOne(lambdaQueryWrapper);
                if (null == info) {
                    return R.fail(ResponseConstant.UNABLE_LOCATE);
                }
                log.info("getEquipTypeInfoByNameV2->oneLevelTypeId = id");
                type = equipmentTypeService.getById(info.getTwoLevelTypeId());
                if (null == type) {
                    return R.fail(ResponseConstant.UNABLE_LOCATE);
                }
            } else {
                type = getOriginEquipmququentType(types);
            }

            BaseQuery<EquipmentInfo> infoBaseQuery = new BaseQuery<>();
            infoBaseQuery.eq("name", equipNames).eq("two_level_type_id", type.getId()).eq("user_by", SessionUtil.getId());
            EquipmentInfo info = equipmentInfoService.getOne(infoBaseQuery);
            if (info != null) {
                String remark = info.getRemark();
                if (StrUtil.isNotBlank(remark)) {
                    JSONObject jsonObject = JSONObject.parseObject(remark);
                    String meritEigenValue = jsonObject.getString(EquipmentConstant.MERIT_EIGEN_VALUE);
                    type = equipmentTypeService.equipmentTypeChange(type, meritEigenValue, info.getName());
                }
                if (StrUtil.isNotBlank(info.getBluetoothAlias())) {
                    type.setBluetoothAlias(info.getBluetoothAlias());
                }
            }
            //速度
            if (type.getMinSpeed() == null) {
                type.setMinSpeed(0);
            }
            if (type.getMaxSpeed() == null) {
                type.setMaxSpeed(0);
            }
            //版本判断
            EquipTypeInfoDTO result = BeanUtil.copyProperties(type, EquipTypeInfoDTO.class);
            if (ObjectUtil.isNotNull(info)) {
                result.setEquipmentInfoId(info.getId());
            }
            UserDeviceDTO device = equipmentInfoService.getDeviceByInfoId(info.getId());
            if (device != null) {
                result.setIsSupportResistanceEcho(device.getIsSupportResistanceEcho());
                result.setModelId(device.getProductModelId());
            }
            return R.ok(result);
        } catch (Exception e) {
            log.warn("无法定位具体型号:" + e.getMessage());
            return R.fail(ResponseConstant.UNABLE_LOCATE);
        }
    }

    private EquipmentType getOriginEquipmququentType(List<EquipmentType> types) {
        return types.stream().filter(v -> StrUtil.isNotBlank(v.getRemark())).findFirst().orElseGet(() -> types.get(0));
    }

    /**
     * 获取所有已绑定的设备信息
     *
     * @param userId 用户id
     * @return 用户绑定的设备集合
     */
    @PostMapping("getUserEquip")
    @Anonymous
    public R getUserEquip(@RequestParam("userId") Long userId, @RequestBody List<Long> typeIds) {
        QueryWrapper<EquipmentInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id", "one_level_type_id", "two_level_type_id", "code", "name", "bind_time").eq("user_by", userId).eq("bind_status", 1).in("one_level_type_id", typeIds);
        List<EquipmentInfo> list = equipmentInfoService.list(queryWrapper);
        return R.ok(list);
    }
    
    /**
     * 根据用户id获取绑定设备信息
     */
    @GetMapping("getTypeByUserIdAnonymous")
    @Anonymous
    public R getTypeByUserIdAnonymous(@RequestParam("userId") String userId) {
        if (StrUtil.isEmpty(userId)) {
            return R.fail(ResponseConstant.COMMON_NOUSER);
        }
        BaseQuery<EquipmentInfo> baseQuery = new BaseQuery<>();
        baseQuery.eq("user_by", Long.valueOf(userId)).eq("bind_status", 1);
        return R.ok(equipmentInfoService.list(baseQuery));
    }

    /**
     * 获取用户已有设备数量
     *
     * @param userId 用户id
     * @return int 设备数量
     */
    @GetMapping("getUserEquipNum")
    @Anonymous
    public R getUserEquipNum(@RequestParam("userId") Long userId) {
        if (userId == null) {
            return R.fail(ResponseConstant.COMMON_NOUSER);
        }
        return R.ok(equipmentInfoService.count("user_by", ConditionEnum.EQ, userId));
    }

    /**
     * 获取当前设备的remark
     *
     * @param modelId 型号id
     * @param userId  用户id
     * @return com.mrk.yudong.core.model.ResDTO<java.lang.String>
     * <AUTHOR>
     * @date 14:49 2022/7/1
     **/
    @Anonymous
    @GetMapping("equipmentInfoRemark")
    public ResDTO<EquipmentInfo> equipmentInfoRemark(Long modelId, Long userId) {
        if (modelId == null || userId == null) {
            return ResDTO.ok();
        }

        BaseQuery<EquipmentInfo> baseQuery = new BaseQuery<>();
        baseQuery.eq("user_by", userId).eq("two_level_type_id", modelId).eq("bind_status", BaseConstant.INT_TRUE).orderByDesc("connect_time");
        List<EquipmentInfo> equipmentInfos = equipmentInfoService.list(baseQuery);
        if (CollUtil.isEmpty(equipmentInfos)) {
            log.info("equipmentInfoRemark modelId={},userId={}", modelId, userId);
            return ResDTO.ok();
        }

        equipmentInfoService.cacheLatestConnectionsModel(equipmentInfos.get(0), 5);
        return ResDTO.ok(equipmentInfos.get(0));
    }

    /**
     * 我的设备数量统计
     *
     * @return com.mrk.yudong.core.model.ResDTO<java.util.List < cn.hutool.core.lang.Dict>>
     * <AUTHOR>
     * @date 18:32 2022/6/24
     **/
    @GetMapping("myEquipmentList")
    public ResDTO<Map<String, Long>> myEquipmentList() {
        Long userId = SessionUtil.getId();
        if (userId == null) {
            return ResDTO.fail(ResponseConstant.COMMON_NOUSER);
        }
        BaseQuery<EquipmentInfo> baseQuery = new BaseQuery<>();

        baseQuery.eq("user_by", userId).eq("bind_status", BaseConstant.INT_TRUE);
        List<EquipmentInfo> equipmentInfos = equipmentInfoService.list(baseQuery);
        Map<Long, List<EquipmentType>> typeMap = equipmentTypeService.list("level", ConditionEnum.EQ, com.mrk.yudong.equipment.biz.equipment.constant.EquipmentConstant.LEVEL_ZERO).stream().collect(Collectors.groupingBy(EquipmentType::getId));
        Map<String, Long> result = new HashMap<>();
        if (CollUtil.isNotEmpty(equipmentInfos) && MapUtil.isNotEmpty(typeMap)) {
            equipmentInfos.forEach(v -> {
                List<EquipmentType> equipmentTypes = typeMap.get(v.getOneLevelTypeId());
                EquipmentType equipmentType = null;
                if (CollUtil.isNotEmpty(equipmentTypes)) {
                    equipmentType = equipmentTypes.stream().findFirst().orElse(null);
                }
                if (equipmentType != null) {
                    if (equipmentType.getType().equals(com.mrk.yudong.equipment.biz.equipment.constant.EquipmentConstant.SPORTS_TYPE)) {
                        v.setType(com.mrk.yudong.equipment.biz.equipment.constant.EquipmentConstant.SPORTS_TYPE);
                    } else if (equipmentType.getType().equals(com.mrk.yudong.equipment.biz.equipment.constant.EquipmentConstant.HEALTH_TYPE)) {
                        v.setType(com.mrk.yudong.equipment.biz.equipment.constant.EquipmentConstant.HEALTH_TYPE);
                    }
                }
            });
            result.put("sportsType", equipmentInfos.stream().filter(v -> v.getType() != null && v.getType().equals(com.mrk.yudong.equipment.biz.equipment.constant.EquipmentConstant.SPORTS_TYPE)).count());
            result.put("healthType", equipmentInfos.stream().filter(v -> v.getType() != null && v.getType().equals(com.mrk.yudong.equipment.biz.equipment.constant.EquipmentConstant.HEALTH_TYPE)).count());

        } else {
            result.put("sportsType", 0L);
            result.put("healthType", 0L);

        }
        return ResDTO.ok(result);
    }

    /**
     * 获取用户已有设备数量
     *
     * @param userId      用户id
     * @param equipTypeId 设备分类id
     * @return com.mrk.yudong.core.model.R
     * <AUTHOR>
     * @date 10:55 2022/7/8
     **/
    @GetMapping("getUserEquipNumByTypeId")
    @Anonymous
    public R getUserEquipNumByTypeId(@RequestParam("userId") Long userId, @RequestParam("equipTypeId") Long equipTypeId) {
        if (userId == null) {
            return R.fail(ResponseConstant.COMMON_NOUSER);
        }
        BaseQuery<EquipmentInfo> baseQuery = new BaseQuery<>();
        baseQuery.eq("user_by", userId).eq("bind_status", BaseConstant.INT_TRUE);
        if (equipTypeId != null) {
            baseQuery.eq("one_level_type_id", equipTypeId);
        }
        return R.ok(equipmentInfoService.count(baseQuery));
    }

    /**
     * 初始化绑定设备任务数据
     *
     * @param taskForm
     * @return com.mrk.yudong.core.model.R
     */
    @PostMapping("initTaskData")
    @Anonymous
    public R initTaskData(@RequestBody TaskForm taskForm) {
        String password = taskForm.getPassword();
        if (StrUtil.isBlank(password) || !"admin@15388337369390aa".equals(password)) {
            return R.paramFail("参数异常");
        }
        Integer current = 1;
        Integer size = 100;
        LocalDateTime now = LocalDateTime.now();
        while (true) {
            IPage<EquipmentInfo> page = equipmentInfoService.page(current, size, null);
            if (page == null || page.getRecords() == null || page.getRecords().isEmpty()) {
                break;
            }

            for (EquipmentInfo equipmentInfo : page.getRecords()) {
                uploadTaskData(equipmentInfo, equipmentInfo.getUserBy(), now);
            }
            current++;
        }
        return R.ok();
    }

    /**
     * 查询最新绑定设备信息
     *
     * @param productId 产品id
     * @return com.mrk.yudong.core.model.ResDTO<com.mrk.yudong.equipment.api.equipment.dto.DeviceDetailDTO>
     * <AUTHOR>
     * @date 13:45 2022/9/15
     **/
    @GetMapping("getDeviceDetail")
    public ResDTO<DeviceDetailDTO> getDeviceDetail(Long productId, Long type) {
        log.info("getDeviceDetail->productId:{}", productId);
        DeviceQry deviceQry = new DeviceQry();
        deviceQry.setUserId(SessionUtil.getId());
        if (null != productId) {
            deviceQry.setProductId(productId);
        }
        //ios 将productId字段改成了type ，设置一个type字段做兼容
        if (null != type) {
            deviceQry.setProductId(type);
        }

        DeviceDTO latestDevice = deviceApi.getLatestDevice(deviceQry);
        DeviceDetailDTO deviceDetailDTO = new DeviceDetailDTO();
        if (null == latestDevice) {
            return ResDTO.ok(deviceDetailDTO);
        }
        log.info("getDeviceDetail->latestDevice:{}", JSONObject.toJSONString(latestDevice));
        BeanUtil.copyProperties(latestDevice, deviceDetailDTO);

        ProductModelDetailDTO productModel = productModelApi.getProductModelById(latestDevice.getModelId());
        if (null == productModel) {
            return ResDTO.ok(deviceDetailDTO);
        }
        log.info("getDeviceDetail->productModel:{}", JSONObject.toJSONString(productModel));
        deviceDetailDTO.setModelCover(productModel.getCover());
        deviceDetailDTO.setModelName(productModel.getModelName());
        deviceDetailDTO.setCommunicationProtocol(productModel.getCommunicationProtocol());
        return ResDTO.ok(deviceDetailDTO);
    }

    /**
     * 生成ws sign
     *
     * @param originId 设备originId
     * @return com.mrk.yudong.core.model.ResDTO<java.lang.String>
     * <AUTHOR>
     * @date 10:13 2022/10/8
     **/
    @GetMapping("createHeratRateWS")
    public ResDTO<String> createHeratRateWS(Long originId) {
        if (null == originId) {
            return ResDTO.fail(ResponseConstant.PARAM_ERROR);
        }
        DeviceUserRelQry deviceUserRelQry = new DeviceUserRelQry();
        deviceUserRelQry.setOriginId(originId);
        DeviceDTO device = deviceApi.getDeviceByDeviceUserRelQry(deviceUserRelQry);
        if (null == device) {
            return ResDTO.fail(ResponseConstant.EQUIP_INVALID);
        }
        String deviceId = device.getDeviceId().toString();
        String sign = WebsocketSignUtil.sign(deviceId);
        if (StrUtil.isBlank(sign)) {
            return ResDTO.fail("签名生成失败，请稍后再试");
        }

        return ResDTO.ok(HealthConstant.WS_HERAT_RATE.replace("${sign}", sign).replace("${deviceId}", deviceId));
    }

    /**
     * 迁移设备数据至设备中台
     *
     * @param prouctId       产品id
     * @param productModelId 产品型号id
     * <AUTHOR>
     * @date 14:50 2022/10/11
     **/
    @GetMapping("migrationEquipInfoToDevice")
    public void migrationEquipInfoToDevice(Long prouctId, Long productModelId, Integer isTest) {
        equipmentInfoService.migrationEquipInfoToDevice(prouctId, productModelId, isTest);
    }

    /**
     * 迁移绑定数据至设备中台
     *
     * @param migrationDTO 合并参数
     * <AUTHOR>
     * @date 14:50 2022/10/11
     **/
    @PostMapping("migrationBindingToDevice")
    public void migrationBindingToDevice(@RequestBody MigrationDTO migrationDTO) {
        equipmentInfoService.migrationBindingToDevice(migrationDTO.getProuctId(), migrationDTO.getProductModelId(), migrationDTO.getIsTest(), migrationDTO.getRelIds());
    }


    @GetMapping("getUserFirstEquipByTypeIds")
    ResDTO<Long> getUserFirstEquipByTypeIds(@RequestParam("typeIds") List<Long> typeIds) {
        return ResDTO.ok(equipmentInfoService.getUserFirstEquipByTypeIds(typeIds));
    }

    @Anonymous
    @GetMapping("getLastConnectModelId")
    public Long getLastConnectModelId(Long userId, Long equipTypeId) {
        EquipmentInfo lastConnectDevice = equipmentInfoService.getLastConnectModelId(userId, equipTypeId);
        if (null == lastConnectDevice) {
            return null;
        }

        return lastConnectDevice.getTwoLevelTypeId();
    }

    /**
     * 根据设备类型获取最后绑定的设备
     *
     * @param equipType
     * @return
     */
    @GetMapping("getUserLastEquipIdByType")
    public ResDTO<Long> getUserLastEquipIdByType(@RequestParam("equipType") Long equipType) {

        return ResDTO.ok(equipmentInfoService.getUserLastEquipIdByType(equipType));
    }

    /**
     * 根据设备类集合获取最后绑定的设备类型
     *
     * @param equipTypes
     * @return
     */
    @GetMapping("getUserLastEquipIdByTypes")
    public ResDTO<Long> getUserLastEquipIdByTypes(@RequestParam("equipTypes") List<Long> equipTypes) {
        return ResDTO.ok(equipmentInfoService.getUserLastEquipIdByTypes(equipTypes));
    }

    @Anonymous
    @GetMapping("getByName")
    public EquipmentInfo getByName(String name) {
        if (StrUtil.isEmpty(name)) {
            return null;
        }

        return equipmentInfoService.getByName(name);
    }

    @Anonymous
    @PostMapping("getEquipmentInfoByIds")
    public List<EquipmentInfo> getEquipmentInfoByIds(@RequestBody List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return null;
        }

        return equipmentInfoService.list(new QueryWrapper<EquipmentInfo>().in("id", ids));
    }

    @GetMapping("getProductGroup")
    @Anonymous
    public String getProductGroup(@RequestParam("userId") Long userId) {
        if (ObjectUtil.isNull(userId)) {
            return "";
        }

        return equipmentInfoService.getProductGroup(userId);
    }

}