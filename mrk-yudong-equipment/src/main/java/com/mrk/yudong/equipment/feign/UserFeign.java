package com.mrk.yudong.equipment.feign;

import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.share.dto.user.TrainEquipDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021−03-31 3:00 下午
 */
@FeignClient(name = "yudong-user")
public interface UserFeign {

    /**
     * 要做是否存在心率设备训练数据
     *
     * @param userId
     * @return
     */
    @GetMapping("/train/check/meta")
    R isTrainMeta(@RequestParam("userId") Long userId);

    /**
     * 获取最新的训练时间及设备分类
     *
     * @param userId
     * @return
     */
    @GetMapping("/train/exerciseEquip")
    ResDTO<List<TrainEquipDTO>> exerciseEquip(@RequestParam("userId") Long userId);
}
