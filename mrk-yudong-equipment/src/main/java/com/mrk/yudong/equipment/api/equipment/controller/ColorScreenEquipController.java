package com.mrk.yudong.equipment.api.equipment.controller;

import cn.hutool.core.util.StrUtil;
import com.mrk.yudong.share.constant.ResponseConstant;
import com.mrk.yudong.core.annotation.Anonymous;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.equipment.api.equipment.vo.ColorScreenEquipVO;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentTypeService;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType;
import com.mrk.yudong.equipment.properties.ColorScreenDeviceModelProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 彩屏端信息
 * <AUTHOR>
 * @create 2022?05-26 10:57
 */
@Slf4j
@RestController
@RequestMapping("color-screen-equip-controller")
@RequiredArgsConstructor
public class ColorScreenEquipController {
    private final ColorScreenDeviceModelProperties colorScreenDeviceModelProperties;
    private final IEquipmentTypeService equipmentTypeService;
    /**
     * 通过唯一码获取设备信息
     * @param code 唯一码
     * @return com.mrk.yudong.equipment.api.equipment.vo.ColorScreenEquipVO 设备信息
     */
    @Anonymous
    @GetMapping("colorScreenEquipTypeInfo")
    public ResDTO<ColorScreenEquipVO> colorScreenEquipTypeInfo(String code){
        log.info("colorScreenEquipTypeInfo,code:{}",code);
        if(StrUtil.isBlank(code)){
            return  ResDTO.paramFail(ResponseConstant.PARAM_ERROR);
        }
        ColorScreenDeviceModelProperties.ColorScreenModel model = colorScreenDeviceModelProperties.getModels().stream()
                .filter(v -> v.getCode().equals(code))
                .findFirst().orElse(null);
        if (model == null) {
            return ResDTO.fail("设备型号不存在");
        }

        EquipmentType equipmentType = equipmentTypeService.getById(model.getId());
        if (equipmentType == null) {
            return ResDTO.fail("设备型号不存在");
        }
        return ResDTO.ok(new ColorScreenEquipVO(equipmentType.getParentId()
                ,equipmentType.getId()
                ,equipmentType.getTypeName()
                ,equipmentType.getTypeImages()
                ,equipmentType.getMaxSpeed()
                ,equipmentType.getMaxSlope()
                ,equipmentType.getMaxResistance()
                ));
    }
}
