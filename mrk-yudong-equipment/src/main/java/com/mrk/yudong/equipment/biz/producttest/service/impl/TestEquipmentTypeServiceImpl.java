package com.mrk.yudong.equipment.biz.producttest.service.impl;

import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType;
import com.mrk.yudong.equipment.infrastructure.producttest.mapper.TestEquipmentTypeMapper;
import com.mrk.yudong.equipment.infrastructure.producttest.model.TestEquipmentType;
import com.mrk.yudong.equipment.biz.producttest.service.ITestEquipmentTypeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 设备类型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-23
 */
@Service
public class TestEquipmentTypeServiceImpl extends BaseServiceImpl<TestEquipmentTypeMapper, TestEquipmentType> implements ITestEquipmentTypeService {

    @Override
    public List<EquipmentType> getTwoTypeNum() {
        return baseMapper.getTwoTypeNum();
    }
}
