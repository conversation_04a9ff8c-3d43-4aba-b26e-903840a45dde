package com.mrk.yudong.equipment.infrastructure.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentBind;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 设备绑定明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
public interface EquipmentBindMapper extends BaseMapper<EquipmentBind> {

    /**
     * 根据用户ID和设备分类ID获取最新的绑定信息
     *
     * @param userId
     * @param categoryId
     * @param bindStatus
     * @return
     */
    EquipmentBind getTopInfo(@Param("userId") Long userId, @Param("categoryId") Long categoryId, @Param("bindStatus") Integer bindStatus);

}
