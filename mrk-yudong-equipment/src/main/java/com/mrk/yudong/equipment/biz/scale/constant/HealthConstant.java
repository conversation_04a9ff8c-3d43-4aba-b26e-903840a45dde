package com.mrk.yudong.equipment.biz.scale.constant;

import java.util.List;

/**
 * @description: 健康属性
 * @author: ljx
 * @create: 2022/6/21 17:18
 * @Version 1.0
 **/
public class HealthConstant {
    /**
     * 体重
     */
    public static final Integer WEIGHT = 1;

    /**
     * 身体得分
     */
    public static final Integer SCORE = 3;
    /**
     * BMI
     */
    public static final Integer BMI = 4;

    /**
     * 左上肢脂肪率
     */
    public static final Integer LEFTARMFATRATE = 48;
    /**
     * 右上肢脂肪率
     */
    public static final Integer RIGHTARMFATRATE = 49;
    /**
     * 左下肢脂肪率
     */
    public static final Integer LEFTLEGFATRATE = 50;
    /**
     * 右下肢脂肪率
     */
    public static final Integer RIGHTLEGFATRATE = 51;
    /**
     * 躯干脂肪率
     */
    public static final Integer ALLBODYFATRATE = 52;
    /**
     * 左上肢肌肉率
     */
    public static final Integer LEFTARMMUSCLERATE = 53;
    /**
     * 右上肢肌肉率
     */
    public static final Integer RIGHTARMMUSCLERATE = 54;
    /**
     * 左下肢肌肉率
     */
    public static final Integer LEFTLEGMUSCLERATE = 55;
    /**
     * 右下肢肌肉率
     */
    public static final Integer RIGHTLEGMUSCLERATE = 56;
    /**
     * 躯干肌肉率
     */
    public static final Integer TRUNKMUSCLERATE = 57;

    /**
     * 体型
     */
    public static final Integer BODYTYPE = 5;
    /**
     * 肌肉
     */
    public static final Integer MUSCLE = 6;
    /**
     * 去脂体重
     */
    public static final Integer LEANBODYMASS = 7;
    /**
     * 体脂率
     */
    public static final Integer BODYFATRATE = 8;
    /**
     * 皮下脂肪
     */
    public static final Integer SUBCUTANEOUSFAT = 9;
    /**
     * 内脏脂肪
     */
    public static final Integer VISCERALFAT = 10;

    /**
     * 基础代谢
     */
    public static final Integer BASALMETABOLISM = 11;

    /**
     * 骨骼肌量
     */
    public static final Integer SKELETALMUSCLERATIO = 12;

    /**
     * 蛋白质
     */
    public static final Integer PROTEIN = 13;

    /**
     * 水分
     */
    public static final Integer WATERCONTENT = 14;

    /**
     * 身体年龄
     */
    public static final Integer BODYAGE = 15;

    /**
     * 脂肪量
     */
    public static final Integer BODYFATMASS = 27;

    /**
     * 肌肉率
     */
    public static final Integer MUSCLERATE = 30;

    /**
     * 建议卡路里摄入
     */
    public static final Integer SUGGESTCALORIEINTAKE = 32;

    /**
     * 心率
     */
    public static final Integer HEARTRATE = 33;

    /**
     * 肥胖水平
     */
    public static final Integer OBESITYLEVEL = 28;

    /**
     * 理想体重
     */
    public static final Integer IDEALBODYWEIGHT = 34;

    /**
     * 控制体重
     */
    public static final Integer CONTROLBODYWEIGHT = 35;

    /**
     * 推测腰臀比
     */
    public static final Integer WAISTHIPRATE = 36;

    /**
     * 体水分量
     */
    public static final Integer WATERMASS = 37;

    /**
     * 蛋白质量
     */
    public static final Integer PROTEINMASS = 38;

    /**
     * 无机盐量
     */
    public static final Integer INORGANICSALTMASS = 39;

    /**
     * 身体细胞量
     */
    public static final Integer BODYCELLMASS = 40;

    /**
     * 细胞外水量
     */
    public static final Integer EXTRACELLULARWATERVOLUME = 41;

    /**
     * 细胞内水量
     */
    public static final Integer INTRACELLULARWATERVOLUME = 42;

    /**
     * 皮下脂肪量
     */
    public static final Integer SUBCUTANEOUSFATMASS = 43;

    /**
     * 骨量
     */
    public static final Integer BONEMASS = 16;

    /**
     * 骨骼肌率
     */
    public static final Integer SKELETALMUSCLERATE = 44;

    /**
     * 骨骼肌质量指数
     */
    public static final Integer SKELETALMUSCLEMASSEXPONENT = 45;

    /**
     * 脂肪控制量
     */
    public static final Integer FATCONTROL = 46;

    /**
     * 肌肉控制量
     */
    public static final Integer MUSCLECONTROL = 47;

    /**
     * 左上肢脂肪量
     */
    public static final Integer LEFTARMFAT = 17;

    /**
     * 右上肢脂肪量
     */
    public static final Integer RIGHTARMFAT = 18;

    /**
     * 躯干脂肪量
     */
    public static final Integer ALLBODYFAT = 21;

    /**
     * 左下肢脂肪量
     */
    public static final Integer LEFTLEGFAT = 19;

    /**
     * 右下肢脂肪量
     */
    public static final Integer RIGHTLEGFAT = 20;

    /**
     * 左上肢肌肉量
     */
    public static final Integer LEFTARMMUSCLE = 22;

    /**
     * 右上肢肌肉量
     */
    public static final Integer RIGHTARMMUSCLE = 23;

    /**
     * 躯干肌肉量
     */
    public static final Integer TRUNKMUSCLE = 26;


    /**
     * 左下肢肌肉量
     */
    public static final Integer LEFTLEGMUSCLE = 24;


    /**
     * 右下肢肌肉量
     */
    public static final Integer RIGHTLEGMUSCLE = 25;

    /**
     * 左上肢阻抗
      */
    public static final Integer LEFTARMIMPEDANCE = 58;

    /**
     * 右上肢阻抗
     */
    public static final Integer RIGHTARMIMPEDANCE = 59;

    /**
     * 躯干阻抗
     */
    public static final Integer BODYIMPEDANCE = 62;

    /**
     * 左下肢阻抗
     */
    public static final Integer LEFTLEGIMPEDANCE = 60;

    /**
     * 右下肢阻抗
     */
    public static final Integer RIGHTLEGIMPEDANCE = 61;







    /**
     * 体重异常数据
     */
    public static final Integer RELATIVE_WEIGHT = 2;


    /**
     * 正常数据
     */
    public static final Integer NORMAL_STATUS_1 = 1;

    /**
     * 异常数据
     */
    public static final Integer ABNORMAL_STATUS_2 = 2;


    /**
     * 自动上报
     */
    public static final Integer AUTOMATICALLY_REPORT = 1;

    /**
     * 手动上报
     */
    public static final Integer MANUAL_REPORTING = 2;


    /**
     * 上升
     */
    public static final Integer UP = 1;

    /**
     * 下降
     */
    public static final Integer DOWN = 2;


    /**
     * 推荐计划key
     */
    public static final String RECOMMENDED_PLAN = "RECOMMENDED_PLAN";
    /**
     * 四电极体脂秤基础展示列表
     **/
    public static final List<Integer> FOUR_BASIC_LIST = List.of(BODYFATRATE, MUSCLE, LEANBODYMASS, SUBCUTANEOUSFAT, VISCERALFAT, BASALMETABOLISM);
    /**
     * 八电极体脂秤基础展示列表
     **/
    public static final List<Integer> EIGHT_BASIC_LIST = List.of(WEIGHT, BMI,BODYFATRATE,BODYTYPE, MUSCLE, LEANBODYMASS, VISCERALFAT, BASALMETABOLISM);

    /**
     * 健康报告展示列表
     **/
    public static final List<Integer> HEALTH_REPORT_LIST = List.of(WEIGHT, BMI, BODYTYPE, MUSCLE, LEANBODYMASS, BODYFATRATE, SUBCUTANEOUSFAT, VISCERALFAT, BASALMETABOLISM, SKELETALMUSCLERATIO, PROTEIN, WATERCONTENT);


    /**
     * 八电极健康报告展示列表
     */
    public static final List<Integer> EIGHT_ELECTRODE_HEALTH_REPORT_LIST = List.of(
            // 基础指标
            WEIGHT, BMI, BODYFATRATE, BODYFATMASS, MUSCLERATE, MUSCLE, BASALMETABOLISM, SUGGESTCALORIEINTAKE, HEARTRATE, BODYTYPE, BODYAGE, SCORE,OBESITYLEVEL,IDEALBODYWEIGHT,CONTROLBODYWEIGHT,LEANBODYMASS,WAISTHIPRATE,
            // 身体成分
            WATERMASS,WATERCONTENT,PROTEINMASS,PROTEIN,INORGANICSALTMASS, BODYCELLMASS,EXTRACELLULARWATERVOLUME,INTRACELLULARWATERVOLUME,
            // 其他指标
            SUBCUTANEOUSFATMASS,SUBCUTANEOUSFAT,VISCERALFAT,BONEMASS,SKELETALMUSCLERATIO,SKELETALMUSCLERATE,SKELETALMUSCLEMASSEXPONENT, FATCONTROL,MUSCLECONTROL,
            // 节段脂肪
            LEFTARMFAT,LEFTARMFATRATE,RIGHTARMFAT,RIGHTARMFATRATE,ALLBODYFAT,ALLBODYFATRATE,LEFTLEGFAT,LEFTLEGFATRATE,RIGHTLEGFAT,RIGHTLEGFATRATE,
            // 节段肌肉
            LEFTARMMUSCLE,LEFTARMMUSCLERATE,RIGHTARMMUSCLE,RIGHTARMMUSCLERATE,TRUNKMUSCLE,TRUNKMUSCLERATE,LEFTLEGMUSCLE,LEFTLEGMUSCLERATE,RIGHTLEGMUSCLE,RIGHTLEGMUSCLERATE,
            // 节段阻抗
            LEFTARMIMPEDANCE,RIGHTARMIMPEDANCE,BODYIMPEDANCE,LEFTLEGIMPEDANCE,RIGHTLEGIMPEDANCE
    );


    /**
     * 体脂秤基础展示列表
     **/
    public static final List<Integer> SPORTS_TYPE_LIST = List.of(1, 2);

    /**
     * 体脂秤基础展示列表
     **/
    public static final Integer HEALTH_TYPE = 3;


    /**
     * 体型九宫格排序
     **/
    public static final List<Integer> SCRATCHABLE_LATEX = List.of(9, 6, 3, 8, 5, 2, 7, 4, 1);
    /**
     * 体脂秤基础展示列表
     **/
    public static final String DEFAULT_COPYWRITING = "本次未测得体脂率和肌肉量,无法判断你的体型,请赤脚上秤试一试吧!";

    /**
     * 体脂秤基础展示列表
     **/
    public static final String DEFAULT_BODY_TYPE_COPYWRITING = "未测得体脂数据，赤脚上秤试一试吧。";

    /**
     * 去脂体重默认描述
     **/
    public static final String LEANBODYMASS_DEFAULT_COPYWRITING = "去脂体重为除脂肪以外身体其他成分的重量，肌肉是其中的主要部分。";


    /**
     * 心率带ws链接
     **/
    public static final String WS_HERAT_RATE = "sign=${sign}&deviceId=${deviceId}";

    /**
     * 不支持跳转属性
     */
    public static final List<Integer> NO_SUPPORT_SKIP_LIST = List.of(SUGGESTCALORIEINTAKE, IDEALBODYWEIGHT, CONTROLBODYWEIGHT, FATCONTROL, MUSCLECONTROL);
}
