package com.mrk.yudong.equipment.api.equipment.controller;

import cn.hutool.core.collection.CollUtil;
import com.merach.sun.device.api.DeviceApi;
import com.merach.sun.device.api.ProductApi;
import com.merach.sun.device.api.ProductModelApi;
import com.merach.sun.device.dto.qry.DeviceQry;
import com.merach.sun.device.dto.qry.ProductQry;
import com.merach.sun.device.dto.resp.device.DeviceDTO;
import com.merach.sun.device.dto.resp.model.ProductModelDetailDTO;
import com.merach.sun.device.dto.resp.product.ProductDetailDTO;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.equipment.api.equipment.dto.EquipmentBindDTO;
import com.mrk.yudong.equipment.api.equipment.vo.EquipBindingVO;
import com.mrk.yudong.equipment.biz.equipment.bo.EquipmentBindBO;
import com.mrk.yudong.equipment.biz.equipment.bo.EquipmentQueryBO;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipDictService;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentBindService;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentTypeService;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipDict;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.ResponseConstant;
import com.mrk.yudong.share.constant.equipment.EquipmentConstant;
import com.mrk.yudong.share.constant.equipment.EquipmentMetaConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备绑定明细表 前端控制器
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/equipment-bind")
public class EquipmentBindController {


    private final IEquipmentBindService equipmentBindService;

    private final IEquipDictService equipDictService;

    private final ProductModelApi productModelApi;

    private final DeviceApi deviceApi;

    private final ProductApi productApi;

    private final IEquipmentTypeService equipmentTypeService;


    /**
     * 迁移数据至info表
     *
     * @return com.mrk.yudong.core.model.ResDTO<java.lang.Boolean>
     * <AUTHOR>
     * @date 14:09 2022/10/11
     **/
    @GetMapping("heartRateDataMigrationToInfo")
    public ResDTO<Boolean> heartRateDataMigrationToInfo(Long prouctId, Long productModelId, Integer isTest) {
        return ResDTO.ok(equipmentBindService.heartRateDataMigrationToInfo(prouctId, productModelId, isTest));
    }

    /**
     * 迁移数据至设备中台
     *
     * @param prouctId       产品id
     * @param productModelId 产品型号id
     * <AUTHOR>
     * @date 14:50 2022/10/11
     **/
    @GetMapping("heartRateDataMigrationToDevice")
    public void heartRateDataMigrationToDevice(Long prouctId, Long productModelId, Integer isTest) {
        equipmentBindService.heartRateDataMigrationToDevice(prouctId, productModelId, isTest);
    }

    /**
     * 查询心率设备分类信息
     *
     * @param type
     * @return
     */
    @GetMapping("/rate/info")
    public ResDTO<EquipmentBindDTO> rate(@RequestParam(defaultValue = EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_100 + "", required = false) Integer type) {

        ProductDetailDTO productDetail = productApi.getProduct(new ProductQry(Long.valueOf(type)));
        if (null == productDetail) {
            return ResDTO.fail(ResponseConstant.EQUIP_ILLEGAL);
        }

        EquipmentBindDTO equipmentBindDTO = new EquipmentBindDTO();
        equipmentBindDTO.setCategoryId(productDetail.getId());
        equipmentBindDTO.setCategoryName(productDetail.getName());
        equipmentBindDTO.setCategoryCover(productDetail.getCover());
        equipmentBindDTO.setCategoryIcon(productDetail.getCover());
        Long userId = SessionUtil.getId();

        if (type.equals(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_100)) {
            // 心率带设备
            DeviceQry deviceQry = new DeviceQry();
            deviceQry.setProductId(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_100);
            deviceQry.setUserId(userId);
            DeviceDTO latestDevice = deviceApi.getLatestDevice(deviceQry);
            if (null == latestDevice) {
                return ResDTO.ok(equipmentBindService.newBuildBindDTO(productDetail, null, null, null, null));
            }
            ProductModelDetailDTO productModel = productModelApi.getProductModelById(latestDevice.getModelId());

            return ResDTO.ok(equipmentBindService.newBuildBindDTO(productDetail, productModel, latestDevice.getDeviceId(), latestDevice.getBluetoothName(), latestDevice.getMac()));

        }

        return ResDTO.fail(ResponseConstant.UNABLE_LOCATE);
    }
    
    /**
     * 查询设备型号列表
     *
     * @param equipmentQueryBO
     * @return
     */
    @PostMapping("/query")
    public ResDTO<List<EquipBindingVO>> query(@RequestBody @Valid EquipmentQueryBO equipmentQueryBO) {
        ProductDetailDTO productDetailDTO = productApi.getProduct(new ProductQry(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_100));
        if (null == productDetailDTO) {
            return ResDTO.paramFail(ResponseConstant.EQUIP_ILLEGAL);
        }

        // 心率设备
        List<EquipDict> equipDicts = equipDictService.list("dict_key", ConditionEnum.EQ, EquipmentMetaConstant.EQUIPMENT_CATEGORY_TYPE_CODE.replace("${type}", equipmentQueryBO.getType().toString()));
        if (CollUtil.isEmpty(equipDicts)) {
            return ResDTO.ok();
        }

        EquipBindingVO equipBindingVo = equipmentTypeService.addEquipBindingVo(equipmentQueryBO.getName(), equipmentQueryBO.getType().toString());
        if (null == equipBindingVo) {
            return ResDTO.ok();
        }

        List<EquipBindingVO> bindingVos = new ArrayList<>();
        bindingVos.add(equipBindingVo);
        return ResDTO.ok(bindingVos);
    }

    /**
     * 绑定 / 解绑设备
     *
     * @param equipmentBindBO
     * @return
     */
    @PostMapping
    public R bind(@RequestBody @Valid EquipmentBindBO equipmentBindBO) {
        boolean isNullEquipmentId = null == equipmentBindBO.getEquipmentId();
        if (equipmentBindBO.getOperation().equals(BaseConstant.INT_TRUE) && isNullEquipmentId) {
            return R.fail("设备ID不能为空");
        }

        ProductModelDetailDTO productModel = new ProductModelDetailDTO();
        if (!isNullEquipmentId) {
            productModel = productModelApi.getProductModelById(equipmentBindBO.getEquipmentId());
        }

        return equipmentBindService.bind(productModel, equipmentBindBO);
    }

}
