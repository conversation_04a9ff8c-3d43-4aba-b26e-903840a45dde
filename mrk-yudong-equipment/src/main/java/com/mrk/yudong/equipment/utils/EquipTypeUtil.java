package com.mrk.yudong.equipment.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.utils.RedisUtil;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipDictService;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipDict;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备常用工具信息
 *
 * <AUTHOR>
 * @create 2021−06-04 11:30上午
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class EquipTypeUtil {

    private final RedisUtil redisUtil;

    private final IEquipDictService equipDictService;

    /**
     * 获取型号
     */
    public Map<String, Object> getTypeCode(String name) {
        log.info("name:" + name);
        if (name == null) {
            return null;
        }

        List<EquipDict> typePrefixRules = getTypePrefixRule();

        if (CollUtil.isEmpty(typePrefixRules)) {
            return null;
        }
        String typeCode = null;
        String prefix = null;

        try {
            for (EquipDict typePrefixRule : typePrefixRules) {
                if (StrUtil.isNotBlank(typeCode) && StrUtil.isNotBlank(prefix)) {
                    break;
                }

                //前缀信息
                EquipDict rule = JSONObject.parseObject(typePrefixRule.getValue(), EquipDict.class);
                //前缀值
                String prefixValue = rule.getValue();

                int ruleValueIndex = name.indexOf(prefixValue);
                if (ruleValueIndex < 0) {
                    continue;
                }

                String sub = rule.getSubInfo();
                //蓝牙广播名拆分
                String[] bluetoothNameSplit = null;
                if (StrUtil.isNotBlank(sub)) {
                    bluetoothNameSplit = name.split(sub);
                }

                Integer mrIndex = null;
                if (rule.getMr().equals(0)) {
                    mrIndex = name.indexOf("MR");
                }
                if (mrIndex != null) {
                    //需要进行判断的MERACH-，Merach-
                    if (mrIndex < 0 && bluetoothNameSplit != null && bluetoothNameSplit.length > rule.getNum()) {
                        prefix = prefixValue;
                        typeCode = bluetoothNameSplit[rule.getNum()];
                    }
                } else {
                    if (rule.getNum() != null && bluetoothNameSplit != null ) {
                        //有型号规则的数据
                        if ("Merach-MR581-P".equals(prefixValue)) {
                            prefix = prefixValue;
                            typeCode = StrUtil.removePrefix(bluetoothNameSplit[0], "Merach-MR");
                        } else if ("Merach-MR".equals(prefixValue) && !name.contains("-P")) {
                            prefix = prefixValue;
                            typeCode = StrUtil.removePrefix(bluetoothNameSplit[1], "MR");
                        } else {
                            prefix = prefixValue;
                            typeCode = bluetoothNameSplit[rule.getNum()];
                        }
                    } else {
                        //区分iconsole+和iconsole
                        if (name.contains("+") && !StrUtil.equals(sub, "//+")) {
                            continue;
                        }
                        //区分 mrk-和mrk
                        if (name.contains("-") && StrUtil.equals(prefixValue, "MRK")) {
                            continue;
                        }
                        prefix = prefixValue;
                        break;
                    }
                }

            }
        } catch (Exception e) {
            log.warn("搜索设备失败：{}", e.getMessage());
        }
        Map<String, Object> result = new HashMap<>(2);
        result.put("typeCode", typeCode);
        //查出prefix的id
        BaseQuery<EquipDict> baseQuery = new BaseQuery<>();
        baseQuery.select("id").eq("value", prefix).eq("dict_key", RedisKeyConstant.EQUIP_TYPE);
        List<EquipDict> list = equipDictService.list(baseQuery);
        if (CollUtil.isNotEmpty(list)) {
            EquipDict one = list.get(0);
            if (one.getId() != null) {
                result.put("prefix", one.getId());
            }
        }


        return result;
    }



    @NotNull
    private List<EquipDict> getTypePrefixRule() {
        Map<String, String> typePrefixsCache = redisUtil.getCacheMap(RedisKeyConstant.EQUIP_TYPE_T);
        return typePrefixsCache.values().stream().map(i -> JSONObject.parseObject(i, EquipDict.class))
                .collect(Collectors.toList())
                .stream()
                .sorted(Comparator.comparing(EquipDict::getId).reversed())
                .collect(Collectors.toList());
    }

}
