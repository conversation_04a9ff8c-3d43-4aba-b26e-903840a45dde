package com.mrk.yudong.equipment.infrastructure.device.gateway.impl;

import com.merach.sun.device.api.DeviceApi;
import com.merach.sun.device.dto.qry.UserDeviceQry;
import com.mrk.yudong.equipment.infrastructure.device.gateway.DeviceGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class DeviceGatewayImpl implements DeviceGateway {
    private final DeviceApi deviceApi;

    @Override
    public List<Long> getBindProductId(Long userId, Long productId) {
        UserDeviceQry userDeviceQry = new UserDeviceQry();
        userDeviceQry.setUserId(userId);
        userDeviceQry.setProductId(productId);
        return deviceApi.getBindProductId(userDeviceQry);
    }
}
