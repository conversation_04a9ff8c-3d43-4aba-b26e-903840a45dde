package com.mrk.yudong.equipment.infrastructure.scale.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 健康属性信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("equ_health_attribute")
public class HealthAttribute extends Model<HealthAttribute> {

    private static final long serialVersionUID = 1L;

    /**
     * 健康属性id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 成分类型：1.基本指标，2.身体成分，3.其他指标，4.节段脂肪，5.节段肌肉，6.节段阻抗
     */
    private Integer compositionType;

    /**
     * 名称
     */
    private String name;

    /**
     * 对应属性字段
     */
    private String attribute;
    /**
     * 单位
     */
    private String unit;

    /**
     * 图标
     */
    private String icon;

    /**
     * 计算公式（规则字段）
     */
    private String formula;

    /**
     * 介绍
     */
    private String introduce;

    /**
     * json字符串
     */
    private String remark;

    /**
     * 创建人
     */
    private Long createId;

    /**
     *  背景图
     */
    private String backgroundImage;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    private Integer isDelete;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
