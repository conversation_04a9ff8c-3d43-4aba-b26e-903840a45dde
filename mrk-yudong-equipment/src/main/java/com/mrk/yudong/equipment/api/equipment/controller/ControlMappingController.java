package com.mrk.yudong.equipment.api.equipment.controller;

import com.merach.sun.device.api.ProductModelApi;
import com.merach.sun.device.dto.qry.ProductModelQry;
import com.mrk.yudong.share.dto.equip.LiveMappingConvertDTO;
import com.mrk.yudong.share.vo.equip.LiveMappingConvertVO;
import com.mrk.yudong.core.annotation.Anonymous;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("control_mapping")
@RequiredArgsConstructor
public class ControlMappingController {

    private final IEquipmentTypeService equipmentTypeService;
    private final ProductModelApi productModelApi;

    /**
     * 转换产品型号控制映射
     *
     * @param liveMappingConvertVO
     * @return com.mrk.yudong.core.model.ResDTO<java.util.List < com.mrk.yudong.share.dto.equip.LiveMappingConvertDTO>>
     * <AUTHOR>
     * @date 14:29 2022/11/22
     **/
    @Anonymous
    @PostMapping("listModelControlMapping")
    public ResDTO<List<LiveMappingConvertDTO>> listModelControlMapping(@RequestBody @Valid LiveMappingConvertVO liveMappingConvertVO) {
        return ResDTO.ok(equipmentTypeService.listModelControlMapping(liveMappingConvertVO));
    }

    @PostMapping("listProductModels")
    @Deprecated
    public void listProductModels() {
        productModelApi.listProductModels(new ProductModelQry());
    }

}
