package com.mrk.yudong.equipment.api.producttest;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.core.annotation.Anonymous;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.equipment.biz.equipment.constant.DictKeyConstant;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipDictService;
import com.mrk.yudong.equipment.biz.producttest.service.ITestEquFirmwareVersionService;
import com.mrk.yudong.equipment.biz.producttest.service.ITestEquipmentTypeService;
import com.mrk.yudong.equipment.biz.producttest.service.ITestModelParamsAssocitedService;
import com.mrk.yudong.equipment.biz.producttest.service.ITestParamsConfigService;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipDict;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType;
import com.mrk.yudong.equipment.infrastructure.producttest.model.TestEquFirmwareVersion;
import com.mrk.yudong.equipment.infrastructure.producttest.model.TestEquipmentType;
import com.mrk.yudong.equipment.infrastructure.producttest.model.TestModelParamsAssocited;
import com.mrk.yudong.equipment.infrastructure.producttest.model.TestParamsConfig;
import com.mrk.yudong.equipment.utils.EquipTypeUtil;
import com.mrk.yudong.share.constant.ResponseConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备类型 前端控制器
 *
 * <AUTHOR>
 * @since 2021-11-23
 */
@RestController
@RequestMapping("/test-equipment-type")
@RequiredArgsConstructor
public class TestEquipmentTypeController {

    private final ITestEquipmentTypeService testEquipmentTypeService;

    private final ITestModelParamsAssocitedService testModelParamsAssocitedService;

    private final ITestParamsConfigService testParamsConfigService;

    private final ITestEquFirmwareVersionService testEquFirmwareVersionService;

    private final EquipTypeUtil equipTypeUtil;

    private final IEquipDictService equipDictService;


    private final ITestEquFirmwareVersionService firmwareVersionService;

    /**
     * 校验信息
     *
     * @param testEquipmentType 设备信息
     * @return 校验信息
     */
    public Map<String, Object> valid(TestEquipmentType testEquipmentType) {
        Map<String, Object> result = new HashMap<>();
        //校验数据
        if (testEquipmentType.getLevel().equals(1)) {
            if (testEquipmentType.getPrefixId() == null) {
                result.put("fail", "请选择蓝牙广播名前缀");
                return result;
            }
            if (testEquipmentType.getCode() == null) {
                result.put("fail", "请填写设备型号");
                return result;
            }
            if (testEquipmentType.getTypeName() == null) {
                result.put("fail", "请选择设备类型名称");
                return result;
            }
            if (testEquipmentType.getInstrumentType() == null) {
                result.put("fail", "请选择仪表类型");
                return result;
            }
            if (CollUtil.isEmpty(testEquipmentType.getEquipParamsList())) {
                result.put("fail", "请选择设备参数");
                return result;
            }
            if (CollUtil.isEmpty(testEquipmentType.getFunctionParamsList())) {
                result.put("fail", "请选择功能参数");
                return result;
            }
        }
        return result;
    }

    /**
     * 新增
     *
     * @param testEquipmentType 参数配置
     * @return R
     */
    @PostMapping("testEquipmentType")
    public R saveTestEquipmentType(@RequestBody @Valid TestEquipmentType testEquipmentType) {
        Long id = SessionUtil.getId();
        if (id == null) {
            return R.fail(ResponseConstant.COMMON_NOUSER);
        }

        //校验信息
        Map<String, Object> validInfo = valid(testEquipmentType);
        if (MapUtil.isNotEmpty(validInfo)) {
            String fail = MapUtil.getStr(validInfo, "fail");
            if (StrUtil.isNotBlank(fail)) {
                return R.fail(fail);
            }
        }
        testEquipmentType.setCreateBy(id);
        testEquipmentType.setUpdateBy(id);
        testEquipmentType.setUpdateTime(LocalDateTime.now());
        boolean saveStatus = testEquipmentTypeService.save(testEquipmentType);
        if (saveStatus) {
            if (testEquipmentType.getLevel().equals(1)) {
                testModelParamsAssocitedService.remove("model_id", ConditionEnum.EQ, testEquipmentType.getId());
                testEquipmentType.getEquipParamsList().addAll(testEquipmentType.getFunctionParamsList());
                testEquipmentType.getEquipParamsList().forEach(v -> v.setModelId(testEquipmentType.getId()));
                testModelParamsAssocitedService.saveBatch(testEquipmentType.getEquipParamsList());
            }
            return R.ok();
        } else {
            return R.fail(ResponseConstant.SAVE_FAIL);
        }
    }

    /**
     * 修改
     *
     * @param testEquipmentType 设备信息
     * @return R
     */
    @PutMapping("testEquipmentType")
    public R updateTestEquipmentType(@RequestBody @Valid TestEquipmentType testEquipmentType) {
        Long id = SessionUtil.getId();
        if (id == null) {
            return R.fail(ResponseConstant.COMMON_NOUSER);
        }
        //校验信息
        Map<String, Object> validInfo = valid(testEquipmentType);
        if (MapUtil.isNotEmpty(validInfo)) {
            String fail = MapUtil.getStr(validInfo, "fail");
            if (StrUtil.isNotBlank(fail)) {
                return R.fail(fail);
            }
        }
        testEquipmentType.setUpdateTime(LocalDateTime.now());
        testEquipmentType.setUpdateBy(id);
        boolean updateStatus = testEquipmentTypeService.updateById(testEquipmentType);
        if (updateStatus) {
            if (testEquipmentType.getLevel().equals(1)) {
                testModelParamsAssocitedService.remove("model_id", ConditionEnum.EQ, testEquipmentType.getId());
                testEquipmentType.getEquipParamsList().addAll(testEquipmentType.getFunctionParamsList());
                testEquipmentType.getEquipParamsList().forEach(v -> v.setModelId(testEquipmentType.getId()));
                testModelParamsAssocitedService.saveOrUpdateBatch(testEquipmentType.getEquipParamsList());
            }
            return R.ok();
        } else {
            return R.fail(ResponseConstant.UPDATE_FAIL);
        }
    }

    /**
     * 删除设备信息
     *
     * @param id 信息id
     * @return 删除信息
     */
    @DeleteMapping("testEquipmentType")
    public R delTestEquipmentType(Long id) {
        boolean delStatus;
        if (id == null) {
            return R.fail(ResponseConstant.PARAM_ERROR);
        }
        delStatus = testEquipmentTypeService.removeById(id);
        if (delStatus) {
            testModelParamsAssocitedService.remove("model_id", ConditionEnum.EQ, id);
            return R.ok();
        } else {
            return R.fail(ResponseConstant.DEL_FAIL);
        }
    }

    /**
     * 设备信息分页
     *
     * @param param 分页参数
     * @return R
     */
    @GetMapping("testEquipmentTypePage")
    public R getTestEquipmentTypePage(@RequestParam Map<String, Object> param) {
        Integer current = MapUtil.getInt(param, "current", 1);
        Integer size = MapUtil.getInt(param, "size", 10);
        BaseQuery<TestEquipmentType> condition = new BaseQuery<>();
        IPage<TestEquipmentType> page = new Page<>(current, size);
        String typeName = MapUtil.getStr(param, "typeName");
        if (StrUtil.isNotBlank(typeName)) {
            condition.eq("type_name", typeName);
        }
        Integer level = MapUtil.getInt(param, "level", 0);
        if (level != null) {
            condition.eq("level", level);
        }
        Long parentId = MapUtil.getLong(param, "parentId");
        if (parentId != null) {
            condition.eq("parent_id", parentId);
        }
        condition.orderByDesc("create_time");
        page = testEquipmentTypeService.page(page, condition);
        //转换仪表类型
        List<EquipDict> dictKeys = equipDictService.list("dict_key", ConditionEnum.EQ, DictKeyConstant.TEST_INS);
        //找到版本信息
        List<TestEquipmentType> records = page.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            if (level.equals(1)) {
                List<Long> modelIds = records.stream().map(TestEquipmentType::getId).collect(Collectors.toList());
                BaseQuery<TestEquFirmwareVersion> testEquFirmwareVersionBaseQuery = new BaseQuery<>();
                testEquFirmwareVersionBaseQuery.in("equip_model_id", modelIds).groupBy("code").select("code", "equip_model_id", "code", "max(version)");
                List<TestEquFirmwareVersion> testEquFirmwareVersions = testEquFirmwareVersionService.list(testEquFirmwareVersionBaseQuery);
                records.forEach(v -> {
                    List<TestEquFirmwareVersion> firmwareInfos = new ArrayList<>();
                    testEquFirmwareVersions.forEach(firmwareVersion -> {
                        if (v.getId().equals(firmwareVersion.getEquipModelId())) {
                            TestEquFirmwareVersion firmwareInfo = new TestEquFirmwareVersion();
                            firmwareInfo.setCode(firmwareVersion.getCode());
                            firmwareInfo.setVersion(firmwareVersion.getVersion());
                            firmwareInfos.add(firmwareInfo);
                        }
                    });
                    v.setFirmwareInfoList(firmwareInfos);
                    if (CollUtil.isNotEmpty(dictKeys)) {
                        dictKeys.forEach(dict -> {
                            if (dict.getValue().equals(v.getInstrumentType().toString())) {
                                v.setInstrumentTypeName(dict.getName());
                            }
                        });
                    }
                });
            } else {
                //获取二级类型数量
                Map<Object, Object> twoTypeNumMap = testEquipmentTypeService.getTwoTypeNum().stream().collect(Collectors.toMap(EquipmentType::getParentId, EquipmentType::getTypeNum));
                records.forEach(item -> {
                    if (MapUtil.isNotEmpty(twoTypeNumMap) && twoTypeNumMap.containsKey(item.getId())) {
                        Integer num = (Integer) twoTypeNumMap.get(item.getId());
                        item.setNum(num);
                    } else {
                        item.setNum(0);
                    }
                });
            }
        }
        return R.ok(page);
    }


    /**
     * 获取单条设备信息
     *
     * @param id id
     * @return 详细信息
     */
    @GetMapping("testEquipmentType")
    public R getTestEquipmentTypeTypeById(Long id) {
        TestEquipmentType info = new TestEquipmentType();
        if (id != null) {
            info = testEquipmentTypeService.getById(id);
            //转换仪表类型
            List<EquipDict> dictKeys = equipDictService.list("dict_key", ConditionEnum.EQ, DictKeyConstant.TEST_INS);
            if (CollUtil.isNotEmpty(dictKeys)) {
                TestEquipmentType finalInfo = info;
                dictKeys.forEach(v -> {
                    if (v.getValue().equals(finalInfo.getInstrumentType().toString())) {
                        finalInfo.setInstrumentTypeName(v.getName());
                    }
                });
            }
            //获取参数配置
            List<TestModelParamsAssocited> paramsAssociteds = testModelParamsAssocitedService.list("model_id", ConditionEnum.EQ, info.getId());
            if (CollUtil.isNotEmpty(paramsAssociteds)) {
                info.setEquipParamsList(paramsAssociteds.stream().filter(v -> v.getEquipParamsId() != null).collect(Collectors.toList()));
                info.setFunctionParamsList(paramsAssociteds.stream().filter(v -> v.getFunctionParamsId() != null).collect(Collectors.toList()));
                List<Long> paramsIds = new ArrayList<>();
                List<Long> functionParamsIds = paramsAssociteds.stream().map(TestModelParamsAssocited::getFunctionParamsId).filter(Objects::nonNull).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(functionParamsIds)) {
                    paramsIds.addAll(functionParamsIds);
                }
                List<Long> equipParamsIds = paramsAssociteds.stream().map(TestModelParamsAssocited::getEquipParamsId).filter(Objects::nonNull).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(equipParamsIds)) {
                    paramsIds.addAll(equipParamsIds);
                }
                List<TestParamsConfig> testParamsConfigs = testParamsConfigService.listByIds(paramsIds);
                if (CollUtil.isNotEmpty(testParamsConfigs)) {
                    Map<Long, TestParamsConfig> paramsNameMap = testParamsConfigs.stream().collect(Collectors.toMap(TestParamsConfig::getId, v -> v));
                    paramsAssociteds.forEach(v -> {
                        if (v.getEquipParamsId() != null) {
                            TestParamsConfig equipParamsConfig = paramsNameMap.get(v.getEquipParamsId());
                            if (equipParamsConfig != null) {
                                v.setParamsName(equipParamsConfig.getName());
                                v.setDefaultValue(equipParamsConfig.getDefaultValue());
                                v.setEquipValue(equipParamsConfig.getValue());
                            }
                        } else {
                            //功能参数需要从参数表中取数据
                            TestParamsConfig functionParamsConfig = paramsNameMap.get(v.getFunctionParamsId());
                            if (functionParamsConfig != null) {
                                v.setParamsName(functionParamsConfig.getName());
                                v.setEquipValue(functionParamsConfig.getValue());
                            }
                        }
                    });
                }
            }
            //查询出所有的版本信息
            List<TestEquFirmwareVersion> modelFirmwareList = firmwareVersionService.list("equip_model_id", ConditionEnum.EQ, info.getId());
            if (CollUtil.isNotEmpty(modelFirmwareList)) {
                Map<String, List<TestEquFirmwareVersion>> collect = modelFirmwareList.stream().collect(Collectors.groupingBy(TestEquFirmwareVersion::getCode));
                List<TestEquFirmwareVersion> firmwareVersions = new ArrayList<>();
                collect.forEach((k, v) ->
                        firmwareVersions.add(v.stream().max(Comparator.comparing(TestEquFirmwareVersion::getUpdateTime)).orElse(new TestEquFirmwareVersion()))
                );
                if (CollUtil.isNotEmpty(firmwareVersions)) {
                    info.setFirmwareInfoList(firmwareVersions);
                }
            }
        }
        return R.ok(info);
    }

    /**
     * app获取设备信息
     *
     * @param name 设备名称
     * @param name 设备编号
     * @return 详细信息
     */
    @GetMapping("appTestEquipmentType")
    @Anonymous
    public R getappTestEquipmentTypeTypeById(String name, String code) {
        //获取蓝牙名称中的型号
        Map<String, Object> typeInfo = equipTypeUtil.getTypeCode(name);
        String typeCode = MapUtil.getStr(typeInfo, "typeCode");
        Integer prefix = MapUtil.getInt(typeInfo, "prefix");
        if (prefix == null) {
            return R.fail("暂不支持该设备");
        }
        //去数据库中查出对应型号数据
        QueryWrapper<TestEquipmentType> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("prefix_id", prefix).eq("code", typeCode);
        TestEquipmentType equipmentTypes = testEquipmentTypeService.getOne(queryWrapper);
        if (equipmentTypes == null) {
            return R.fail("暂不支持该设备");
        }
        //获取分类名称
        TestEquipmentType parent = testEquipmentTypeService.getById(equipmentTypes.getParentId());
        equipmentTypes.setTypeName(parent.getTypeName());
        equipmentTypes.setTypeId(parent.getId());
        //转换仪表类型
        List<EquipDict> dictKeys = equipDictService.list("dict_key", ConditionEnum.EQ, DictKeyConstant.TEST_INS);
        if (CollUtil.isNotEmpty(dictKeys)) {
            dictKeys.forEach(v -> {
                if (v.getValue().equals(equipmentTypes.getInstrumentType().toString())) {
                    equipmentTypes.setInstrumentTypeName(v.getName());
                }
            });
        }
        //获取参数配置
        List<TestModelParamsAssocited> paramsAssociteds = testModelParamsAssocitedService.list("model_id", ConditionEnum.EQ, equipmentTypes.getId());
        if (CollUtil.isNotEmpty(paramsAssociteds)) {
            equipmentTypes.setEquipParamsList(paramsAssociteds.stream().filter(v -> v.getEquipParamsId() != null).collect(Collectors.toList()));
            equipmentTypes.setFunctionParamsList(paramsAssociteds.stream().filter(v -> v.getFunctionParamsId() != null).collect(Collectors.toList()));
            List<Long> paramsIds = new ArrayList<>();
            List<Long> functionParamsIds = paramsAssociteds.stream().map(TestModelParamsAssocited::getFunctionParamsId).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(functionParamsIds)) {
                paramsIds.addAll(functionParamsIds);
            }
            List<Long> equipParamsIds = paramsAssociteds.stream().map(TestModelParamsAssocited::getEquipParamsId).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(equipParamsIds)) {
                paramsIds.addAll(equipParamsIds);
            }
            List<TestParamsConfig> testParamsConfigs = testParamsConfigService.listByIds(paramsIds);
            if (CollUtil.isNotEmpty(testParamsConfigs)) {
                Map<Long, TestParamsConfig> paramsNameMap = testParamsConfigs.stream().collect(Collectors.toMap(TestParamsConfig::getId, v -> v));
                paramsAssociteds.forEach(v -> {
                    if (v.getEquipParamsId() != null) {
                        TestParamsConfig equipParamsConfig = paramsNameMap.get(v.getEquipParamsId());
                        if (equipParamsConfig != null) {
                            v.setParamsName(equipParamsConfig.getName());
                            v.setDefaultValue(equipParamsConfig.getDefaultValue());
                            v.setEquipValue(equipParamsConfig.getValue());
                        }
                    } else {
                        //功能参数需要从参数表中取数据
                        TestParamsConfig functionParamsConfig = paramsNameMap.get(v.getFunctionParamsId());
                        if (functionParamsConfig != null) {
                            v.setParamsName(functionParamsConfig.getName());
                            v.setEquipValue(functionParamsConfig.getValue());
                        }
                    }
                });
            }
        }
        //查询出所有的版本信息
        BaseQuery<TestEquFirmwareVersion> baseQuery = new BaseQuery<>();
        baseQuery.eq("code", code).eq("equip_model_id", equipmentTypes.getId());
        List<TestEquFirmwareVersion> list = firmwareVersionService.list(baseQuery);
        if (CollUtil.isNotEmpty(list)) {
            TestEquFirmwareVersion firmwareVersion = list.stream().max(Comparator.comparing(TestEquFirmwareVersion::getUpdateTime)).orElse(new TestEquFirmwareVersion());
            equipmentTypes.setFirmwareInfo(firmwareVersion);
        }
        return R.ok(equipmentTypes);
    }

}
