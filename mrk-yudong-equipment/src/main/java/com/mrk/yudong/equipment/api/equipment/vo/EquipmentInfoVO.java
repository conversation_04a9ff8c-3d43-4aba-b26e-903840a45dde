package com.mrk.yudong.equipment.api.equipment.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021−04-01 4:22 下午
 */
@Data
public class EquipmentInfoVO {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    private String code;

    /**
     * 设备类型名称
     */
    private String name;

    /**
     * 设备类型id
     */
    private Long typeId;

    /**
     * 绑定状态1：绑定，0：未绑定
     */
    private Integer bindStatus;

    /**
     * 使用人信息
     */
    private Long userBy;
    /**
     * 使用系统版本
     */
    private String userVersion;
    /**
     * 图片信息
     */
    private String images;


}