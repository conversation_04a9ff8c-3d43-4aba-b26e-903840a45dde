package com.mrk.yudong.equipment.infrastructure.scale.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.mrk.yudong.equipment.api.scale.dto.BodyFatScaleFrom;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 体脂秤用户
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("equ_scale_user")
public class ScaleUser extends Model<ScaleUser> {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 关联用户id
     */
    private Long userId;

    /**
     * 昵称
     */
    @NotBlank(message = "请填写昵称")
    @Size(min = 1, max =15,message = "昵称长度在1-15个字符内")
    private String nickName;

    /**
     *  性别 0-未知，1男，2女
     */
    @NotNull(message ="请选择性别")
    private Integer sex;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 身高
     */
    @NotNull(message ="请输入身高")
    private Double height;

    /**
     * 生日
     */
    @NotBlank(message = "请选择生日")
    private String birthday;

    /**
     * 是否是主账号，1是0否
     */
    private Integer isMain;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 最后一次称重时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime lastTime;

    /**
     * 状态,0正常
     */
    private Integer status;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    private Long updateId;

    /**
     * 是否删除，1是0否
     */
    private Integer isDelete;

    /**
     * 体脂秤数据
     **/
    @TableField(exist = false)
    private BodyFatScaleFrom bodyFatScaleFrom;
    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
