package com.mrk.yudong.equipment.biz.scale.service.impl;

import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.equipment.biz.scale.service.IScaleRecordService;
import com.mrk.yudong.equipment.infrastructure.scale.mapper.ScaleRecordMapper;
import com.mrk.yudong.equipment.infrastructure.scale.model.ScaleRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 测量信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-17
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ScaleRecordServiceImpl extends BaseServiceImpl<ScaleRecordMapper, ScaleRecord> implements IScaleRecordService {


    @Override
    public List<ScaleRecord> minWeightRecordList(List<Long> measureIdList, Integer seachType) {
        return baseMapper.minWeightRecordList(measureIdList, seachType);
    }

}
