package com.mrk.yudong.equipment.infrastructure.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
@Mapper
public interface EquipmentTypeMapper extends BaseMapper<EquipmentType> {
    /**
     * 获取二级类型
     * @return
     */
    List<EquipmentType> getTwoTypeNum( @Param("isTra") Integer isTra);
    /**
     * 获取小件数据的分页
     * @param page
     * @param equipmentType
     * @return
     */
    Page<EquipmentType> getPage(IPage<EquipmentType> page, @Param("equipmentType") EquipmentType equipmentType);
    /**
     * 获取小件数据
     * @return
     */
    EquipmentType getOtherInfo();
}
