package com.mrk.yudong.equipment.infrastructure.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
@Mapper
public interface EquipmentInfoMapper extends BaseMapper<EquipmentInfo> {

    /**
     *根据用户id查询用户所有绑定的设备
     * @param userId 用户id
     * @return 所有绑定的设备
     */
    List<EquipmentInfo> getHomePage(@Param("userId") Long userId);

    /**
     * 获取用户所有绑定的设备
     * @param userId 用户id
     * @return 所有绑定的设备
     */
    List<EquipmentInfo> connectSort(@Param("userId") Long userId);

    /**
     * 删除
     * @param id
     * @return 数量
     */
    Integer physicsDeleteById(@Param("id") Long id);

    /**
     * 根据条件查询唯一设备信息
     * @param userId
     * @param productId
     * @param modelId
     * @param bluetoothName
     * @return
     */
    EquipmentInfo getByConditions(@Param("userId")Long userId,
                                  @Param("productId")Long productId,
                                  @Param("modelId")Long modelId,
                                  @Param("bluetoothName")String bluetoothName);

    /**
     * 获取最新的绑定设备
     * @param userId
     * @return
     */
    EquipmentInfo getNewestBind(@Param("userId") Long userId);

}
