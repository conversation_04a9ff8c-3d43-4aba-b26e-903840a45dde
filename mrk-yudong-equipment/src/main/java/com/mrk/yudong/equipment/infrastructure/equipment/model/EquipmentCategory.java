package com.mrk.yudong.equipment.infrastructure.equipment.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 设备分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@JsonIgnoreProperties(value = {"status", "createId", "createTime", "updateId", "updateTime"})
@Data
@EqualsAndHashCode(callSuper = false)
public class EquipmentCategory extends Model<EquipmentCategory> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 类型：100-心率带
     */
    private Integer type;

    /**
     * 名称
     */
    @TableField(exist = false)
    private String name;

    /**
     * 封面
     */
    private String cover;

    /**
     * 图标
     */
    private String icon;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
