package com.mrk.yudong.equipment.infrastructure.scale.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.equipment.infrastructure.scale.model.ScaleRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 测量信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-17
 */
public interface ScaleRecordMapper extends BaseMapper<ScaleRecord> {
    /**
     * 查询最新数据
     * @param measureIdList  测量id集合
     * @param seachType  查询类型
     * <AUTHOR>
     * @date 20:00 2022/7/7
     * @return java.util.List<com.mrk.yudong.equipment.infrastructure.scale.model.ScaleRecord>
     **/
    List<ScaleRecord> minWeightRecordList(@Param("measureIdList") List<Long> measureIdList, @Param("seachType") Integer seachType);
}
