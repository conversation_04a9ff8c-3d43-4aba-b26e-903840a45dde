package com.mrk.yudong.equipment.infrastructure.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentCategory;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 设备分类表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
public interface EquipmentCategoryMapper extends BaseMapper<EquipmentCategory> {

    /**
     * 根据类型获取设备分类
     *
     * @param categoryId
     * @param type
     * @return
     */
    EquipmentCategory getInfo(@Param("categoryId") Long categoryId, @Param("type") Integer type);

}
