package com.mrk.yudong.equipment.biz.equipment.service;

import cn.hutool.core.lang.Dict;
import com.mrk.yudong.core.service.BaseService;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipDict;

import java.util.List;

/**
 * <p>
 * 设备字典表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-09
 */
public interface IEquipDictService extends BaseService<EquipDict> {

    List<Dict> option(String code);

}
