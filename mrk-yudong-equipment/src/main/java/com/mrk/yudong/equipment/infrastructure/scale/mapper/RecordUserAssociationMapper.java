package com.mrk.yudong.equipment.infrastructure.scale.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.equipment.infrastructure.scale.model.RecordUserAssociation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 体脂秤用户与记录关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-17
 */
@Mapper
public interface RecordUserAssociationMapper extends BaseMapper<RecordUserAssociation> {

    /**
     * 最新一条正常数据
     * @param scaleUserId 体脂秤用户id
     * <AUTHOR>
     * @date 16:58 2022/6/21
     * @return com.mrk.yudong.equipment.infrastructure.scale.model.RecordUserAssociation
     **/
    RecordUserAssociation lastScaleDate(@Param("scaleUserId") Long scaleUserId,@Param("isContainManualReport") Integer isContainManualReport);
}
