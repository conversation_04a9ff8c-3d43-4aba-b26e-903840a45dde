package com.mrk.yudong.equipment.infrastructure.equipment.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mrk.yudong.share.constant.ResponseConstant;
import com.mrk.yudong.core.model.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 固件版本管理
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("equ_firmware_version")
public class FirmwareVersion extends BaseModel {

    private static final long serialVersionUID = 1L;

    /**
     * 设备分类id
     */
    @NotNull(message = ResponseConstant.EQUIP_SELECT_DEVICE_TYPE)
    private Long equipTypeId;

    /**
     * 设备型号id
     */
    @NotNull(message = "请选择设备型号")
    private Long equipModelId;

    /**
     * ota类型
     */
    @NotNull(message = "请选择ota类型")
    private Integer otaType;

    /**
     * 更新类型1:选择更新，2:强制更新
     */
    @NotNull(message = "请选择更新类型")
    private Integer updateType;

    /**
     * 编号
     */
    @NotBlank(message = "请填写模块编号")
    private String code;

    /**
     * 版本号
     */
    @NotBlank(message = "请填写固件包版本号")
    private String version;

    /**
     * 版本地址
     */
    @NotBlank(message = "请上传固件包")
    private String versionAddress;

    /**
     * 版本更新日志
     */
    @NotBlank(message = "请填写版本更新日志")
    private String updateLog;
    /**
     * 预发环境
     */
    private Integer  isTra;

    /**
     * 设备分类名称
     */
    @TableField(exist = false)
    private String  equipTypeName;
    /**
     * 设备型号名称
     */
    @TableField(exist = false)
    private String  equipModelNmae;
    /**
     * 操作人名称
     */
    @TableField(exist = false)
    private String  updateName;
    /**
     * ota类型名称
     */
    @TableField(exist = false)
    private String  otaTypeName;
    /**
     * 是否支持ota 1是0否
     */
    @TableField(exist = false)
    private Integer  isOta;
    /**
     * 是否最新版本 1是0否
     */
    @TableField(exist = false)
    private Integer  isLastVersion;
    /**
     * 是否弹窗 1是0否
     */
    @TableField(exist = false)
    private Integer  isPop;
}
