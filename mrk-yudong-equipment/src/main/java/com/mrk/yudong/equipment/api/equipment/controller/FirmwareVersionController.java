package com.mrk.yudong.equipment.api.equipment.controller;

import cn.hutool.core.util.StrUtil;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.equipment.biz.equipment.service.IFirmwareVersionService;
import com.mrk.yudong.equipment.infrastructure.equipment.model.FirmwareVersion;
import com.mrk.yudong.share.constant.ResponseConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 固件版本管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@RestController
@RequestMapping("/firmwareVersionController")
@RequiredArgsConstructor
@Slf4j
public class FirmwareVersionController {
    private final IFirmwareVersionService firmwareVersionService;

    /**
     * 获取历史版本
     *
     * @param modelId 设备型号id
     * @return R
     */
    @GetMapping("getModelFirmwareVersion")
    public R getModelFirmwareVersion(@RequestParam String modelId) {
        if (StrUtil.isNotBlank(modelId)) {
            return R.fail(ResponseConstant.PARAM_ERROR);
        }
        return R.ok(firmwareVersionService.getHistoryFirmwareVersion(modelId));
    }

    /**
     * 获取单条固件版本信息
     *
     * @param id id
     * @return 详细信息
     */
    @GetMapping("firmwareVersion")
    public R getFirmwareVersionTypeById(Long id) {
        FirmwareVersion info = new FirmwareVersion();
        if (id != null) {
            info = firmwareVersionService.getById(id);
        }
        return R.ok(info);
    }

    /**
     * 根据设备型号id获取是否需要更新固件
     *
     * @param id      设备id
     * @param modelId 型号id
     * @param code    模块编号
     * @param version 固件版本号
     * @return 详细信息
     */
    @GetMapping("FirmwareVersionLast")
    public R getFirmwareVersionLastVersion(@RequestParam("id") Long id, @RequestParam("modelId") Long modelId, @RequestParam("code") String code, @RequestParam("version") String version, String name) {
        log.info("id:{},modelId:{},code:{},version:{}", id, modelId, code, version);
        return R.ok(firmwareVersionService.getDeviceModelFromwareVersion(id, code, version, name));
    }
}
