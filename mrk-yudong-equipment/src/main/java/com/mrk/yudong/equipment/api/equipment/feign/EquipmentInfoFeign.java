package com.mrk.yudong.equipment.api.equipment.feign;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.mrk.yudong.core.annotation.Anonymous;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentInfoService;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.ResponseConstant;
import com.mrk.yudong.share.dto.equip.EquipInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 设备信息前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
@RestController
@RequestMapping("/equipment/equipmentInfoController")
@RequiredArgsConstructor
@Slf4j
public class EquipmentInfoFeign {

    private final IEquipmentInfoService equipmentInfoService;

    @Anonymous
    @GetMapping("/getUserBindProductIds")
    public Set<Long> getUserBindProductIds(Long userId) {
        if (userId == null || userId <= 0L) {
            return new HashSet<>(0);
        }
        LambdaQueryChainWrapper<EquipmentInfo> wrapper = new LambdaQueryChainWrapper<>(equipmentInfoService.getBaseMapper());
        wrapper.eq(EquipmentInfo::getUserBy,
                   userId).select(EquipmentInfo::getOneLevelTypeId);
        return wrapper.list().stream().map(EquipmentInfo::getOneLevelTypeId).collect(Collectors.toSet());
    }

    /**
     * 获取型号ID
     *
     * @param userId      用户id
     * @param equipmentId 设备分类id
     * @param modelName   设备名称
     * @return 设备型号id
     */
    @Anonymous
    @GetMapping("/query/modelId")
    public R getModelId(Long userId, Long equipmentId, String modelName) {
        BaseQuery<EquipmentInfo> baseQuery = new BaseQuery<>();
        baseQuery.eq("user_by",
                     userId).eq("one_level_type_id",
                                equipmentId).eq("bind_status",
                                                BaseConstant.INT_TRUE);
        baseQuery.eq("name",
                     modelName).select("id",
                                       "two_level_type_id");
        EquipmentInfo equipmentInfo = equipmentInfoService.getOne(baseQuery);
        if (equipmentInfo == null) {
            return R.ok();
        }
        return R.ok(equipmentInfo.getTwoLevelTypeId());
    }

    @Anonymous
    @PostMapping("/getEquipmentInfoByCondition")
    public EquipmentInfo getEquipmentInfoByCondition(@RequestBody EquipInfoDTO equipInfoDTO) {
        if (Objects.isNull(equipInfoDTO)) {
            return null;
        }
        return equipmentInfoService.getEquipmentInfoByConditions(equipInfoDTO);
    }

    /**
     * 判断是否绑定设备
     *
     * @param userId      用户id
     * @param equipmentId 设备分类id
     * @param modelIds    设备型号id
     * @return 绑定信息
     */
    @Anonymous
    @PostMapping("/getUserEquipBind")
    public R getUserEquipBind(@RequestParam("userId") Long userId, @RequestParam("equipmentId") Long equipmentId, @RequestBody List<Long> modelIds) {
        if (userId == null || CollUtil.isEmpty(modelIds) || equipmentId == null) {
            return R.fail(ResponseConstant.PARAM_ERROR);
        }
        QueryWrapper<EquipmentInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_by",
                        userId).eq("bind_status",
                                   BaseConstant.INT_TRUE).eq("one_level_type_id",
                                                             equipmentId);
        if (!modelIds.contains(100L)) {
            queryWrapper.in("two_level_type_id",
                            modelIds);
        }
        int count = equipmentInfoService.count(queryWrapper);
        int result = count > 0 ? BaseConstant.INT_TRUE : BaseConstant.INT_FALSE;
        return R.ok(result);
    }
}
