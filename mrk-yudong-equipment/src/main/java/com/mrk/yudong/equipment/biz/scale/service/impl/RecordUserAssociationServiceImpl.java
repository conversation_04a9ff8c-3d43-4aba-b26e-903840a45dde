package com.mrk.yudong.equipment.biz.scale.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.merach.sun.device.enums.ElectrodeTypeEnum;
import com.merach.sun.device.enums.ProductEnum;
import com.merach.sun.user.dto.user.LatestHealthRecordDTO;
import com.merach.sun.user.enums.UserHealthSourceEnum;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.equipment.api.scale.dto.LastScaleFrom;
import com.mrk.yudong.equipment.api.scale.dto.resp.ScaleHealthDTO;
import com.mrk.yudong.equipment.api.scale.dto.resp.TargetWeightGapDTO;
import com.mrk.yudong.equipment.api.scale.vo.*;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipDictService;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentInfoService;
import com.mrk.yudong.equipment.biz.scale.bo.ChartTrendBO;
import com.mrk.yudong.equipment.biz.scale.bo.PlanInfoListBO;
import com.mrk.yudong.equipment.biz.scale.bo.RecommendedPlanBO;
import com.mrk.yudong.equipment.biz.scale.bo.StandardListBO;
import com.mrk.yudong.equipment.biz.scale.constant.HealthConstant;
import com.mrk.yudong.equipment.biz.scale.enums.BodyTypeCopywritingEnum;
import com.mrk.yudong.equipment.biz.scale.enums.BodyTypeEnum;
import com.mrk.yudong.equipment.biz.scale.enums.SummarizeCopywritingEnum;
import com.mrk.yudong.equipment.biz.scale.service.*;
import com.mrk.yudong.equipment.feign.CourseFeign;
import com.mrk.yudong.equipment.infrastructure.device.gateway.DeviceGateway;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo;
import com.mrk.yudong.equipment.infrastructure.scale.mapper.RecordUserAssociationMapper;
import com.mrk.yudong.equipment.infrastructure.scale.model.*;
import com.mrk.yudong.equipment.infrastructure.user.gateway.UserGateway;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.equipment.EquipmentConstant;
import com.mrk.yudong.share.dto.course.CoursePlanDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 体脂秤用户与记录关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-17
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RecordUserAssociationServiceImpl extends BaseServiceImpl<RecordUserAssociationMapper, RecordUserAssociation> implements IRecordUserAssociationService {

    private final IScaleRecordService scaleRecordService;

    private final IScaleUserService scaleUserService;

    private final IHealthAttributeService healthAttributeService;

    private final IEquipmentInfoService equipmentInfoService;

    private final IEquipDictService equipDictService;

    private final CourseFeign courseFeign;

    private final IHealthStandardsService healthStandardsService;

    private final UserGateway userGateway;

    private final DeviceGateway deviceGateway;

    @Override
    public Dict lastScaleDate(LastScaleFrom lastScaleDate) {
        Dict dict = new Dict();
        dict.set("isPop", BaseConstant.INT_FALSE);
        dict.set("nickName", "");
        dict.set("lastWeight", BaseConstant.INT_FALSE);
        if (lastScaleDate.getScaleUserId() == null) {
            BaseQuery<ScaleUser> scaleRecordBaseQuery = new BaseQuery<>();
            scaleRecordBaseQuery.eq("user_id", SessionUtil.getId()).eq("is_main", BaseConstant.INT_TRUE);
            ScaleUser one = scaleUserService.getOne(scaleRecordBaseQuery);
            if (one != null) {
                lastScaleDate.setScaleUserId(one.getId());
            }
        }
        //查询体脂秤用户昵称
        ScaleUser scaleUser = scaleUserService.getById(lastScaleDate.getScaleUserId());
        if (scaleUser != null) {
            dict.set("nickName", scaleUser.getNickName());
        }
        //对比体重
        RecordUserAssociation recordUserAssociation = baseMapper.lastScaleDate(lastScaleDate.getScaleUserId(), 0);
        if (recordUserAssociation == null) {
            return dict;
        }
        BaseQuery<ScaleRecord> scaleRecordBaseQuery = new BaseQuery<>();
        scaleRecordBaseQuery.eq("health_attributes_id", HealthConstant.WEIGHT)
                .eq("measure_id", recordUserAssociation.getId());
        List<ScaleRecord> scaleRecords = scaleRecordService.list(scaleRecordBaseQuery);
        if (CollUtil.isNotEmpty(scaleRecords)) {
            ScaleRecord scaleRecord = scaleRecords.stream().findFirst().orElse(null);
            dict.set("lastWeight", scaleRecord.getHealthAttributesValue());
            boolean meet = Math.abs(Double.parseDouble(lastScaleDate.getWeight()) - Double.parseDouble(scaleRecord.getHealthAttributesValue())) > HealthConstant.RELATIVE_WEIGHT;
            if (meet) {
                dict.set("isPop", BaseConstant.INT_TRUE);
            }
        }
        return dict;
    }

    @Override
    public Dict chartTrend(ScalesTrendsVO scalesTrendsVO) {
        Dict dict = new Dict();
        dict.set("recordList", new ArrayList<>());
        dict.set("recordDays", 0);
        dict.set("trend", 0);
        BaseQuery<RecordUserAssociation> recordUserAssociationBaseQuery = new BaseQuery<>();
        if (!scalesTrendsVO.getSeachType().equals(HealthConstant.WEIGHT)) {
            //非体重去除手动上报数据
            recordUserAssociationBaseQuery.in("insert_type", UserHealthSourceEnum.SCALE_AUTOMATIC.getCode(), UserHealthSourceEnum.EIGHT_SCALE_AUTOMATIC.getCode());
        }
        if (null == scalesTrendsVO.getScaleUserId()) {
            ScaleUser mainScaleUser = scaleUserService.getMainScaleUser(SessionUtil.getId());
            if (mainScaleUser == null) {
                return dict;
            }
            scalesTrendsVO.setScaleUserId(mainScaleUser.getId());
        }

        recordUserAssociationBaseQuery.eq("scale_user_id", scalesTrendsVO.getScaleUserId())
                .le("DATE(create_time)", scalesTrendsVO.getEndDate())
                .ge("DATE(create_time)", scalesTrendsVO.getStartDate());
        List<RecordUserAssociation> recordUserAssociations = baseMapper.selectList(recordUserAssociationBaseQuery);
        if (CollUtil.isEmpty(recordUserAssociations)) {
            return dict;
        }
        List<Long> measureIdList = recordUserAssociations.stream().map(RecordUserAssociation::getId).collect(Collectors.toList());
        List<ScaleRecord> scaleRecordList = scaleRecordService.minWeightRecordList(measureIdList, scalesTrendsVO.getSeachType());
        if (CollUtil.isEmpty(scaleRecordList)) {
            return dict;
        }
        Map<Integer, HealthAttribute> healthAttributeMap = healthAttributeService.list().stream().collect(Collectors.toMap(HealthAttribute::getId, v -> v));
        //计算趋势
        ScaleRecord minScaleRecord = scaleRecordList.stream().min(Comparator.comparing(ScaleRecord::getCreateTime)).orElse(null);
        ScaleRecord maxScaleRecord = scaleRecordList.stream().max(Comparator.comparing(ScaleRecord::getCreateTime)).orElse(null);
        BigDecimal trend = new BigDecimal(maxScaleRecord.getHealthAttributesValue()).subtract(new BigDecimal(minScaleRecord.getHealthAttributesValue()));
        List<ChartTrendBO> recordList = new ArrayList<>();
        scaleRecordList.forEach(v -> {
            ChartTrendBO record = new ChartTrendBO();
            record.setTime(LocalDateTimeUtil.format(v.getCreateTime(), "MM/dd"));
            record.setTimeDate(v.getCreateTime());
            record.setPointData(v.getHealthAttributesValue());
            recordList.add(record);
        });
        if (CollUtil.isNotEmpty(recordList)) {
            dict.set("recordList", recordList.stream().sorted(Comparator.comparing(ChartTrendBO::getTimeDate)).collect(Collectors.toList()));
        } else {
            dict.set("recordList", new ArrayList<>());
        }
        dict.set("recordDays", scaleRecordList.size());
        dict.set("trend", trend.setScale(2, RoundingMode.HALF_UP).doubleValue());
        HealthAttribute healthAttribute = healthAttributeMap.get(scalesTrendsVO.getSeachType());
        if (healthAttribute != null) {
            dict.set("unit", healthAttribute.getUnit());
        } else {
            dict.set("unit", "");
        }
        return dict;
    }

    @Override
    public BodyFatScaleInfoVO bodyFatScaleInfo(Long scaleUserId) {
        BodyFatScaleInfoVO bodyFatScaleInfo = new BodyFatScaleInfoVO();
        //查询当前登录用户信息
        ScaleUser scaleUser = scaleUserService.getById(scaleUserId);
        if (scaleUser == null) {
            return bodyFatScaleInfo;
        }
        bodyFatScaleInfo.setNickName(scaleUser.getNickName());
        bodyFatScaleInfo.setAvatar(scaleUser.getAvatar());
        List<StandardInfoVO> standardInfoList = new ArrayList<>();
        RecordUserAssociation recordUserAssociation = baseMapper.lastScaleDate(scaleUserId, 0);
        if (recordUserAssociation == null) {
            return bodyFatScaleInfo;
        }
        bodyFatScaleInfo.setElectrodeType(recordUserAssociation.getElectrodeType());
        BaseQuery<ScaleRecord> scaleRecordBaseQuery = new BaseQuery<>();
        scaleRecordBaseQuery.eq("measure_id", recordUserAssociation.getId());
        List<ScaleRecord> scaleRecords = scaleRecordService.list(scaleRecordBaseQuery);
        Map<Integer, HealthAttribute> healthAttributeMap = healthAttributeService.list().stream().collect(Collectors.toMap(HealthAttribute::getId, v -> v));
        bodyFatScaleInfo.setCreateTime(recordUserAssociation.getCreateTime());
        bodyFatScaleInfo.setId(recordUserAssociation.getId());
        bodyFatScaleInfo.setStatus(recordUserAssociation.getStatus());
        scaleRecords.forEach(v -> {
            if (v.getHealthAttributesId().equals(HealthConstant.SCORE)) {
                bodyFatScaleInfo.setScore(v.getHealthAttributesValue());
            }
            if (v.getHealthAttributesId().equals(HealthConstant.WEIGHT)) {
                bodyFatScaleInfo.setWeight(v.getHealthAttributesValue());
                bodyFatScaleInfo.setWeightTrend(v.getTrend());
                bodyFatScaleInfo.setWeightTrendValue(v.getTrendValue().toString());
            }
            if (v.getHealthAttributesId().equals(HealthConstant.BMI)) {
                bodyFatScaleInfo.setBmi(v.getHealthAttributesValue());
            }
            if (v.getHealthAttributesId().equals(HealthConstant.BODYAGE)) {
                bodyFatScaleInfo.setBodyAge(v.getHealthAttributesValue());
            }
            StandardInfoVO standardInfo = new StandardInfoVO();
            standardInfo.setId(v.getId());
            standardInfo.setStandard(v.getStandardStatus());
            standardInfo.setScaleData(v.getHealthAttributesValue());
            HealthAttribute healthAttribute = healthAttributeMap.get(v.getHealthAttributesId());
            if (healthAttribute != null) {
                standardInfo.setHealthAttributesId(healthAttribute.getId());
                standardInfo.setIcon(healthAttribute.getIcon());
                if (StrUtil.isNotBlank(v.getStandardStatus())) {
                    if (v.getHealthAttributesId().equals(HealthConstant.BODYTYPE)) {
                        Optional.ofNullable(BodyTypeEnum.getBodyType(Integer.valueOf(v.getStandardStatus()), scaleUser.getSex()))
                                .ifPresent(bodyType -> standardInfo.setScaleData(bodyType.getName()));
                    } else {
                        Optional.ofNullable(healthStandardsService.getById(v.getStandardStatus()))
                                .ifPresent(healthStandards -> {
                                    standardInfo.setStandardName(healthStandards.getName());
                                    if (v.getHealthAttributesId().equals(HealthConstant.WEIGHT)) {
                                        bodyFatScaleInfo.setWeightStandardName(healthStandards.getName());
                                    }
                                    standardInfo.setBackGroundColor(healthStandards.getBackgroundColor());
                                    standardInfo.setFontColor(healthStandards.getFontColor());
                                });
                    }
                }
                standardInfo.setName(healthAttribute.getName());
                standardInfo.setUnit(healthAttribute.getUnit());
            }
            standardInfoList.add(standardInfo);
        });
        boolean standardInfoListnotEmpty = CollUtil.isNotEmpty(standardInfoList);
        List<StandardInfoVO> newStandardInfoList = new ArrayList<>();
        if (standardInfoListnotEmpty) {
            Map<Integer, StandardInfoVO> standardInfoMap = standardInfoList.stream().collect(Collectors.toMap(StandardInfoVO::getHealthAttributesId, v -> v));
            List<Integer> sortList = ElectrodeTypeEnum.FOUR.getCode().equals(recordUserAssociation.getElectrodeType()) ? HealthConstant.FOUR_BASIC_LIST : HealthConstant.EIGHT_BASIC_LIST;
            for (Integer sort : sortList) {
                StandardInfoVO standardInfoVO = standardInfoMap.get(sort);
                if (standardInfoVO != null) {
                    newStandardInfoList.add(standardInfoVO);
                } else {
                    //创建一条空数据
                    StandardInfoVO standardInfo = new StandardInfoVO();
                    healthAttributeChaneg(healthAttributeMap, standardInfo, sort);
                    newStandardInfoList.add(standardInfo);
                }
            }
        }
        if (CollUtil.isEmpty(newStandardInfoList)) {
            return bodyFatScaleInfo;
        }

        if (ElectrodeTypeEnum.FOUR.getCode().equals(recordUserAssociation.getElectrodeType())) {
            bodyFatScaleInfo.setStandardInfoList(newStandardInfoList);
        } else {
            bodyFatScaleInfo.setEightElectrodeStandards(newStandardInfoList);
        }
        return bodyFatScaleInfo;

    }

    @Override
    public List<ScaleHealthDTO> listScaleHealth(Long userId) {
        BaseQuery<EquipmentInfo> equipmentInfoBaseQuery = new BaseQuery<>();
        equipmentInfoBaseQuery.eq("user_by", userId).eq("one_level_type_id", EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_41).eq("bind_status", BaseConstant.INT_TRUE);
        boolean scaleExist = equipmentInfoService.isExist(equipmentInfoBaseQuery);
        if (!scaleExist) {
            log.info("用户未绑定体脂秤:{}", userId);
            return new ArrayList<>();
        }

        //查询体脂秤主账号信息
        ScaleUser scaleUser = scaleUserService.getOne(
                new BaseQuery<ScaleUser>()
                        .eq("user_id", userId)
                        .eq("is_main", BaseConstant.INT_TRUE)
                        .last("limit 1"));
        if (scaleUser == null) {
            log.info("用户未创建体脂秤主账号信息:{}", userId);
            return new ArrayList<>();
        }
        //查询体脂秤下最新一条数据
        RecordUserAssociation recordUserAssociation = baseMapper.lastScaleDate(scaleUser.getId(), 0);
        if (recordUserAssociation == null) {
            log.info("用户未称量:{}", userId);
            return new ArrayList<>();
        }
        List<ScaleRecord> scaleRecords = scaleRecordService.list(new BaseQuery<ScaleRecord>().eq("measure_id", recordUserAssociation.getId()));
        if (CollUtil.isEmpty(scaleRecords)) {
            return new ArrayList<>();
        }
        List<ScaleHealthDTO> collect = scaleRecords.stream().map(v -> {
            ScaleHealthDTO scaleHealthDTO = BeanUtil.copyProperties(v, ScaleHealthDTO.class);
            if (StrUtil.isBlank(v.getStandardStatus())) {
                return scaleHealthDTO;
            }
            HealthStandards healthStandards = healthStandardsService.getById(v.getStandardStatus());
            if (healthStandards != null) {
                scaleHealthDTO.setStandardStatus(healthStandards.getName());
                scaleHealthDTO.setStandardBackgroundColor(healthStandards.getBackgroundColor());
                scaleHealthDTO.setStandardFontColor(healthStandards.getFontColor());
            }
            return scaleHealthDTO;
        }).collect(Collectors.toList());
        return collect;
    }

    @Override
    public HealthReportVO healthReport(Long bodyFatScaleId) {
        log.warn("测量id:{},用户id:{}", bodyFatScaleId, SessionUtil.getId());
        //返回结果
        HealthReportVO healthReportInfo = new HealthReportVO();
        List<StandardInfoVO> standardInfoList = new ArrayList<>();
        RecordUserAssociation recordUserAssociation = baseMapper.selectById(bodyFatScaleId);
        if (recordUserAssociation == null) {
            return healthReportInfo;
        }
        //查询用户信息
        ScaleUser scaleUser = scaleUserService.getById(recordUserAssociation.getScaleUserId());
        if (scaleUser == null) {
            return healthReportInfo;
        }

        healthReportInfo.setNickName(scaleUser.getNickName());
        healthReportInfo.setAvatar(scaleUser.getAvatar());
        healthReportInfo.setSex(scaleUser.getSex());
        healthReportInfo.setHeight(scaleUser.getHeight());
        healthReportInfo.setElectrodeType(recordUserAssociation.getElectrodeType());
        healthReportInfo.setAge(LocalDate.parse(scaleUser.getBirthday(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).until(LocalDate.now(), ChronoUnit.YEARS));

        //查询称量数据
        List<ScaleRecord> scaleRecords = scaleRecordService.list("measure_id", ConditionEnum.EQ, recordUserAssociation.getId());
        if (scaleRecords == null) {
            return healthReportInfo;
        }
        //所有健康属性
        Map<Integer, HealthAttribute> healthAttributeMap = healthAttributeService.list().stream().collect(Collectors.toMap(HealthAttribute::getId, v -> v));
        healthReportInfo.setCreateTime(recordUserAssociation.getCreateTime());
        healthReportInfo.setId(recordUserAssociation.getId());
        Integer sex = scaleUser.getSex();
        List<BodyTypeEnum> bodyTypeEnums = Arrays.stream(BodyTypeEnum.values())
                .filter(bodyType -> bodyType.getSex().equals(sex))
                .sorted(Comparator.comparing(BodyTypeEnum::getSort))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(bodyTypeEnums)) {
            List<Dict> dicts = new ArrayList<>();
            bodyTypeEnums.forEach(v -> {
                Dict dict = new Dict();
                dict.put("code", v.getCode());
                dict.put("name", v.getName());
                dicts.add(dict);
            });
            List<Dict> scratchableLatex = new ArrayList<>();
            HealthConstant.SCRATCHABLE_LATEX.forEach(v -> bodyTypeEnums.forEach(bodyType -> {
                if (bodyType.getCode().equals(v)) {
                    Dict dict = new Dict();
                    dict.put("code", bodyType.getCode());
                    dict.put("name", bodyType.getName());
                    scratchableLatex.add(dict);
                }
            }));
            healthReportInfo.setScratchableLatexList(scratchableLatex);
            healthReportInfo.setBodyTypeList(dicts);
            healthReportInfo.setBodyTypeCopywriting(HealthConstant.DEFAULT_BODY_TYPE_COPYWRITING);
        }
        //体型文案
        Map<Integer, String> bodyTypeCopywritingMap = Arrays.stream(BodyTypeCopywritingEnum.values()).collect(Collectors.toMap(BodyTypeCopywritingEnum::getCode, BodyTypeCopywritingEnum::getCopywriting));
        List<HealthStandards> healthStandards = healthStandardsService.list();
        scaleRecords.forEach(v -> setResult(healthReportInfo, standardInfoList, healthAttributeMap, bodyTypeEnums, bodyTypeCopywritingMap, healthStandards, v));
        //塞入新数据及空数据填充
        List<StandardInfoVO> newStandardInfoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(standardInfoList)) {
            Map<Integer, StandardInfoVO> standardInfoMap = standardInfoList.stream().collect(Collectors.toMap(StandardInfoVO::getHealthAttributesId, v -> v));
            List<Integer> healthReportList = ElectrodeTypeEnum.FOUR.getCode().equals(recordUserAssociation.getElectrodeType()) ? HealthConstant.HEALTH_REPORT_LIST : HealthConstant.EIGHT_ELECTRODE_HEALTH_REPORT_LIST;
            for (Integer sort : healthReportList) {
                StandardInfoVO standardInfoVO = standardInfoMap.get(sort);
                if (standardInfoVO != null) {
                    newStandardInfoList.add(standardInfoVO);
                } else {
                    //创建一条空数据
                    StandardInfoVO standardInfo = new StandardInfoVO();
                    healthAttributeChaneg(healthAttributeMap, standardInfo, sort);
                    newStandardInfoList.add(standardInfo);
                }
            }
        }
        if (CollUtil.isNotEmpty(newStandardInfoList)) {
            healthReportInfo.setStandardInfoList(newStandardInfoList);
        }
        return healthReportInfo;

    }

    /**
     * 设置结果信息
     *
     * @param healthReportInfo       返回数据
     * @param standardInfoList       标准集合
     * @param healthAttributeMap     健康属性map
     * @param bodyTypeEnums          体型枚举
     * @param bodyTypeCopywritingMap 提醒文案枚举
     * @param healthStandards        健康属性标准
     * @param v                      当前数据
     * <AUTHOR>
     * @date 10:16 2022/7/27
     **/
    private void setResult(HealthReportVO healthReportInfo, List<StandardInfoVO> standardInfoList, Map<Integer, HealthAttribute> healthAttributeMap, List<BodyTypeEnum> bodyTypeEnums, Map<Integer, String> bodyTypeCopywritingMap, List<HealthStandards> healthStandards, ScaleRecord v) {
        if (v.getHealthAttributesId().equals(HealthConstant.SCORE)) {
            healthReportInfo.setScore(v.getHealthAttributesValue());
        }
        if (v.getHealthAttributesId().equals(HealthConstant.WEIGHT)) {
            healthReportInfo.setWeight(v.getHealthAttributesValue());
            healthReportInfo.setWeightTrend(v.getTrend());
            if (v.getTrendValue() != null) {
                healthReportInfo.setWeightDifference(v.getTrendValue());
            }
        }
        if (v.getHealthAttributesId().equals(HealthConstant.BMI)) {
            healthReportInfo.setBmi(v.getHealthAttributesValue());
        }
        if (v.getHealthAttributesId().equals(HealthConstant.BODYAGE)) {
            healthReportInfo.setBodyAge(v.getHealthAttributesValue());
        }
        if (v.getHealthAttributesId().equals(HealthConstant.BODYFATRATE)) {
            healthReportInfo.setBodyFatRate(v.getHealthAttributesValue());
        }
        StandardInfoVO standardInfo = new StandardInfoVO();
        standardInfo.setId(v.getId());
        standardInfo.setScaleData(v.getHealthAttributesValue());
        if (v.getHealthAttributesId().equals(HealthConstant.LEANBODYMASS)) {
            standardInfo.setStandardCopywriting(HealthConstant.LEANBODYMASS_DEFAULT_COPYWRITING);
        }
        HealthAttribute healthAttribute = healthAttributeMap.get(v.getHealthAttributesId());
        if (healthAttribute != null) {
            boolean standardNotBlank = StrUtil.isNotBlank(v.getStandardStatus());
            if (standardNotBlank) {
                //体型做额外处理
                if (v.getHealthAttributesId().equals(HealthConstant.BODYTYPE)) {
                    BodyTypeEnum bodyTypeEnum = bodyTypeEnums.stream().filter(type -> type.getCode().equals(Integer.parseInt(v.getStandardStatus()))).findFirst().orElse(null);
                    if (bodyTypeEnum != null) {
                        standardInfo.setStandard(bodyTypeEnum.getCode().toString());
                        standardInfo.setScaleData(bodyTypeEnum.getName());
                        healthReportInfo.setBodyTypeName(bodyTypeEnum.getName());
                        healthReportInfo.setBodyTypeCode(bodyTypeEnum.getCode());
                        if (MapUtil.isNotEmpty(bodyTypeCopywritingMap) && bodyTypeCopywritingMap.containsKey(bodyTypeEnum.getId())) {
                            String copywriting = bodyTypeCopywritingMap.get(bodyTypeEnum.getId());
                            standardInfo.setStandardCopywriting(copywriting);
                            healthReportInfo.setBodyTypeCopywriting(copywriting);
                        }
                    }
                } else {
                    for (HealthStandards value : healthStandards) {
                        long standardStatus = Long.parseLong(v.getStandardStatus());
                        if (value.getId().equals(standardStatus)) {
                            standardInfo.setStandard(v.getStandardStatus());
                            standardInfo.setStandardName(value.getName());
                            standardInfo.setBackGroundColor(value.getBackgroundColor());
                            standardInfo.setFontColor(value.getFontColor());
                            standardInfo.setCode(value.getId().intValue());
                            standardInfo.setStandardCopywriting(value.getStandardsCopywriting());
                        }
                    }
                }
            }
            //记录区间
            if (StrUtil.isNotBlank(v.getRemark())) {
                JSONArray jsonArray = JSONArray.parseArray(v.getRemark());
                jsonArray.forEach(k -> {
                    JSONObject jsonObject = JSONObject.parseObject(k.toString());
                    Long code = jsonObject.getLong("code");
                    //查询字段颜色
                    for (HealthStandards value : healthStandards) {
                        if (value.getId().equals(code)) {
                            ((JSONObject) k).put("rangeColor", value.getRangeColor());
                        }
                    }
                });
                standardInfo.setStandardRange(jsonArray);
            }
            standardInfo.setHealthAttributesId(healthAttribute.getId());
            standardInfo.setIcon(healthAttribute.getIcon());
            standardInfo.setName(healthAttribute.getName());
            standardInfo.setUnit(healthAttribute.getUnit());
            standardInfo.setBodyCompositionType(healthAttribute.getCompositionType());
            standardInfo.setIsSupportSkip(!HealthConstant.NO_SUPPORT_SKIP_LIST.contains(healthAttribute.getId()));
            if (StrUtil.isNotBlank(healthAttribute.getBackgroundImage())) {
                standardInfo.setBackgroundImage(healthAttribute.getBackgroundImage());
            }
            if (v.getTrend() != null) {
                standardInfo.setTrend(v.getTrend());
            }
        }
        standardInfoList.add(standardInfo);
    }

    /**
     * 设置标准信息
     *
     * @param healthAttributeMap 健康数据
     * @param standardInfo       标准信息
     * @param healthAttributesId 健康属性id
     * <AUTHOR>
     * @date 10:18 2022/7/27
     **/
    private void healthAttributeChaneg(Map<Integer, HealthAttribute> healthAttributeMap, StandardInfoVO standardInfo, Integer healthAttributesId) {
        HealthAttribute healthAttribute = healthAttributeMap.get(healthAttributesId);
        if (healthAttribute != null) {
            standardInfo.setIcon(healthAttribute.getIcon());
            standardInfo.setHealthAttributesId(healthAttributesId);
            standardInfo.setName(healthAttribute.getName());
            standardInfo.setUnit(healthAttribute.getUnit());
            standardInfo.setBodyCompositionType(healthAttribute.getCompositionType());
            standardInfo.setIsSupportSkip(!HealthConstant.NO_SUPPORT_SKIP_LIST.contains(healthAttribute.getId()));
            if (healthAttributesId.equals(HealthConstant.BODYTYPE)) {
                standardInfo.setStandardCopywriting(HealthConstant.DEFAULT_COPYWRITING);
            }
            if (healthAttributesId.equals(HealthConstant.LEANBODYMASS)) {
                standardInfo.setStandardCopywriting(HealthConstant.LEANBODYMASS_DEFAULT_COPYWRITING);
            }
            if (StrUtil.isNotBlank(healthAttribute.getBackgroundImage())) {
                standardInfo.setBackgroundImage(healthAttribute.getBackgroundImage());
            }
            //塞入标准
            JSONArray jsonArray = new JSONArray();
            if (StrUtil.isNotBlank(healthAttribute.getRemark())) {
                List<StandardListBO> standardPropertiesList = JSONArray.parseArray(healthAttribute.getRemark(), StandardListBO.class);
                standardPropertiesList.forEach(v -> {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("name", v.getName());
                    jsonObject.put("code", v.getCode());
                    jsonObject.put("maxData", 0);
                    jsonObject.put("minData", 0);
                    //查询字段颜色
                    healthStandardsService.list().stream()
                            .filter(value -> v.getCode().equals(value.getId().intValue())).findFirst().ifPresent(a -> jsonObject.put("rangeColor", a.getRangeColor()));
                    jsonArray.add(jsonObject);
                });
            }
            standardInfo.setStandardRange(jsonArray);
        }
    }

    @Override
    public String summarizeCopywriting(Long scaleUserId) {
        String bodyType = bodyTypeStandard(scaleUserId);
        if (StrUtil.isBlank(bodyType)) {
            return "";
        }
        int bodyTypeInt = Integer.parseInt(bodyType);
        SummarizeCopywritingEnum summarizeCopywritingEnum = Arrays.stream(SummarizeCopywritingEnum.values()).filter(v -> v.getCode().equals(bodyTypeInt)).findFirst().orElse(null);
        if (summarizeCopywritingEnum != null) {
            return summarizeCopywritingEnum.getCopywriting();
        }
        return "";
    }

    @Override
    public List<RecommendedPlanVO> recommendedPlan(Long scaleUserId, Long userId) {
        //查询用户是否有绑定的设备
        BaseQuery<EquipmentInfo> equipmentInfoBaseQuery = new BaseQuery<>();
        equipmentInfoBaseQuery.eq("user_by", userId).groupBy("one_level_type_id").eq("bind_status", BaseConstant.INT_TRUE).select("one_level_type_id");
        List<EquipmentInfo> equipmentInfoList = equipmentInfoService.list(equipmentInfoBaseQuery);
        //需要查询的训练计划
        List<Long> planIdList = new ArrayList<>();
        //所有的训练计划
        List<RecommendedPlanVO> recommendedPlanList = new ArrayList<>();
        String bodyType = bodyTypeStandard(scaleUserId);
        if (StrUtil.isBlank(bodyType)) {
            return recommendedPlanList;
        }
        int bodyTypeInt = Integer.parseInt(bodyType);

        //找出目前所属区间
        List<RecommendedPlanBO> planList = JSONArray.parseArray(equipDictService.getOne("dict_key", ConditionEnum.EQ, HealthConstant.RECOMMENDED_PLAN).getValue(), RecommendedPlanBO.class);
        List<PlanInfoListBO> planInfoList = null;
        for (RecommendedPlanBO recommendedPlanBO : planList) {
            if (recommendedPlanBO.getBodyType().contains(bodyTypeInt)) {
                planInfoList = recommendedPlanBO.getPlanInfoList();
            }
        }
        //无绑定设备推送非四大件类型的活动
        if (planInfoList == null) {
            return recommendedPlanList;
        }
        //徒手计划必须展示
        planInfoList.stream().filter(v -> v.getEquipTypeId().equals(0)).findFirst().ifPresent(planInfoListBO -> planIdList.addAll(planInfoListBO.getPlanIdList()));
        List<PlanInfoListBO> finalPlanInfoList = planInfoList;
        equipmentInfoList.forEach(v -> finalPlanInfoList.stream().filter(planInfo -> planInfo.getEquipTypeId().equals(v.getOneLevelTypeId().intValue())).findFirst().ifPresent(planInfoListBO -> planIdList.addAll(planInfoListBO.getPlanIdList())));

        ResDTO<List<CoursePlanDTO>> listResDTO = courseFeign.coursePlanList(planIdList);
        if (ResDTO.isFail(listResDTO)) {
            log.warn("获取计划信息失败，{}", listResDTO.getMessage());
            return recommendedPlanList;
        }
        List<CoursePlanDTO> data = listResDTO.getData();
        if (CollUtil.isNotEmpty(data)) {
            data.forEach(v -> {
                RecommendedPlanVO recommendedPlan = new RecommendedPlanVO();
                BeanUtil.copyProperties(v, recommendedPlan);
                recommendedPlanList.add(recommendedPlan);
            });
        }
        return recommendedPlanList;
    }

    @Override
    public TargetWeightGapDTO targetWeightGap() {
        List<Long> bindProductId = deviceGateway.getBindProductId(SessionUtil.getId(), ProductEnum.BODY_SCALE.getId());
        if (CollUtil.isEmpty(bindProductId)) {
            return new TargetWeightGapDTO();
        }

        TargetWeightGapDTO targetWeightGap = new TargetWeightGapDTO();
        LatestHealthRecordDTO latestWeight = userGateway.getLatestData(SessionUtil.getId());
        if (Objects.isNull(latestWeight)) {
            return new TargetWeightGapDTO();
        }
        targetWeightGap.setWeight(latestWeight.getWeight());
        targetWeightGap.setLastWeighingTime(getLastWeighingTime(LocalDateTimeUtil.of(latestWeight.getDateTime()).toLocalDate()));
        targetWeightGap.setTargetWeight(latestWeight.getTargetWeight());

        return targetWeightGap;
    }


    private String getLastWeighingTime(LocalDate createTime) {
        LocalDate now = LocalDate.now();
        if (createTime.equals(now)) {
            return "今日更新";
        } else if (createTime.isBefore(now.minusMonths(6))) {
            return "半年前更新";
        } else if (createTime.isBefore(now.minusMonths(1))) {
            return "一个月前更新";
        } else if (createTime.isBefore(now.minusDays(7))) {
            return "7天前更新";
        } else if (createTime.isBefore(now.minusDays(3))) {
            return "3天前更新";
        } else if (createTime.isBefore(now.minusDays(1))) {
            return "1天前更新";
        }
        return "";
    }

    /**
     * 获取最新数据体型
     *
     * @param scaleUserId 体脂秤用户id
     * @return java.lang.String
     * <AUTHOR>
     * @date 19:32 2022/7/4
     **/
    public String bodyTypeStandard(Long scaleUserId) {
        RecordUserAssociation recordUserAssociation = baseMapper.lastScaleDate(scaleUserId, 0);
        if (recordUserAssociation == null) {
            return null;
        }
        BaseQuery<ScaleRecord> scaleRecordBaseQuery = new BaseQuery<>();
        scaleRecordBaseQuery.eq("measure_id", recordUserAssociation.getId()).eq("health_attributes_id", HealthConstant.BODYTYPE);
        List<ScaleRecord> scaleRecords = scaleRecordService.list(scaleRecordBaseQuery);
        if (CollUtil.isNotEmpty(scaleRecords)) {
            ScaleRecord scaleRecord = scaleRecords.get(0);
            return scaleRecord.getStandardStatus();
        }
        return null;
    }
}
