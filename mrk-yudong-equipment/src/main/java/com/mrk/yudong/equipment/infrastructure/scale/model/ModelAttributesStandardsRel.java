package com.mrk.yudong.equipment.infrastructure.scale.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 型号属性标准计算关联
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("equ_model_attributes_standards_rel")
public class ModelAttributesStandardsRel extends Model<ModelAttributesStandardsRel> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 型号id
     */
    private Long modelId;

    /**
     * 标准计算id
     */
    private Long standardsId;

    /**
     * 健康属性id
     */
    private Long healthAttributesId;

    /**
     * 排序
     */
    private Integer seq;

    private Integer isDelete;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
