package com.mrk.yudong.equipment.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.List;

/**
 * @description: 彩屏编号
 * @author: ljx
 * @create: 2023/10/18 13:41
 * @Version 1.0
 **/
@ConfigurationProperties(prefix = "com.mrk.color-screen")
@Data
@RefreshScope
public class ColorScreenDeviceModelProperties {
    private List<ColorScreenModel> models;


    public static class ColorScreenModel {
        private String code;
        private Long id;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }
    }
}
