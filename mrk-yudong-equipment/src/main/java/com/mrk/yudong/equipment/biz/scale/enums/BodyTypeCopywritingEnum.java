package com.mrk.yudong.equipment.biz.scale.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/** 
 * 体脂秤体型文案
 * <AUTHOR>
 * @create 2022−07-04 11:31
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BodyTypeCopywritingEnum {

    TYPE_1(1,"放在球场上你就是妥妥的篮下霸主，但是体脂率过高会有损身体健康哦~养成良好的生活习惯从开启运动开始，一起加油吧~",1,1),

    TYPE_2(2,"肌肉率标准的身材，你一定是个热爱生活的美食达人，适当进行减脂运动，美食和美好身材都会是你的~",2,1),

    TYPE_3(3,"你结实得像头牛！如果能通过适量运动降低体脂，会更有男性魅力哦，快启动小马达，让运动唤醒你的身体吧~",3,1),

    TYPE_4(4,"你身材骨感体态轻盈，但是如果能适当增肌，你会收获更强健的体魄和活力满满的精神状态！",4,1),

    TYPE_5(5,"你身材匀称，是力量与肉感兼备的选手，女神往往容易被你撩动心弦。适当加强运动，不断散发魅力吧~",5,1),

    TYPE_6(6,"令人目不转睛的body说的就是你，浑身散发高级感，肌肉与体脂的配比堪称完美，继续保持规律的运动，做更好的自己~",6,1),

    TYPE_7(7,"你身材瘦削，少年感满分，建议再进行一些增肌运动会达到“穿衣显瘦脱衣有肉”的完美状态~",7,1),

    TYPE_8(8,"肌肉线条棱角分明，浑身上下都散发出诱人的荷尔蒙气息，继续保持规律的运动，做更好的自己~",8,1),

    TYPE_9(9,"凭借弹性十足的肌肉，你已碾压90%以上的肉体，追求极致是人生态度，MERIT就是你的主场~",9,1),

    TYPE_10(10,"你一定是个热爱生活的小吃货吧，但是体脂率过高会有损身体健康哦~养成良好的生活习惯从开启运动开始，瘦下来所有衣服都会变得合身~",10,1),

    TYPE_11(11,"你的珠圆玉润让人梦回唐朝，不过做个身材矫健的小玉环，会更让人眼前一亮喔，快开启小马达，让运动唤醒你的body吧~",11,1),

    TYPE_12(12,"你的身材凹凸有致，是别人说的“S型身材”，不过适当降低体脂，会更让人眼前一亮喔，快开启小马达，让运动唤醒你的body吧~",12,1),

    TYPE_13(13,"你身材苗条纤细，适当加强运动，腰臀比可以越来越完美，身材曲线变婀娜，不断散发自己的魅力~",13,1),

    TYPE_14(14,"你身材匀称，是紧致与肉感兼备的选手，男神往往容易被你撩动心弦。适当加强运动，不断散发自己的魅力~",14,1),

    TYPE_15(15,"令人目不转睛的body说的就是你，浑身散发高级感，腰臀比例堪称完美，继续保持规律的运动，做更好的自己~",15,1),

    TYPE_16(16,"身材娇小体态轻盈，你是猪猪女孩望尘莫及的小辣椒，适当增肌能增强体质改善睡眠状态哦~",16,1),

    TYPE_17(17,"女神没有马甲，何以闯天下，你浑身上下都散发着诱人的荷尔蒙气息，继续保持规律的运动，做更好的自己~",17,1),

    TYPE_18(18,"你的身材好到让平常女性惊骇到发指的地步，也就是人们说的“标准的S型身材”，追求极致是人生态度，MERIT就是你的主场~",18,1)
    ;

    /**
     * 编号
     */
    private Integer code;
    /**
     * 文案
     */
    private String copywriting;
    /**
     * 体型id
     */
    private Integer bodyTypeId;
    /**
     * 排序
     */
    private Integer sort;
}
