package com.mrk.yudong.equipment.biz.equipment.service;

import com.merach.sun.device.dto.resp.device.BindDeviceDTO;
import com.merach.sun.device.dto.resp.device.UserDeviceDTO;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.service.BaseService;
import com.mrk.yudong.equipment.api.equipment.dto.NoviceTutorialEquipTypeDTO;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType;
import com.mrk.yudong.share.dto.equip.EquipInfoDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
public interface IEquipmentInfoService extends BaseService<EquipmentInfo> {

    /**
     * 根据用户id查询用户设备信息
     *
     * @param userId
     * @return
     */
    List<EquipmentInfo> getNameByUserId(Long userId);

    /**
     * 首页类型
     *
     * @param userId 用户id
     * @return 具体信息
     */
    List<EquipmentInfo> getHomePage(@Param("userId") Long userId);

    /**
     * 通过id删除
     *
     * @param id 设备id
     * @return 数量
     */
    Integer deleteById(Long id);

    /**
     * 获取用户所有绑定的设备
     *
     * @param userId 用户id
     * @return 所有绑定的设备
     */
    List<EquipmentInfo> connectSort(@Param("userId") Long userId);

    R bind(EquipmentInfo equipmentInfo);

    void cacheLatestConnectionsModel(EquipmentInfo equipmentInfo, Integer expirTime);

    BindDeviceDTO doubleWrite(EquipmentType equipmentType, EquipmentInfo equipmentInfo);

    void migrationEquipInfoToDevice(Long prouctId, Long productModelId, Integer isTest);

    void migrationBindingToDevice(Long prouctId, Long productModelId, Integer isTest, List<Long> relIds);

    Long getUserFirstEquipByTypeIds(List<Long> typeIds);

    EquipmentInfo getLastConnectModelId(Long userId, Long equipTypeId);

    Long getUserLastEquipIdByType(Long equipType);

    Boolean disconnectionDevice(Long id);

    /**
     * 通过设备名称查询是否存在
     *
     * @return null
     * <AUTHOR>
     * @date 16:41 2023/3/31
     **/
    EquipmentInfo getByName(String name);

    /**
     * 根据设备类型集合获取最后绑定设备类型
     *
     * @param equipTypes
     * @return
     */
    Long getUserLastEquipIdByTypes(List<Long> equipTypes);

    String getProductGroup(Long userId);

    UserDeviceDTO getDeviceByInfoId(Long infoId);

    List<EquipmentInfo> findEquipmentByProductId(Long productId,Long userId);

    /**
     * 根据条件获取设备信息
     * @param equipInfoDTO
     * @return
     */
    EquipmentInfo getEquipmentInfoByConditions(EquipInfoDTO equipInfoDTO);

    /**
     * 新手指导课设备类型
     * @return
     */
    List<NoviceTutorialEquipTypeDTO> getNoviceTutorial();
}
