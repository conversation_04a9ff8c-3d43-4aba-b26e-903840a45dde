package com.mrk.yudong.equipment.biz.equipment.service;

import com.merach.sun.device.dto.resp.model.ProductModelDetailDTO;
import com.merach.sun.device.dto.resp.product.ProductDetailDTO;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.service.BaseService;
import com.mrk.yudong.equipment.api.equipment.dto.EquipmentBindDTO;
import com.mrk.yudong.equipment.biz.equipment.bo.EquipmentBindBO;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentBind;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentCategory;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentOther;

/**
 * <p>
 * 设备绑定明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
public interface IEquipmentBindService extends BaseService<EquipmentBind> {

    /**
     * 绑定 / 解绑设备
     *
     * @param equipmentBindBO
     * @return
     */
    R bind(ProductModelDetailDTO productModel, EquipmentBindBO equipmentBindBO);

    /**
     * 根据用户ID和设备分类ID获取最新的绑定信息
     *
     * @param userId
     * @param categoryId
     * @param bindStatus
     * @return
     */
    EquipmentBind getTopInfo(Long userId, Long categoryId, Integer bindStatus);

    /**
     * 构建绑定信息返回值
     *
     * @param equipmentCategory
     * @param equipmentOther
     * @param bindId
     * @param bluetoothName
     * @param mac
     * @return
     */
    EquipmentBindDTO buildBindDTO(EquipmentCategory equipmentCategory, EquipmentOther equipmentOther, Long bindId, String bluetoothName, String mac);

    /**
     * 中台服务转换
     *
     * @param productDetail 产品详情
     * @param productModel  产品型号
     * @param bindId        绑定id
     * @param bluetoothName 名称
     * @param mac           mac地址
     * @return com.mrk.yudong.equipment.api.equipment.dto.EquipmentBindDTO
     * <AUTHOR>
     * @date 16:19 2022/9/14
     **/
    EquipmentBindDTO newBuildBindDTO(ProductDetailDTO productDetail, ProductModelDetailDTO productModel, Long bindId, String bluetoothName, String mac);

    /**
     * 数据迁移至 equ_equipment_info
     *
     * @return com.mrk.yudong.core.model.ResDTO<java.lang.Boolean>
     * <AUTHOR>
     * @date 10:48 2022/9/23
     **/
    Boolean heartRateDataMigrationToInfo(Long prouctId, Long productModelId, Integer isTest);

    /**
     * 数据迁移 设备中台
     *
     * @return com.mrk.yudong.core.model.ResDTO<java.lang.Boolean>
     * <AUTHOR>
     * @date 10:48 2022/9/23
     **/
    void heartRateDataMigrationToDevice(Long prouctId, Long productModelId, Integer isTest);
}
