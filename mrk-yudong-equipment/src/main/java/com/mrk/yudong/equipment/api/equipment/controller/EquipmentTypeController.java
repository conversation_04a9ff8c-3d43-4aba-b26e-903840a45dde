package com.mrk.yudong.equipment.api.equipment.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.merach.sun.device.dto.resp.device.UserDeviceDTO;
import com.mrk.yudong.core.annotation.Anonymous;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.utils.RedisUtil;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.equipment.api.equipment.dto.NoviceTutorialEquipTypeDTO;
import com.mrk.yudong.equipment.api.equipment.vo.EquipFrontPageVO;
import com.mrk.yudong.equipment.api.equipment.vo.EquipSearchListVO;
import com.mrk.yudong.equipment.api.equipment.vo.ProductEncyclopediaVO;
import com.mrk.yudong.equipment.biz.equipment.constant.DictKeyConstant;
import com.mrk.yudong.equipment.biz.equipment.constant.EquipmentConstant;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipDictService;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentInfoService;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentTypeService;
import com.mrk.yudong.equipment.biz.equipment.service.IFirmwareVersionService;
import com.mrk.yudong.equipment.feign.UserFeign;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipDict;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType;
import com.mrk.yudong.equipment.infrastructure.equipment.model.FirmwareVersion;
import com.mrk.yudong.equipment.utils.EquipTypeUtil;
import com.mrk.yudong.equipment.utils.ExcelExportUtil;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.share.constant.ResponseConstant;
import com.mrk.yudong.share.dto.equip.EquipmentTypeDTO;
import com.mrk.yudong.share.dto.equip.UniqueModelIdentifyDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
@RestController
@RequestMapping("/equipment/equipmentTypeController")
@RequiredArgsConstructor
@Slf4j
public class EquipmentTypeController {

    private final IEquipmentTypeService equipmentTypeService;

    private final IEquipmentInfoService equipmentInfoService;

    private final RedisUtil redisUtil;

    private final IEquipDictService equipDictService;

    private final IFirmwareVersionService firmwareVersionService;

    private final UserFeign userFeign;

    private final EquipTypeUtil equipTypeUtil;

    private final StringRedisTemplate redisTemplate;

    /**
     * 后台-校验信息
     *
     * @param equipmentType 设备信息
     * @return 校验信息
     */
    public Map<String, Object> valid(EquipmentType equipmentType) {
        Map<String, Object> result = new HashMap<>(4);
        if (null == equipmentType.getParentId()) {
            equipmentType.setParentId(0L);
        }
        if (null == equipmentType.getLevel()) {
            equipmentType.setLevel(0);
        }
        //校验数据
        if (equipmentType.getParentId() != null && equipmentType.getParentId() != 0) {
            if (equipmentType.getTypeName() == null) {
                result.put("fail", "请填写设备型号");
                return result;
            }

            //判断是否在同一个分类下存在相同名称的设备类型
            if (equipmentType.getPrefixId() == null) {
                result.put("fail", "请选择蓝牙广播名前缀");
                return result;
            }
            if (equipmentType.getProductType() == null) {
                result.put("fail", "请选择产品类型");
                return result;
            }
            if (equipmentType.getIsOta() == null) {
                result.put("fail", "请选择是否支持ota");
                return result;
            }
            if (equipmentType.getIsClean() == null) {
                result.put("fail", "请选择是否清理运动数据");
                return result;
            }
            String id = String.valueOf(equipmentType.getParentId());
            Map<String, Object> stringObjectMap = checkElectromagneticControl(equipmentType, result, id);
            if (stringObjectMap != null) {
                return stringObjectMap;
            }
        } else {
            if (StrUtil.isEmpty(equipmentType.getIconImages())) {
                result.put("fail", "请上传icon图");
                return result;
            }
            if (StrUtil.isEmpty(equipmentType.getTypeImages())) {
                result.put("fail", "请上传设备图");
                return result;
            }

            if (equipmentType.getId() == null) {
                result.put("fail", ResponseConstant.PARAM_ERROR);
                return result;
            }
        }
        return result;
    }

    /**
     * 后台-校验是否显示和调节
     *
     * @param equipmentType 设备信息
     * @param result        错误信息
     * @param id            设备id
     * @return 错误信息
     */
    private Map<String, Object> checkElectromagneticControl(EquipmentType equipmentType, Map<String, Object> result, String id) {
        switch (id) {
            case "6": {
                //椭圆机判断阻力和坡度
                if (equipmentType.getShowResistance() == null) {
                    result.put("fail", "请选择是否可调节阻力");
                    return result;
                }
                if (equipmentType.getShowSlope() == null) {
                    result.put("fail", "请选择是否可调节坡度");
                    return result;
                }
                if (checkSlope(equipmentType, result)) {
                    return result;
                }

                if (checkResistance(equipmentType, result)) {
                    return result;
                }
                break;
            }
            case "2": {
                //跑步机
                if (equipmentType.getShowSlope() == null) {
                    result.put("fail", "请选择是否可调节坡度");
                    return result;
                }
                if (equipmentType.getShowSpeed() == null) {
                    result.put("fail", "请选择是否可调节速度");
                    return result;
                }
                if (equipmentType.getShowSpeed().equals(BaseConstant.INT_TRUE)) {
                    if (equipmentType.getMinSpeed() == null || equipmentType.getMaxSpeed() == null) {
                        result.put("fail", "请填写速度区间");
                        return result;
                    }
                }
                if (checkSlope(equipmentType, result)) {
                    return result;
                }
                break;
            }
            case "9": {
                //筋膜枪
                if (equipmentType.getShowGear() == null) {
                    result.put("fail", "请选择是否可调节档位");
                    return result;
                }
                if (equipmentType.getShowGear().equals(BaseConstant.INT_TRUE)) {
                    if (equipmentType.getMinGear() == null) {
                        result.put("fail", "请填写最小档位");
                        return result;
                    }
                    if (equipmentType.getMaxGear() == null) {
                        result.put("fail", "请填写最大档位");
                        return result;
                    }
                }
                break;
            }
            case "1":
            case "5": {
                //划船机
                //动感单车
                if (equipmentType.getShowResistance() == null) {
                    result.put("fail", "请选择是否可调节阻力");
                    return result;
                }
                if (checkResistance(equipmentType, result)) {
                    return result;
                }
                break;
            }
            default: {
                return null;
            }
        }
        return null;
    }

    private boolean checkResistance(EquipmentType equipmentType, Map<String, Object> result) {
        if (equipmentType.getShowResistance().equals(BaseConstant.INT_TRUE)) {
            if (equipmentType.getMinResistance() == null) {
                result.put("fail", "请填写最小阻力");
                return true;
            }
            if (equipmentType.getMaxResistance() == null) {
                result.put("fail", "请填写最大阻力");
                return true;
            }
        }
        return false;
    }

    private boolean checkSlope(EquipmentType equipmentType, Map<String, Object> result) {
        if (equipmentType.getShowSlope().equals(BaseConstant.INT_TRUE)) {
            if (equipmentType.getMinSlope() == null) {
                result.put("fail", "请填写最小坡度");
                return true;
            }
            if (equipmentType.getMaxSlope() == null) {
                result.put("fail", "请填写最大坡度");
                return true;
            }
        }
        return false;
    }

    /**
     * 后台-新增设备类型信息
     *
     * @param equipmentType 设备类型信息
     * @return R
     */
    @PostMapping("equipmentType")
    @Deprecated
    public R saveEquipment(@RequestBody @Valid EquipmentType equipmentType) {
        Integer isTra = SessionUtil.getIsTra();
        //校验信息
        Map<String, Object> validInfo = valid(equipmentType);
        if (MapUtil.isNotEmpty(validInfo)) {
            String fail = MapUtil.getStr(validInfo, "fail");
            if (StrUtil.isNotBlank(fail)) {
                return R.fail(fail);
            }
        }
        equipmentType.setIsTra(isTra);
        equipmentType.setCreateBy(SessionUtil.getId());
        equipmentType.setUpdateTime(LocalDateTime.now());
        boolean saveStatus = equipmentTypeService.save(equipmentType);
        if (!saveStatus) {
            return R.fail(ResponseConstant.SAVE_FAIL);
        }
//        saveDeviceProduct(equipmentType);
        //修改redis中的数据
        updateRedis(equipmentType);
        return R.ok(equipmentType);
    }

    /**
     * 后台-修改设备类型信息
     *
     * @param equipmentType 设备信息
     * @return R
     */
    @PutMapping("equipmentType")
    @Deprecated
    public R updateEquipment(@RequestBody @Valid EquipmentType equipmentType) {
        //校验信息
        Map<String, Object> validInfo = valid(equipmentType);
        if (MapUtil.isNotEmpty(validInfo)) {
            String fail = MapUtil.getStr(validInfo, "fail");
            if (StrUtil.isNotBlank(fail)) {
                return R.fail(fail);
            }
        }
        equipmentType.setUpdateBy(SessionUtil.getId());
        equipmentType.setIsTra(null);
        boolean updateStatus = equipmentTypeService.updateById(equipmentType);
        if (updateStatus) {
            //新增
            updateRedis(equipmentType);
            return R.ok(equipmentType);
        } else {
            return R.fail(ResponseConstant.UPDATE_FAIL);
        }
    }

    /**
     * 后台-修改redis和分类是否支持连接字段
     *
     * @param equipmentType 设备分类、类型信息
     */
    private void updateRedis(EquipmentType equipmentType) {
        equipmentType = equipmentTypeService.getById(equipmentType.getId());
        redisUtil.setCacheMapValue(RedisKeyConstant.EQUIPMENT_TYPE_KEY, equipmentType.getId().toString(), JSONUtil.toJsonStr(equipmentType));
        redisTemplate.opsForHash().put(RedisKeyConstant.ALL_EQUIPMENT_TYPE_KEY, equipmentType.getId().toString(), JSON.toJSONString(equipmentType));
        if (equipmentType.getLevel().equals(EquipmentConstant.LEVEL_ONE)) {
            EquipmentType typeInfo = equipmentTypeService.getById(equipmentType.getParentId());
            BaseQuery<EquipmentType> equipmentTypeBaseQuery = new BaseQuery<>();
            equipmentTypeBaseQuery.eq("parent_id", equipmentType.getParentId()).eq("is_support_connection", BaseConstant.INT_TRUE);
            List<EquipmentType> list = equipmentTypeService.list(equipmentTypeBaseQuery);
            if (CollUtil.isNotEmpty(list)) {
                typeInfo.setIsSupportConnection(BaseConstant.INT_TRUE);
            } else {
                typeInfo.setIsSupportConnection(BaseConstant.INT_FALSE);
            }
            equipmentTypeService.updateById(typeInfo);
            redisTemplate.opsForHash().put(RedisKeyConstant.ALL_EQUIPMENT_TYPE_KEY, typeInfo.getId().toString(), JSON.toJSONString(typeInfo));
            redisUtil.setCacheMapValue(RedisKeyConstant.EQUIPMENT_TYPE_KEY, typeInfo.getId().toString(), JSON.toJSONString(typeInfo));
        }
    }

    /**
     * 后台-拖动排序
     *
     * @param equipmentType 设备信息
     * @return R
     */
    @PostMapping("updateSort")
    @Deprecated
    public R updateSort(@RequestBody List<EquipmentType> equipmentType) {
        boolean b = equipmentTypeService.updateBatchById(equipmentType);
        if (b) {
            return R.ok();
        }
        return R.fail(ResponseConstant.SAVE_FAIL);
    }

    /**
     * 后台-设备类型分页信息
     *
     * @param param 分页参数
     * @return R 具体信息
     */
    @GetMapping("equipmentTypePage")
    @Deprecated
    public R getEquipmentPage(@RequestParam Map<String, Object> param) {
        Integer current = MapUtil.getInt(param, "current", 1);
        Integer size = MapUtil.getInt(param, "size", 10);

        EquipmentType equipmentType = new EquipmentType();

        //目录等级
        Integer level = MapUtil.getInt(param, "level");
        if (level != null) {
            equipmentType.setLevel(level);
        }
        //是否拆分小件为其他 1是0否
        Integer isSplit = MapUtil.getInt(param, "isSplit");
        if (isSplit == null) {
            assert level != null;
            if (level.equals(0)) {
                //没有传参只查运动类型设备和其他
                equipmentType.setType(1);
            }
        }
        if (isSplit != null && isSplit.equals(0)) {
            assert level != null;
            if (level.equals(0)) {
                //不拆分则只显示其他
                equipmentType.setType(1);
            }
        }
        //上级id
        Long parentId = MapUtil.getLong(param, "parentId");
        if (parentId != null) {
            equipmentType.setParentId(parentId);
        }
        //设备型号
        String typeName = MapUtil.getStr(param, "typeName");
        if (StrUtil.isNotEmpty(typeName)) {
            equipmentType.setTypeName(typeName);
        }
        //设备型号
        String code = MapUtil.getStr(param, "code");
        if (StrUtil.isNotEmpty(code)) {
            equipmentType.setCode(code);
        }
        //产品类型
        Integer productType = MapUtil.getInt(param, "productType");
        if (productType != null) {
            equipmentType.setProductType(productType);
        }
        equipmentType.setIsTra(SessionUtil.getIsTra());
        IPage<EquipmentType> page = new Page<>(current, size);
        page = equipmentTypeService.getPage(page, equipmentType);
        //信息转换测试
        if (CollUtil.isNotEmpty(page.getRecords())) {
            page = exchangePage(page);
        }
        return R.ok(page);
    }


    public IPage<EquipmentType> exchangePage(IPage<EquipmentType> page) {
        List<EquipmentType> equipmentTypeList = equipmentTypeService.list("level", ConditionEnum.EQ, 1);
        //获取二级类型数量
        Map<Long, List<EquipmentType>> twoTypeNumMap = equipmentTypeList.stream().collect(Collectors.groupingBy(EquipmentType::getParentId));
        //设备前缀
        Map<Integer, String> prefixMap = equipDictService.list().stream().collect(Collectors.toMap(EquipDict::getId, EquipDict::getValue));
        //ota版本信息
        Map<String, String> otaNameMap = Convert.toList(EquipDict.class, equipDictService.list("dict_key", ConditionEnum.EQ, DictKeyConstant.OTA_TYPE))
                .stream().collect(Collectors.toMap(EquipDict::getValue, EquipDict::getName));
        List<Long> versionIds = page.getRecords().stream().map(EquipmentType::getTypeVersionId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, FirmwareVersion> firmwareVersionMap = null;
        if (CollUtil.isNotEmpty(versionIds)) {
            List<FirmwareVersion> firmwareVersions = firmwareVersionService.listByIds(versionIds);
            firmwareVersionMap = firmwareVersions.stream().collect(Collectors.toMap(FirmwareVersion::getId, v -> v));
        }
        //数据转换
        for (int i = 0; i < page.getRecords().size(); i++) {
            EquipmentType item = page.getRecords().get(i);
            if (twoTypeNumMap.containsKey(item.getId())) {
                List<EquipmentType> equipmentTypes = twoTypeNumMap.get(item.getId()).stream().filter(v -> v.getIsDelete().equals(BaseConstant.INT_FALSE)).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(equipmentTypes)) {
                    Integer num = equipmentTypes.size();
                    item.setTypeNum(num);
                }
            }
            String prefixVlue = MapUtil.getStr(prefixMap, item.getPrefixId());
            if (StrUtil.isNotBlank(prefixVlue)) {
                item.setPrefixName(prefixVlue);
            }
            FirmwareVersion firmwareVersion = MapUtil.get(firmwareVersionMap, item.getTypeVersionId(), FirmwareVersion.class);
            if (firmwareVersion != null) {
                if (StrUtil.isNotBlank(firmwareVersion.getVersion())) {
                    item.setTypeVersion(firmwareVersion.getVersion());
                }
                if (firmwareVersion.getOtaType() != null) {
                    if (MapUtil.isNotEmpty(otaNameMap)) {
                        String name = MapUtil.getStr(otaNameMap, firmwareVersion.getOtaType().toString());
                        if (StrUtil.isNotBlank(name)) {
                            item.setAgreement(name);
                        }
                    }
                }
            }
        }
        return page;
    }

    /**
     * 后台-导出
     *
     * @param response 响应信息
     * @param param    参数
     */
    @PostMapping("/export")
    @Deprecated
    public void export(HttpServletResponse response, @RequestBody Map<String, Object> param) {
        String filename = "设备类型信息";
        BaseQuery<EquipmentType> condition = new BaseQuery<>();
        //上级id
        Integer parentId = MapUtil.getInt(param, "parentId");
        if (parentId != null) {
            condition.eq("parent_id", parentId);
        }
        //目录等级
        String level = MapUtil.getStr(param, "level");
        if (StrUtil.isNotEmpty(level)) {
            condition.eq("level", level);
        }
        //设备型号
        String typeName = MapUtil.getStr(param, "typeName");
        if (StrUtil.isNotEmpty(typeName)) {
            condition.likeRight("type_name", typeName);
        }
        //产品类型
        String productType = MapUtil.getStr(param, "productType");
        if (StrUtil.isNotEmpty(productType)) {
            condition.eq("product_type", productType);
        }
        condition.le("is_tra", SessionUtil.getIsTra());
        condition.orderByAsc("sort");
        List<Map<String, Object>> list = equipmentTypeService.listMaps(condition);
        List<String> cellName = new ArrayList<>();
        cellName.add("蓝牙广播前缀名");
        cellName.add("设备型号");
        cellName.add("传播名");
        cellName.add("协议");
        cellName.add("产品类型");
        cellName.add("固件型号");
        List<String> cellValue = new ArrayList<>();
        //设备前缀
        Map<Integer, String> prefixMap = equipDictService.list().stream().collect(Collectors.toMap(EquipDict::getId, EquipDict::getValue));
        //版本信息
        List<Long> versionIds = list.stream().map(item -> MapUtil.getLong(item, "type_version_id")).collect(Collectors.toList());
        Map<Long, FirmwareVersion> firmwareVersionMap = null;
        if (CollUtil.isNotEmpty(versionIds)) {
            List<FirmwareVersion> firmwareVersions = firmwareVersionService.listByIds(versionIds);
            firmwareVersionMap = firmwareVersions.stream().collect(Collectors.toMap(FirmwareVersion::getId, v -> v));
        }
        //ota类型
        Map<String, String> otaNameMap = Convert.toList(EquipDict.class, equipDictService.list("dict_key", ConditionEnum.EQ, DictKeyConstant.OTA_TYPE))
                .stream().collect(Collectors.toMap(EquipDict::getValue, EquipDict::getName));
        cellValue.add("prefix_id");
        cellValue.add("code");
        cellValue.add("type_name");
        cellValue.add("ota_type");
        cellValue.add("product_type");
        cellValue.add("typeVersion");
        if (CollUtil.isNotEmpty(list)) {
            exchange(list, prefixMap, firmwareVersionMap, otaNameMap);
        }
        ExcelExportUtil.export(response, filename, cellName, cellValue, list);
    }

    /***
     * 导出数据转换
     * @param list 设备集合信息
     * @param prefixMap 前缀集合
     * @param firmwareVersionMap 固件版本集合
     * @param otaNameMap ota名称集合
     */
    private void exchange(List<Map<String, Object>> list, Map<Integer, String> prefixMap, Map<Long, FirmwareVersion> firmwareVersionMap, Map<String, String> otaNameMap) {
        for (Map<String, Object> dict : list) {
            //蓝牙广播前缀名
            Integer prefixId = MapUtil.getInt(dict, "prefix_id");
            if (prefixId != null) {
                String prefixName = MapUtil.getStr(prefixMap, prefixId);
                if (StrUtil.isNotBlank(prefixName)) {
                    dict.put("prefix_id", prefixName);
                }
            }
            //协议与固件型号
            Long typeVersionId = MapUtil.getLong(dict, "type_version_id");
            if (typeVersionId != null) {
                FirmwareVersion firmwareVersion = MapUtil.get(firmwareVersionMap, typeVersionId, FirmwareVersion.class);
                if (firmwareVersion != null) {
                    if (firmwareVersion.getOtaType() != null) {
                        if (MapUtil.isNotEmpty(otaNameMap)) {
                            String name = MapUtil.getStr(otaNameMap, firmwareVersion.getOtaType().toString());
                            if (StrUtil.isNotBlank(name)) {
                                dict.put("ota_type", name);
                            }
                        }
                    }
                    //版本号
                    if (firmwareVersion.getVersion() != null) {
                        dict.put("typeVersion", firmwareVersion.getVersion());
                    }
                }
            }
            //产品类型,1:彩屏，2：蓝牙电子表，3：wifi电子表
            Integer productTypeParam = MapUtil.getInt(dict, "product_type");
            if (productTypeParam != null) {
                String productTypeName = null;
                switch (productTypeParam) {
                    case 1:
                        productTypeName = "彩屏";
                        break;
                    case 2:
                        productTypeName = "蓝牙电子表";
                        break;
                    case 3:
                        productTypeName = "wifi电子表";
                        break;
                    default: {
                        break;
                    }
                }
                dict.put("product_type", productTypeName);
            }

        }
    }


    /**
     * app-（首页右侧菜单栏）设备类型一级icon，类型名称，id，是否有绑定设备
     * firmwareVersionPage
     *
     * @param param 参数
     * @return R
     */
    @GetMapping("getAssociateList")
    public R getAssociateList(@RequestParam Map<String, Object> param) {
        Integer current = MapUtil.getInt(param, "current", 1);
        Integer size = MapUtil.getInt(param, "size", 99);

        Integer isShowOther = MapUtil.getInt(param, "isShowOther", 0);

        int type = MapUtil.getInt(param, "type", 1);
        //一级设备类型查询
        BaseQuery<EquipmentType> condition = new BaseQuery<>();
        condition.select("id", "type_name", "icon_images", "big_icon_images", "binding_image", "unbound_image", "sort", "type").eq("level", 0).le("is_tra", SessionUtil.getIsTra());
        if (type == 1) {//展示运动设备及部分特殊设备
            condition.eq("type", 1).or().eq("is_show", 1);
        } else if (type == 2) { //运动设备
            condition.eq("type", 1);
        } else if (type == 3) {//健康设备
            condition.eq("type", 3);
        } else if (type == 4) {//不展示四大件
            condition.notIn("id", List.of(1L,2L,5L,6L)).in("type", List.of(1L,3L));
        }
        IPage<EquipmentType> page = new Page<>(current, size);
        page = equipmentTypeService.page(page, condition);
        //查询该用户绑定设备数量
        Long userId = SessionUtil.getId();
        List<EquipmentInfo> infoSet = equipmentInfoService.getHomePage(userId);
        IPage<EquipmentType> finalPage = page;
        infoSet.forEach(info -> finalPage.getRecords().forEach(item -> {
                    if (info.getOneLevelTypeId().equals(item.getId())) {
                        item.setTypeNum(1);
                        item.setBindTime(info.getBindTime());
                    }
                })
        );
        List<EquipmentType> equipmentTypeList = page.getRecords().stream().sorted(
                        Comparator.comparing(EquipmentType::getBindTime, Comparator.nullsFirst(LocalDateTime::compareTo)).reversed().thenComparing(EquipmentType::getSort))
                .collect(Collectors.toList());
        //添加燃脂小件
        if (isShowOther.equals(EquipmentConstant.ONE)) {
            EquipmentType otherInfo = equipmentTypeService.getOtherInfo();
            equipmentTypeList.add(otherInfo);
        }
        page.setRecords(equipmentTypeList);
        return R.ok(page);
    }

    /**
     * app-海外版设备信息首页接口
     *
     * @return R
     */
    @GetMapping("equipFrontPage")
    public R equipFrontPage() {
        //一级设备类型查询
        BaseQuery<EquipmentType> condition = new BaseQuery<>();
        condition.select("id", "type_name", "type_images", "icon_images", "big_icon_images", "binding_image", "unbound_image", "sort").eq("level", 0).eq("type", 1).le("is_tra", SessionUtil.getIsTra()).or().eq("is_show", 1);
        List<EquipmentType> equipmentTypes = equipmentTypeService.list(condition);
        //查询该用户绑定设备数量
        Long userId = SessionUtil.getId();
        List<EquipmentInfo> infoSet = equipmentInfoService.getHomePage(userId);
        infoSet.forEach(info -> equipmentTypes.forEach(item -> {
                    if (info.getOneLevelTypeId().equals(item.getId())) {
                        item.setTypeNum(1);
                        item.setBindTime(info.getBindTime());
                    }
                })
        );
        //tab
        List<EquipmentType> equipmentTypeList = equipmentTypes.stream().sorted(
                Comparator.comparing(EquipmentType::getBindTime, Comparator.nullsFirst(LocalDateTime::compareTo)).reversed().thenComparing(EquipmentType::getSort)).collect(Collectors.toList());
        //连接数据
        List<EquipmentInfo> connectInfo = equipmentInfoService.connectSort(userId);
        List<EquipFrontPageVO> equipFrontPageList = new ArrayList<>();
        equipmentTypeList.forEach(item -> {
            EquipFrontPageVO equipFrontPageVO = new EquipFrontPageVO();
            equipFrontPageVO.setEquipTypeId(item.getId());
            equipFrontPageVO.setEquipTypeName(item.getTypeName());
            equipFrontPageVO.setTypeNum(item.getTypeNum());
            equipFrontPageVO.setBindingImage(item.getBindingImage());
            equipFrontPageVO.setUnboundImage(item.getUnboundImage());
            List<EquipmentInfo> infos = connectInfo.stream().filter(v -> Objects.equals(v.getOneLevelTypeId(), item.getId())).sorted(
                            Comparator.comparing(EquipmentInfo::getConnectTime, Comparator.nullsFirst(LocalDateTime::compareTo)).reversed())
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(infos)) {
                equipFrontPageVO.setEquipName(infos.get(0).getName());
                equipFrontPageVO.setEquipImage(infos.get(0).getTwoLevelImage());
            }
            equipFrontPageList.add(equipFrontPageVO);
        });
        return R.ok(equipFrontPageList);
    }

    /**
     * 设备信息类型集合
     *
     * @param param 参数
     * @return R
     */
    @GetMapping("equipmentTypeList")
    public R getEquipmentList(@RequestParam Map<String, Object> param) {
        BaseQuery<EquipmentType> condition = new BaseQuery<>();
        String parentId = MapUtil.getStr(param, "parentId");
        if (StrUtil.isNotEmpty(parentId)) {
            condition.eq("parent_id", parentId);
        }
        condition.le("is_tra", SessionUtil.getIsTra());
        condition.orderByAsc("sort");
        List<EquipmentType> list = equipmentTypeService.list(condition);
        return R.ok(list);
    }

    /**
     * 获取单条设备类型信息
     *
     * @param id id
     * @return R 具体信息
     */
    @Anonymous
    @GetMapping("equipmentTypeInfo")
    public R getEquipmentTypeById(Long id) {
        if (id == null) {
            return R.fail(ResponseConstant.PARAM_ERROR);
        }
        EquipmentType info = equipmentTypeService.getById(id);
        return R.ok(info);
    }

    /**
     * 获取单条设备类型信息
     *
     * @param ids ids
     * @return R 具体信息
     */
    @Anonymous
    @PostMapping("getEquipmentTypeList")
    public R getEquipmentTypeList(@RequestBody List<Long> ids) {
        List<EquipmentType> types;
        if (CollUtil.isEmpty(ids)) {
            return R.fail("非法参数");
        }
        types = equipmentTypeService.listByIds(ids);
        return R.ok(types);
    }

    /**
     * 获取所有设备类型id，name,images
     * arms近三个月无调用
     *
     * @return R
     */
    @GetMapping("equipmentTypeName")
    @Deprecated
    public R getEquipmentTypeName() {
        List<EquipmentType> info = equipmentTypeService.getEquipmentTypeName();
        return R.ok(info);
    }


    /**
     * 判断是否需要弹出设备连接
     *
     * @param equipmentId 产品id
     * @return R
     */
    @GetMapping("/equipTypeIcon")
    public R equipTypeIcon(Long equipmentId) {
        log.debug("这里是equip下的equipTypeIcon接口");
        if (equipmentId == null) {
            return R.fail(ResponseConstant.PARAM_ERROR);
        }
        BaseQuery<EquipmentType> baseQuery = new BaseQuery<>();
        baseQuery.select("dark_icon", "bright_icon").eq("id", equipmentId);
        EquipmentType type = equipmentTypeService.getOne(baseQuery);
        if (ObjectUtil.isNull(type)) {
            return R.ok(new EquipmentType());
        }
        return R.ok(type);
    }

    /**
     * 通过设备id获取设备所属大类
     *
     * @return R
     */
    @Anonymous
    @GetMapping("getOneTypeByequipId")
    public R getOneTypeByequipId(Long equipId) {
        log.debug("这里是equip下的getOneTypeByequipId接口");
        if (equipId == null) {
            return R.fail("非法参数");
        }
        List<EquipmentTypeDTO> returnInfo = new ArrayList<>();
        EquipmentInfo equipmentInfo = equipmentInfoService.getById(equipId);
        if (equipmentInfo == null || equipmentInfo.getTwoLevelTypeId() == null) {
            return R.ok(returnInfo);
        }
        List<EquipmentInfo> equipmentInfoList = new ArrayList<>();
        equipmentInfoList.add(equipmentInfo);
        returnInfo = equipmentTypeService.getMysqlEquipType(equipmentInfoList);
        return R.ok(returnInfo);
    }

    /**
     * 通过id获取设备所有信息（提供给外部服务）
     *
     * @return R
     */
    @PostMapping("getTypeById")
    @Anonymous
    public R getTypeById(@RequestBody List<String> id) {
        List<EquipmentType> info = new ArrayList<>();
        if (CollUtil.isNotEmpty(id)) {
            info = equipmentTypeService.listByIds(id);
        }
        return R.ok(info);
    }

    /**
     * 获取设备分类
     *
     * @return R
     */
    @GetMapping("getOneTypeId")
    @Anonymous
    public R getOneTypeId() {
        log.debug("这里是equip下的getOneTypeId接口");
        BaseQuery<EquipmentType> baseQuery = new BaseQuery<>();
        baseQuery.select("id", "type_name", "icon_images", "tag_icon").eq("level", 0);
        baseQuery.le("is_tra", SessionUtil.getIsTra());
        List<EquipmentType> info = equipmentTypeService.list(baseQuery);
        return R.ok(info);
    }

    /**
     * 判断是否需要弹出设备连接（无接口调用）
     *
     * @param equipmentId 暂时不使用
     * @return R
     */
    @Anonymous
    @GetMapping("/isSupportConnection")
    @Deprecated
    public R isSupportConnection(Long equipmentId) {
        log.debug("这里是equip下的isSupportConnection接口");
        if (equipmentId == null) {
            return R.fail(ResponseConstant.PARAM_ERROR);
        }
        boolean result = false;
        BaseQuery<EquipmentType> baseQuery = new BaseQuery<>();
        baseQuery.eq("parent_id", equipmentId).eq("is_support_connection", 1);
        baseQuery.le("is_tra", SessionUtil.getIsTra());
        int count = equipmentTypeService.count(baseQuery);
        if (count > 0) {
            result = true;
        }
        return R.ok(result);
    }

    /**
     * 彩屏设备信息
     *
     * @param name 名称
     * @return 彩屏设备
     */
    @GetMapping("colorScreenEquip")
    public R colorScreenEquip(String name) {
        return R.ok(equipDictService.getOne("dict_key", ConditionEnum.EQ, name, "value"));
    }


    /**
     * 支持连接的设备信息
     *
     * @return 设备列表
     */
    @Anonymous
    @GetMapping("supportConnectionEquipList")
    public R supportConnectionEquipList() {
        return R.ok(equipmentTypeService.supportConnectionEquipList());
    }

    /**
     * 设备常见问题详情
     *
     * @param equipmodelId 型号id
     * @return 详情信息
     */
    @Anonymous
    @GetMapping("equipmodelFkq")
    public R equipmodelFkq(Long equipmodelId) {
        if (equipmodelId == null) {
            return R.paramFail(ResponseConstant.PARAM_ERROR);
        }
        ProductEncyclopediaVO productEncyclopediaVO = new ProductEncyclopediaVO();
        EquipmentType equipmentType = equipmentTypeService.getById(equipmodelId);
        if (equipmentType == null) {
            return R.fail(ResponseConstant.EQUIP_INVALID);
        }
        productEncyclopediaVO.setEquipTypeId(equipmentType.getParentId());
        productEncyclopediaVO.setEquipModelId(equipmentType.getId());
        productEncyclopediaVO.setEquipModelName(equipmentType.getTypeName());
        productEncyclopediaVO.setEquipModelImage(equipmentType.getTypeImages());
        if (StrUtil.isNotBlank(equipmentType.getInstallTutorial())) {
            productEncyclopediaVO.setInstallTutorial(equipmentType.getInstallTutorial());
        }
        return R.ok(productEncyclopediaVO);
    }

    /**
     * 通过设备名称查询设备信息
     *
     * @param equipTypeId 设备分类id
     * @param equipName   设备名称
     * @return 设备信息
     */
    @Anonymous
    @GetMapping("equipByName")
    @Deprecated
    public R equipByName(Long equipTypeId, String equipName) {
        //获取蓝牙名称中的型号
        Map<String, Object> typeInfo = equipTypeUtil.getTypeCode(equipName);
        String typeCode = MapUtil.getStr(typeInfo, "typeCode");
        Integer prefix = MapUtil.getInt(typeInfo, "prefix");
        if (prefix == null) {
            return R.fail(ResponseConstant.BINDING_SUPPORTED);
        }

        //去数据库中查出对应型号数据
        EquipmentType equipmentType;
        //前缀就是数据的信息
        if (StrUtil.isBlank(typeCode)) {
            //无法确定型号的设备可以不需要型号
            equipmentType = equipmentTypeService.getTypeId(null, prefix, equipTypeId).stream().findFirst().orElse(null);
        } else {
            equipmentType = equipmentTypeService.getTypeId(typeCode, prefix, null).stream().findFirst().orElse(null);
        }
        return R.ok(equipmentType);
    }


    /**
     * 通过设备名称查询设备信息(临时接口，后续迁移到设备中台)
     *
     * @param equipName 设备名称
     * @return 设备信息
     */
    @GetMapping("getModelCode")
    @Anonymous
    public ResDTO<UniqueModelIdentifyDTO> getModelCode(@RequestParam("equipName") String equipName) {
        Map<String, Object> typeInfo = equipTypeUtil.getTypeCode(equipName);
        String typeCode = MapUtil.getStr(typeInfo, "typeCode");
        Integer prefix = MapUtil.getInt(typeInfo, "prefix");
        if (prefix == null) {
            return ResDTO.fail(ResponseConstant.BINDING_SUPPORTED);
        }

        return ResDTO.ok(new UniqueModelIdentifyDTO(typeCode, prefix));
    }

    /**
     * 通过设备名称查询设备信息
     *
     * @param equipTypeId 设备分类id
     * @param equipName   设备名称
     * @return 设备信息
     */
    @Anonymous
    @GetMapping("equipByName/v2")
    public R equipByNamev2(Long equipTypeId, String equipName, Long userId) {
        //获取蓝牙名称中的型号
        log.info("equipByNamev2 equipTypeId:{},equipName:{},userId:{}", equipTypeId, equipName, userId);
        List<EquipmentType> models = equipmentTypeService.getModel(equipName, equipTypeId);
        if (CollUtil.isEmpty(models)) {
            return R.fail(ResponseConstant.EQUIP_INVALID);
        }
        EquipmentType equipmentType = models.get(0);
        BaseQuery<EquipmentInfo> infoBaseQuery = new BaseQuery<>();
        infoBaseQuery.eq("name", equipName).eq("one_level_type_id", equipTypeId).eq("user_by", userId).last("limit 1");
        EquipmentInfo info = equipmentInfoService.getOne(infoBaseQuery);
        if (info != null) {
            String remark = info.getRemark();
            if (StrUtil.isNotBlank(remark)) {
                JSONObject jsonObject = JSONObject.parseObject(remark);
                String meritEigenValue = jsonObject.getString(com.mrk.yudong.share.constant.equipment.EquipmentConstant.MERIT_EIGEN_VALUE);
                equipmentType = equipmentTypeService.equipmentTypeChange(equipmentType, meritEigenValue, equipName);
            }
            UserDeviceDTO device = equipmentInfoService.getDeviceByInfoId(info.getId());
            if (device != null) {
                equipmentType.setIsSupportResistanceEcho(device.getIsSupportResistanceEcho());
                equipmentType.setModelId(device.getProductModelId());
            }

        }
        log.info("设备信息：{}", equipmentType);
        return R.ok(equipmentType);
    }

    /**
     * 通过设备分类id查询设备列表
     *
     * @param equipTypeId 设备分类id 41-体脂秤
     * @return com.mrk.yudong.core.model.ResDTO<java.util.List < com.mrk.yudong.equipment.api.equipment.vo.EquipSearchListVO>>
     * <AUTHOR>
     * @date 19:49 2022/6/14
     **/
    @GetMapping("equipSearchList")
    public ResDTO<List<EquipSearchListVO>> EquipSearchListVO(Long equipTypeId) {
        List<EquipmentType> typeList = equipmentTypeService.list("parent_id", ConditionEnum.EQ, equipTypeId);
        List<EquipSearchListVO> equipSearchList = new ArrayList<>();
        typeList.forEach(v -> {
            EquipSearchListVO equipSearch = new EquipSearchListVO();
            BeanUtil.copyProperties(v, equipSearch);
            equipSearch.setEquipName(v.getTypeName());
            equipSearch.setImage(v.getTypeImages());
            equipSearch.setIsMerach(v.getIsElectromagneticControl());
            equipSearch.setOneLevelTypeId(equipTypeId);
            equipSearch.setTwoLevelTypeId(v.getId());
            String remark = v.getRemark();
            if (StrUtil.isNotBlank(remark)) {
                JSONObject jsonObject = JSONObject.parseObject(remark);
                equipSearch.setCopywriting(jsonObject.getString("copywriting"));
                equipSearch.setCopywritingImage(jsonObject.getString("copywritingImage"));
            }
            equipSearchList.add(equipSearch);
        });
        return ResDTO.ok(equipSearchList);
    }

    /**
     * 查询体脂秤最新的一条数据的code和名称
     *
     * @param equipTypeId 设备分类id
     * @return com.mrk.yudong.core.model.ResDTO<java.util.List < com.mrk.yudong.equipment.api.equipment.vo.EquipSearchListVO>>
     * <AUTHOR>
     * @date 19:49 2022/6/14
     **/
    @GetMapping("latestScales")
    public ResDTO<Dict> latestScales(Long equipTypeId) {
        Long userId = SessionUtil.getId();
        if (userId == null) {
            return ResDTO.fail(ResponseConstant.COMMON_NOUSER);
        }
        Dict dict = new Dict();
        BaseQuery<EquipmentInfo> baseQuery = new BaseQuery<>();
        baseQuery.eq("one_level_type_id", equipTypeId).eq("bind_status", BaseConstant.INT_TRUE).eq("user_by", userId);
        EquipmentInfo info = equipmentInfoService.getOne(baseQuery);
        if (info != null) {
            dict.set("code", info.getCode());
            if (StrUtil.isNotBlank(info.getBluetoothAlias())) {
                dict.set("typeName", info.getBluetoothAlias());
            } else {
                EquipmentType equipmentType = equipmentTypeService.getById(info.getTwoLevelTypeId());
                dict.set("typeName", equipmentType.getTypeName());
            }
            dict.set("id", info.getId());
        }

        return ResDTO.ok(dict);
    }


    /**
     * 新手指导课设备类型接口
     *
     * @return ResDTO
     */
    @GetMapping("noviceTutorial")
    public ResDTO<List<NoviceTutorialEquipTypeDTO>> getNoviceTutorial() {
        return ResDTO.ok(equipmentInfoService.getNoviceTutorial());
    }
}
