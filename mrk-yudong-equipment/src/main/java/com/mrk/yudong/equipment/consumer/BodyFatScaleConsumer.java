package com.mrk.yudong.equipment.consumer;

import com.alibaba.fastjson.JSONObject;
import com.merach.sun.device.constant.MqConstant;
import com.mrk.yudong.equipment.biz.scale.bo.BodyFatScaleBO;
import com.mrk.yudong.equipment.biz.scale.service.IScaleReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
@RocketMQMessageListener(topic = "${rocketmq.device.topic}", consumerGroup = "${rocketmq.consumer.group}", selectorExpression = MqConstant.BODY_FAT_SCALE_CREATE_SUCCESS_TAG)
public class BodyFatScaleConsumer implements RocketMQListener<MessageExt> {
    private final IScaleReportService scaleReportService;

    @Override
    public void onMessage(MessageExt messageExt) {
        byte[] body = messageExt.getBody();
        log.info("BodyFatScaleConsumer receive message: {}", JSONObject.parseObject(new String(body), BodyFatScaleBO.class));
        try {
            scaleReportService.saveRecords(JSONObject.parseObject(new String(body), BodyFatScaleBO.class));
        } catch (Exception e) {
            log.error("BodyFatScaleConsumer saveRecords error: {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }
}
