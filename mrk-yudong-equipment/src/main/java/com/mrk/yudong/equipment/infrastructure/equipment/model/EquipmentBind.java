package com.mrk.yudong.equipment.infrastructure.equipment.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 设备绑定明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class EquipmentBind extends Model<EquipmentBind> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 蓝牙名称
     */
    private String name;

    /**
     * MAC地址
     */
    private String mac;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 设备分类ID
     */
    private Long categoryId;

    /**
     * 设备信息ID
     */
    private Long equipmentId;

    /**
     * 绑定状态：0-解绑，1-绑定
     */
    private Integer bindStatus;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    public EquipmentBind(String name, String mac, Long userId, Long categoryId, Long equipmentId, Integer bindStatus) {
        this.name = name;
        this.mac = mac;
        this.userId = userId;
        this.categoryId = categoryId;
        this.equipmentId = equipmentId;
        this.bindStatus = bindStatus;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
