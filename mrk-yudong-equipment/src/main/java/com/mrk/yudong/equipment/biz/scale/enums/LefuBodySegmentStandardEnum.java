package com.mrk.yudong.equipment.biz.scale.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum LefuBodySegmentStandardEnum {
    LOW(223222, "偏低", "0"),
    NORMAL(223223, "标准", "1"),
    UPTILTED(223224, "偏高", "2");
    private Integer id;
    private String name;
    private String lefuId;
    public static Integer getIdByLefuId(String lefuId) {
        for (LefuBodySegmentStandardEnum value : LefuBodySegmentStandardEnum.values()) {
            if (value.getLefuId().equals(lefuId)) {
                return value.getId();
            }
        }
        return null;
    }
}
