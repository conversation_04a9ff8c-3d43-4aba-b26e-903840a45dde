package com.mrk.yudong.equipment.api.equipment.controller;


import cn.hutool.core.util.StrUtil;
import com.mrk.yudong.core.annotation.Anonymous;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.equipment.biz.equipment.constant.DictKeyConstant;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipDict;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipDictService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 设备字典表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-09
 */
@RestController
@RequestMapping("/equipDictController")
public class EquipDictController {

    final
    IEquipDictService equipDictService;

    public EquipDictController(IEquipDictService equipDictService) {
        this.equipDictService = equipDictService;
    }

    /**
     * 获取所有的设备前缀
     * @return 获取所有的设备前缀
     */
    @GetMapping("getEquipDictList")
    public R getEquipDictList() {
        return R.ok(dictList(DictKeyConstant.EQUIP_TYPE,null));
    }

    /**
     * 无权限校验 -获取所有的设备前缀
     * @return 获取所有的设备前缀
     */
    @GetMapping("anonymousEquipDictList")
    @Anonymous
    public R getAnonymousEquipDictList() {
        return R.ok(dictList(DictKeyConstant.EQUIP_TYPE,null));
    }

    /**
     * 获取app设备前缀
     * @return 获取app设备前缀
     */
    @GetMapping("getAppEquipPrefix")
    public R getAppEquipPrefix() {
        return R.ok(dictList(DictKeyConstant.APP_EQUIP_TYPE,null));
    }
    /**
     * 获取所有ota类型
     * @return 获取所有ota类型
     */
    @GetMapping("otaTypeList")
    public R otaTypeList() {
        return R.ok(dictList(DictKeyConstant.OTA_TYPE,null));
    }

    /**
     * 获取所有小件设备id
     * @return 获取所有小件设备id
     */
    @Anonymous
    @GetMapping("smallIdList")
    public R smallIdList() {
        return R.ok(dictList(DictKeyConstant.SMALL_ID,null));
    }


    /**
     * 测试类型
     * @return 测试类型
     */
    @GetMapping("testInsList")
    public R testInsList() {
        return R.ok(dictList(DictKeyConstant.TEST_INS,null));
    }

    /**
     * 通信协议
     * @return 通信协议
     */
    @GetMapping("agreementList")
    public R agreementList() {
        return R.ok(dictList(DictKeyConstant.AGREEMENT_TYPE,null));
    }
    /**
     * 特征值
     * @return 特征值
     */
    @GetMapping("eigenValueList")
    public R eigenValueList() {
        return R.ok(dictList(DictKeyConstant.EIGENVALUE,null));
    }
    /**
     * 绑定页强提醒
     * @return
     */
    @GetMapping("bindingWarn")
    public ResDTO<EquipDict> bindingWarn(String equipTypeId) {
        return ResDTO.ok(dictList(DictKeyConstant.BINDING_WARN,equipTypeId).stream().findFirst().orElse(new EquipDict()));
    }
    public List<EquipDict> dictList(String key,String name) {
        BaseQuery<EquipDict> baseQuery = new BaseQuery<>();
        baseQuery.eq("dict_key",key).orderByAsc("sort");
        if(StrUtil.isNotBlank(name)){
            baseQuery.eq("name",name);
        }
        return equipDictService.list(baseQuery);
    }
}
