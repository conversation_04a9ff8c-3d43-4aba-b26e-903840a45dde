package com.mrk.yudong.equipment.biz.scale.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.equipment.biz.scale.bo.StandardsSectionBO;
import com.mrk.yudong.equipment.biz.scale.enums.BodyTypeEnum;
import com.mrk.yudong.equipment.biz.scale.service.*;
import com.mrk.yudong.equipment.infrastructure.scale.mapper.EquHealthStandardsMapper;
import com.mrk.yudong.equipment.infrastructure.scale.model.*;
import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 健康标准信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HealthStandardsServiceImpl extends BaseServiceImpl<EquHealthStandardsMapper, HealthStandards> implements IHealthStandardsService {

    private final IScaleRecordService scaleRecordService;
    private final IScaleUserService scaleUserService;
    private final IEquModelAttributesStandardsRelService modelAttributesStandardsRelService;

    private final IHealthAttributeService healthAttributeService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void washStandardsData(List<RecordUserAssociation> oldData) {
        if (oldData.isEmpty()) {
            log.info("没有需要处理的数据");
            return;
        }
        List<ScaleUser> scaleUsers = scaleUserService.listByIds(oldData.stream().map(RecordUserAssociation::getScaleUserId).collect(Collectors.toList()));
        Map<Long, ScaleUser> scaleUserMap = scaleUsers.stream().collect(Collectors.toMap(ScaleUser::getId, v -> v));
        BaseQuery<ScaleRecord> query = new BaseQuery<>();
        query.in("measure_id", oldData.stream().map(RecordUserAssociation::getId).collect(Collectors.toList()));
        List<ScaleRecord> scaleRecords = scaleRecordService.list(query);
        if (scaleRecords.isEmpty()) {
            log.info("没有需要处理的数据");
            return;
        }
        Map<Long, ScaleUser> scaleUserGroup = new HashMap<>();
        for (RecordUserAssociation oldDatum : oldData) {
            ScaleUser scaleUser = MapUtil.get(scaleUserMap, oldDatum.getScaleUserId(), ScaleUser.class);
            if (scaleUser != null) {
                scaleUserGroup.put(oldDatum.getId(), scaleUser);
            }
        }
        Map<Long, List<ScaleRecord>> scaleGroupMeasureId = scaleRecords.stream().collect(Collectors.groupingBy(ScaleRecord::getMeasureId));
        BigDecimal oneHundred = new BigDecimal(100);
        List<ScaleRecord> updateScaleRecordList = new ArrayList<>();
        setUpdateScaleRecordList(scaleUserGroup, scaleGroupMeasureId, oneHundred, updateScaleRecordList);
        log.info("需要更新的数据: {}", updateScaleRecordList);
        scaleRecordService.updateBatchById(updateScaleRecordList);
    }

    private void setUpdateScaleRecordList(Map<Long, ScaleUser> scaleUserGroup, Map<Long, List<ScaleRecord>> scaleGroupMeasureId, BigDecimal oneHundred, List<ScaleRecord> updateScaleRecordList) {
        LambdaQueryWrapper<HealthStandards> healthStandardsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        healthStandardsLambdaQueryWrapper.le(HealthStandards::getId, 33);
        List<HealthStandards> healthStandards = list(healthStandardsLambdaQueryWrapper);
        Map<Long, ModelAttributesStandardsRel> groupModelAttributesStandardsRelByStandardsId = modelAttributesStandardsRelService.list().stream().collect(Collectors.toMap(ModelAttributesStandardsRel::getStandardsId, v -> v));

        scaleGroupMeasureId.forEach((k, v) -> {
            ScaleUser scaleUser = scaleUserGroup.get(k);
            if (scaleUser == null) {
                log.info("没有找到用户信息,{}", k);
                return;
            }
            DefaultContext<String, Object> context = new DefaultContext<>();
            context.put("sex", scaleUser.getSex());
            context.put("age", DateUtil.ageOfNow(scaleUser.getBirthday()));
            context.put("height", scaleUser.getHeight());

            ScaleRecord musicScaleRecord = new ScaleRecord();
            ScaleRecord skeletalMuscleRatioScaleRecord = new ScaleRecord();
            ScaleRecord bodyFatRateScaleRecord = new ScaleRecord();

            Map<String, Object> result = new HashMap<>();
            ExpressRunner runner = new ExpressRunner();

            ScaleRecord weightRecord = v.stream().filter(record -> record.getHealthAttributesId() == 1).findFirst().orElse(new ScaleRecord());
            BigDecimal weight = new BigDecimal(weightRecord.getHealthAttributesValue());

            for (ScaleRecord scaleRecord : v) {
                if (scaleRecord.getHealthAttributesId() == 6) {
                    log.info("muscle:{}", scaleRecord.getHealthAttributesValue());
                    double muscle = new BigDecimal(scaleRecord.getHealthAttributesValue()).doubleValue();
                    log.info("muscle:{}", muscle);
                    musicScaleRecord.setHealthAttributesValue(String.valueOf(muscle));
                    musicScaleRecord.setId(scaleRecord.getId());
                    context.put("muscle", muscle);
                }
                if (scaleRecord.getHealthAttributesId() == 12) {
                    log.info("skeletalMuscleRatio:{}", scaleRecord.getHealthAttributesValue());
                    double skeletalMuscleRatio = new BigDecimal(scaleRecord.getHealthAttributesValue()).doubleValue();
                    log.info("skeletalMuscleRatio:{}", skeletalMuscleRatio);
                    skeletalMuscleRatioScaleRecord.setHealthAttributesValue(String.valueOf(skeletalMuscleRatio));
                    skeletalMuscleRatioScaleRecord.setId(scaleRecord.getId());
                    context.put("skeletalMuscleRatio", skeletalMuscleRatio);
                }
                if (scaleRecord.getHealthAttributesId() == 8) {
                    log.info("bodyFatRate:{}", scaleRecord.getHealthAttributesValue());
                    bodyFatRateScaleRecord.setHealthAttributesValue(scaleRecord.getHealthAttributesValue());
                    bodyFatRateScaleRecord.setId(scaleRecord.getId());
                    context.put("bodyFatRate", Double.parseDouble(scaleRecord.getHealthAttributesValue()));
                }
            }

            for (HealthStandards healthStandard : healthStandards) {
                if ( healthStandard.getAttributesId() != 8 && healthStandard.getAttributesId() != 12) {
                    continue;
                }
                String dataFormula = healthStandard.getStandardsDataFormula();
                log.info("healthStandard:{}", healthStandard.getDescription());
                if (StrUtil.isNotBlank(dataFormula)) {
                    log.info("数据计算公式：{}", dataFormula);
                    Object execute = null;
                    try {
                        execute = runner.execute(dataFormula, context, null, true, true);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    if (execute != null) {
                        JSONObject data = JSONObject.parseObject((String) execute);
                        Map<String, Object> dataInnerMap = data.getInnerMap();
                        context.putAll(dataInnerMap);
                        //设置标准区间
                        setStandardSection(result, healthStandard, dataInnerMap, groupModelAttributesStandardsRelByStandardsId);
                    }
                }

                if (StrUtil.isNotBlank(healthStandard.getStandardsSectionFormula())) {
                    log.info("区间计算公式：{}", healthStandard.getStandardsSectionFormula());
                    Object standardsSection = null;
                    try {
                        standardsSection = runner.execute(healthStandard.getStandardsSectionFormula(), context, null, true, true);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    if (standardsSection != null) {
                        //命中标准
                        result.put(healthStandard.getAttributesId().toString(), healthStandard.getId());
                    }
                    log.info("计算结果：{}", standardsSection);
                }
            }
            LambdaQueryWrapper<HealthAttribute> healthAttributeLambdaQueryWrapper = new LambdaQueryWrapper<>();
            healthAttributeLambdaQueryWrapper.in(HealthAttribute::getId, List.of(6, 8, 12));
            List<HealthAttribute> healthAttributes = healthAttributeService.list(healthAttributeLambdaQueryWrapper);
            for (HealthAttribute healthAttribute : healthAttributes) {
                String standardStatus = MapUtil.getStr(result, healthAttribute.getId().toString());
                String remark = JSONObject.toJSONString(result.get("section" + healthAttribute.getId()));
                if (healthAttribute.getId() == 6) {
                    musicScaleRecord.setStandardStatus(standardStatus);
                    if (StrUtil.isNotBlank(remark) && !"null".equals(remark)) {
                        musicScaleRecord.setRemark(remark);
                    }
                }
                if (healthAttribute.getId() == 12) {
                    skeletalMuscleRatioScaleRecord.setStandardStatus(standardStatus);
                    if (StrUtil.isNotBlank(remark) && !"null".equals(remark)) {
                        skeletalMuscleRatioScaleRecord.setRemark(remark);
                    }
                }
                if (healthAttribute.getId() == 8) {
                    bodyFatRateScaleRecord.setStandardStatus(standardStatus);
                    if (StrUtil.isNotBlank(remark) && !"null".equals(remark)) {
                        bodyFatRateScaleRecord.setRemark(remark);
                    }
                }
            }
            HealthStandards musicStandards = healthStandards.stream().filter(music -> music.getId().toString().equals(musicScaleRecord.getStandardStatus())).findFirst().orElse(null);
            HealthStandards bodyFatRateStandards = healthStandards.stream().filter(music -> music.getId().toString().equals(bodyFatRateScaleRecord.getStandardStatus())).findFirst().orElse(null);
            if (null != musicStandards && null != bodyFatRateStandards && null != scaleUser.getSex()) {
                log.info("scaleUser.getSex(), musicStandards.getName(), bodyFatRateStandards.getName()", scaleUser.getSex(), musicStandards.getName(), bodyFatRateStandards.getName());
                BodyTypeEnum bodyType = BodyTypeEnum.calculateBodyType(scaleUser.getSex(), musicStandards.getName(), bodyFatRateStandards.getName());
                if (null != bodyType) {
                    ScaleRecord bodyTypeScaleRecord = v.stream().filter(record -> record.getHealthAttributesId() == 5).findFirst().orElse(null);  //体型
                    if (null != bodyTypeScaleRecord) {
                        bodyTypeScaleRecord.setStandardStatus(bodyType.getCode().toString());
//                        updateScaleRecordList.add(bodyTypeScaleRecord);
                    }

                    log.info("bodyTypeScaleRecord: " + bodyTypeScaleRecord);
                }
            }
            log.info("musicScaleRecord: " + musicScaleRecord);
            log.info("skeletalMuscleRatioScaleRecord: " + skeletalMuscleRatioScaleRecord);
            log.info("bodyFatRateScaleRecord: " + bodyFatRateScaleRecord);
//            updateScaleRecordList.add(musicScaleRecord);
            updateScaleRecordList.add(skeletalMuscleRatioScaleRecord);
            updateScaleRecordList.add(bodyFatRateScaleRecord);
        });


    }

    private void setStandardSection(Map<String, Object> standardInterval, HealthStandards healthStandard, Map<String, Object> dataInnerMap, Map<Long, ModelAttributesStandardsRel> groupModelAttributesStandardsRelBYStandardsId) {
        StandardsSectionBO standard = new StandardsSectionBO();
        standard.setName(healthStandard.getName());
        standard.setCode(healthStandard.getId());
        String min = "";
        if (dataInnerMap.containsKey("min")) {
            min = dataInnerMap.get("min").toString();
        } else if (dataInnerMap.containsKey("min".concat(healthStandard.getId().toString()))) {
            min = dataInnerMap.get("min".concat(healthStandard.getId().toString())).toString();
        }
        standard.setMinData(min);
        String max = "";
        if (dataInnerMap.containsKey("max")) {
            max = dataInnerMap.get("max").toString();
        } else if (dataInnerMap.containsKey("max".concat(healthStandard.getId().toString()))) {
            max = dataInnerMap.get("max".concat(healthStandard.getId().toString())).toString();
        }
        standard.setMaxData(max);
        ModelAttributesStandardsRel modelAttributesStandardsRel = MapUtil.get(groupModelAttributesStandardsRelBYStandardsId, healthStandard.getId(), ModelAttributesStandardsRel.class);
        if (modelAttributesStandardsRel != null) {
            standard.setSeq(modelAttributesStandardsRel.getSeq());
        }
        String sectionKey = "section".concat(healthStandard.getAttributesId().toString());
        JSONArray standardArray = new JSONArray();
        if (standardInterval.containsKey(sectionKey)) {
            //区间
            List<StandardsSectionBO> sectionBOS = JSONArray.parseArray(standardInterval.get(sectionKey).toString(), StandardsSectionBO.class);
            sectionBOS.add(standard);
            List<StandardsSectionBO> standardsSectionBOS = sectionBOS.stream().sorted(Comparator.comparing(StandardsSectionBO::getSeq)).collect(Collectors.toList());
            JSONArray jsonArray = new JSONArray();
            jsonArray.addAll(standardsSectionBOS);
            standardInterval.put(sectionKey, jsonArray);
        } else {
            standardArray.add(JSONObject.toJSON(standard));
            standardInterval.put(sectionKey, standardArray);
        }
    }

}
