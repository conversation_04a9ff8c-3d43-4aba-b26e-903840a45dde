package com.mrk.yudong.equipment.infrastructure.scale.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 测量信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("equ_scale_record")
public class ScaleRecord extends Model<ScaleRecord> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 测量id
     */
    private Long measureId;

    /**
     * 健康属性id
     */
    private Integer healthAttributesId;

    /**
     * 属性内容
     */
    private String healthAttributesValue;

    /**
     * 趋势：0无状态，1上升，2下降,
     */
    private Integer  trend;

    /**
     * 趋势值
     */
    private Double  trendValue;
    /**
     * 标准状态
     */
    private String standardStatus;

    /**
     * json
     */
    private String remark;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    private Integer isDelete;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
