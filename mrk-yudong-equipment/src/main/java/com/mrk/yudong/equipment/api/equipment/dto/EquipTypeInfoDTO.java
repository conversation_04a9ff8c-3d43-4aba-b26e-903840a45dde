package com.mrk.yudong.equipment.api.equipment.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Data
public class EquipTypeInfoDTO {

    /**
     * 型号id
     */
    private Long modelId;

    /**
     * 型号id
     */
    private Long id;

    /**
     * 设备用户关联ID
     */
    private Long equipmentInfoId;

    /**
     * 蓝牙广播前缀id
     */
    private Integer prefixId;

    /**
     * 设备型号所属编号
     */
    private String code;

    /**
     * 展示图片
     */
    @NotBlank(message = "展示图片不能为空")
    private String typeImages;

    /**
     * icon图片
     */
    private String iconImages;

    /**
     * 大icon图片
     */
    private String bigIconImages;

    /**
     * 设备角标图片
     */
    private String tagIcon;

    /**
     * 今日直播icon
     */
    private String liveIcon;
    /**
     * 设备型号名称
     */
    @Length(max = 100, message = "设备型号长度不能超过100")
    private String typeName;

    /**
     * 固件型号id
     */
    private Long typeVersionId;

    /**
     * 最高电磁档位
     */
    private Integer maxElectromagneticControl;

    /**
     * 通信类型，1:彩屏，2：蓝牙电子表，3：wifi电子表，4:无，5：物联网双模
     */
    private Integer productType;

    /**
     * 产品说明（http）
     */
    private String productManual;

    /**
     * 帮助中心（http）
     */
    private String helpCenter;

    /**
     * 目录等级，0：顶级，1：二级
     */
    private Integer level;

    /**
     * 上级id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 排序
     */
    private Integer sort;
    /**
     * 是否显示阻力，1是0否
     */
    private Integer showResistance;

    /**
     * 最小阻力
     */
    private Integer minResistance;

    /**
     * 最大阻力
     */
    private Integer maxResistance;

    /**
     * 是否显示速度，1是0否
     */
    private Integer showSpeed;

    /**
     * 最小速度
     */
    private Integer minSpeed;

    /**
     * 最大速度
     */
    private Integer maxSpeed;

    /**
     * 是否显示坡度，1是0否
     */
    private Integer showSlope;

    /**
     * 最小坡度
     */
    private Integer minSlope;

    /**
     * 最大坡度
     */
    private Integer maxSlope;

    /**
     * 是否可调节档位，1是0否
     */
    private Integer showGear;

    /**
     * 最小档位
     */
    private Integer minGear;

    /**
     * 最大档位
     */
    private Integer maxGear;
    /**
     * 是否支持ota：1是0否
     */
    private Integer isOta;

    /**
     * 是否清理运动数据：1是0否
     */
    private Integer isClean;

    /**
     * 1运动2小件3健康设备
     */
    private Integer type;

    /**
     * 预发布环境
     */
    private Integer isTra;
    /**
     * 是否支持app连接 1是0否
     */
    private Integer isSupportConnection;

    /**
     * 特征值
     */
    private Integer eigenValue;

    /**
     * 暗色图标
     */
    private String darkIcon;

    /**
     * 亮色图标
     */
    private String brightIcon;


    /**
     * 绑定图片
     */
    private String bindingImage;

    /**
     * 未绑定图片
     */
    private String unboundImage;

    /**
     * 是否单独显示1是0否
     */
    private Integer isShow;

    /**
     * 通信协议：1:麦瑞克,2:FTMS,3:智健,4:柏群,5:FTMS+智健
     */
    private Integer communicationProtocol;
    /**
     * 运动模式
     */
    private Integer motorPattern;
    /**
     * 视频显示
     */
    private Integer videoShow;
    /**
     * 训练视频显示
     */
    private Integer trainingReportShow;

    /**
     * 所属类型数量/是否有绑定的设备
     */
    @TableField(exist = false)
    private Integer typeNum;

    /**
     * ota名称
     */
    @TableField(exist = false)
    private String agreement;


    /**
     * 蓝牙广播前缀名字
     */
    @TableField(exist = false)
    private String prefixName;
    /**
     * 固件型号名称
     */
    @TableField(exist = false)
    private String typeVersion;
    /**
     * 是否是电磁控设备 1是0否
     */
    private Integer isElectromagneticControl;

    /**
     * 是否需要拆分小件 1是0否
     */
    @TableField(exist = false)
    private Integer isSplit;
    /**
     * 绑定时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime bindTime;
    /**
     * 基础类型
     */
    private Integer topType;
    /**
     * 蓝牙别名
     */
    @TableField(exist = false)
    private String bluetoothAlias;

    /**
     * 备注
     */
    private String remark;
    /**
     * 安装教程
     */
    private String installTutorial;
    /**
     * 是否支持阻力回显
     */
    private Integer isSupportResistanceEcho;

}
