package com.mrk.yudong.equipment.api.scale.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.core.annotation.Anonymous;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.equipment.api.scale.dto.BodyFatScaleFrom;
import com.mrk.yudong.equipment.api.scale.dto.HistoricalWeightFrom;
import com.mrk.yudong.equipment.api.scale.dto.LastScaleFrom;
import com.mrk.yudong.equipment.api.scale.dto.resp.ScaleHealthDTO;
import com.mrk.yudong.equipment.api.scale.dto.resp.TargetWeightGapDTO;
import com.mrk.yudong.equipment.api.scale.vo.*;
import com.mrk.yudong.equipment.biz.scale.constant.HealthConstant;
import com.mrk.yudong.equipment.biz.scale.service.IRecordUserAssociationService;
import com.mrk.yudong.equipment.biz.scale.service.IScaleRecordService;
import com.mrk.yudong.equipment.biz.scale.service.IScaleReportService;
import com.mrk.yudong.equipment.infrastructure.scale.model.RecordUserAssociation;
import com.mrk.yudong.equipment.infrastructure.scale.model.ScaleRecord;
import com.mrk.yudong.share.constant.ResponseConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 体脂秤用户与记录关联 前端控制器
 *
 * <AUTHOR>
 * @since 2022-06-17
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/record-user-association")
public class RecordUserAssociationController {

    private final IRecordUserAssociationService recordUserAssociationService;

    private final IScaleRecordService scaleRecordService;

    private final IScaleReportService scaleReportService;

    /**
     * 上报体脂秤数据
     *
     * @param bodyFatScale 用户信息
     * @return com.mrk.yudong.core.model.ResDTO
     * <AUTHOR>
     * @date 18:36 2022/6/14
     **/
    @PostMapping("bodyFatScale")
    @Deprecated
    public ResDTO<String> saveBodyFatScale(@RequestBody BodyFatScaleFrom bodyFatScale) {
        return reportBodyFatScale(bodyFatScale);
    }

    /**
     * 上报体脂秤数据
     *
     * @param bodyFatScale 用户信息
     * @return com.mrk.yudong.core.model.ResDTO
     * <AUTHOR>
     * @date 18:36 2022/6/14
     **/

    @PostMapping("bodyFatScale/v2")
    public ResDTO<String> reportBodyFatScale(@RequestBody BodyFatScaleFrom bodyFatScale){
        String reportBodyFatScale = scaleReportService.reportBodyFatScale(bodyFatScale);
        if (StrUtil.isNotEmpty(reportBodyFatScale)){
            return ResDTO.ok(reportBodyFatScale);
        }
        return ResDTO.fail();
    }

    /**
     * 体脂秤体重相差弹窗控制
     *
     * @param lastScaleDate 校验参数
     * @return com.mrk.yudong.core.model.ResDTO<cn.hutool.core.lang.Dict>
     * <AUTHOR>
     * @date 14:58 2022/6/21
     **/
    @PostMapping("lastScaleDate")
    public ResDTO<Dict> lastScaleDate(@RequestBody @Valid LastScaleFrom lastScaleDate) {
        return ResDTO.ok(recordUserAssociationService.lastScaleDate(lastScaleDate));
    }

    /**
     * 删除体脂秤数据
     *
     * @param id id
     * @return 删除信息
     */
    @DeleteMapping("bodyFatScale")
    public ResDTO<String> delScaleDate(Long id) {
        if (id == null) {
            return ResDTO.fail(ResponseConstant.PARAM_ERROR);
        }
        boolean delStatus = recordUserAssociationService.removeById(id);
        if (delStatus) {
            return ResDTO.ok();
        } else {
            return ResDTO.fail(ResponseConstant.DEL_FAIL);
        }
    }


    /**
     * 体脂秤历史数据
     *
     * @param historicalWeightFrom 查询条件
     * @return com.mrk.yudong.core.model.ResDTO
     * <AUTHOR>
     * @date 15:16 2022/7/5
     **/
    @GetMapping("historicalWeight")
    public ResDTO<IPage<HistoricalWeightVO>> historicalWeight(@Valid HistoricalWeightFrom historicalWeightFrom) {
        Long userId = SessionUtil.getId();
        if (userId == null) {
            return ResDTO.fail(ResponseConstant.COMMON_NOUSER);
        }

        IPage<HistoricalWeightVO> result = new Page<>(historicalWeightFrom.getCurrent(), historicalWeightFrom.getSize());
        if (historicalWeightFrom.getScaleUserId() == null) {
            return ResDTO.ok(result);
        }
        IPage<RecordUserAssociation> page = new Page<>(historicalWeightFrom.getCurrent(), historicalWeightFrom.getSize());
        BaseQuery<RecordUserAssociation> basequery = new BaseQuery<>();
        basequery.eq("scale_user_id", historicalWeightFrom.getScaleUserId()).orderByDesc("create_time");
        page = recordUserAssociationService.page(page, basequery);
        List<RecordUserAssociation> recordUserAssociations = page.getRecords();
        if (CollUtil.isNotEmpty(recordUserAssociations)) {
            List<HistoricalWeightVO> historicalWeightList = new ArrayList<>();
            List<Long> measureId = recordUserAssociations.stream().map(RecordUserAssociation::getId).collect(Collectors.toList());
            List<ScaleRecord> scaleRecords = scaleRecordService.list("measure_id", ConditionEnum.IN, measureId);
            if (scaleRecords != null) {
                Map<Long, List<ScaleRecord>> measureMap = scaleRecords.stream().collect(Collectors.groupingBy(ScaleRecord::getMeasureId));
                recordUserAssociations.forEach(v -> {
                    HistoricalWeightVO historicalWeight = new HistoricalWeightVO();
                    historicalWeight.setId(v.getId());
                    historicalWeight.setMeasureId(v.getId());
                    historicalWeight.setInsertType(v.getInsertType());
                    historicalWeight.setDateTime(LocalDateTimeUtil.toEpochMilli(v.getCreateTime()));
                    List<ScaleRecord> scaleRecordList = measureMap.get(v.getId());
                    if (CollUtil.isNotEmpty(scaleRecordList)) {
                        scaleRecordList.stream().filter(healthAttributes -> healthAttributes.getHealthAttributesId().equals(HealthConstant.WEIGHT)).findFirst().ifPresent(weight -> historicalWeight.setWeight(weight.getHealthAttributesValue()));
                        scaleRecordList.stream().filter(healthAttributes -> healthAttributes.getHealthAttributesId().equals(HealthConstant.BMI)).findFirst().ifPresent(bmi -> historicalWeight.setBmi(bmi.getHealthAttributesValue()));
                        scaleRecordList.stream().filter(healthAttributes -> healthAttributes.getHealthAttributesId().equals(HealthConstant.BODYFATRATE)).findFirst().ifPresent(bodyfatrate -> historicalWeight.setBodyFatRate(bodyfatrate.getHealthAttributesValue()));
                    }
                    historicalWeight.setElectrodeType(v.getElectrodeType());
                    historicalWeightList.add(historicalWeight);
                });
                result.setRecords(historicalWeightList);
            }
            result.setTotal(page.getTotal());
        }
        return ResDTO.ok(result);
    }

    /**
     * 体脂秤体图表趋势
     *
     * @param scalesTrendsVO 查询条件
     * @return com.mrk.yudong.core.model.ResDTO<cn.hutool.core.lang.Dict>
     * <AUTHOR>
     * @date 11:07 2022/6/15
     **/
    @PostMapping("chartTrend")
    public ResDTO<Dict> chartTrend(@RequestBody @Valid ScalesTrendsVO scalesTrendsVO) {
        return ResDTO.ok(recordUserAssociationService.chartTrend(scalesTrendsVO));
    }

    /**
     * 体脂秤用户基础数据
     *
     * @param scaleUserId 体脂秤用户id
     * @return com.mrk.yudong.core.model.ResDTO<com.mrk.yudong.equipment.model.BodyFatScale>
     * <AUTHOR>
     * @date 16:37 2022/6/15
     **/
    @GetMapping("bodyFatScaleInfo")
    public ResDTO<BodyFatScaleInfoVO> bodyFatScaleInfo(@RequestParam("scaleUserId") Long scaleUserId) {
        if (scaleUserId == null) {
            return ResDTO.ok();
        }
        return ResDTO.ok(recordUserAssociationService.bodyFatScaleInfo(scaleUserId));
    }

    @GetMapping("listScaleHealth")
    @Anonymous
    public ResDTO<List<ScaleHealthDTO>> listScaleHealth(@RequestParam("userId") Long userId) {
        if (userId == null) {
            return ResDTO.fail(ResponseConstant.COMMON_NOUSER);
        }
        return ResDTO.ok(recordUserAssociationService.listScaleHealth(userId));
    }
    /**
     * 八电极健康报告
     *
     * @param bodyFatScaleId 称量id
     * @return com.mrk.yudong.core.model.ResDTO<com.mrk.yudong.equipment.model.BodyFatScale>
     * <AUTHOR>
     * @date 16:37 2022/6/15
     **/
    @GetMapping("healthReport")
    public ResDTO<HealthReportVO> healthReport(@RequestParam("bodyFatScaleId") Long bodyFatScaleId) {
        if (bodyFatScaleId == null) {
            return ResDTO.fail(ResponseConstant.PARAM_ERROR);
        }
        return ResDTO.ok(recordUserAssociationService.healthReport(bodyFatScaleId));
    }

    /**
     * 健康报告
     *
     * @param bodyFatScaleId 称量id
     * @return com.mrk.yudong.core.model.ResDTO<com.mrk.yudong.equipment.model.BodyFatScale>
     * <AUTHOR>
     * @date 16:37 2022/6/15
     **/
    @GetMapping("eightElectrodeHealthReport")
    public ResDTO<HealthReportVO> eightElectrodeHealthReport(@RequestParam("bodyFatScaleId") Long bodyFatScaleId) {
        if (bodyFatScaleId == null) {
            return ResDTO.fail(ResponseConstant.PARAM_ERROR);
        }
        return ResDTO.ok(recordUserAssociationService.healthReport(bodyFatScaleId));
    }

    /**
     * 查询总结建议文案
     *
     * @param scaleUserId 体脂秤用户id
     * @return com.mrk.yudong.core.model.ResDTO<java.lang.String>
     * <AUTHOR>
     * @date 15:33 2022/7/4
     **/
    @GetMapping("summarizeCopywriting")
    public ResDTO<String> summarizeCopywriting(Long scaleUserId) {
        if (scaleUserId == null) {
            return ResDTO.ok("");
        }
        return ResDTO.ok(recordUserAssociationService.summarizeCopywriting(scaleUserId));
    }

    /**
     * 查询推荐计划
     *
     * @return com.mrk.yudong.core.model.ResDTO<com.mrk.yudong.equipment.model.BodyFatScale>
     * <AUTHOR>
     * @date 16:37 2022/6/15
     **/
    @GetMapping("recommendedPlan")
    public ResDTO<List<RecommendedPlanVO>> recommendedPlan(Long scaleUserId) {
        if (scaleUserId == null) {
            return ResDTO.fail(ResponseConstant.PARAM_ERROR);
        }
        Long userId = SessionUtil.getId();
        if (userId == null) {
            return ResDTO.fail(ResponseConstant.COMMON_NOUSER);
        }

        return ResDTO.ok(recordUserAssociationService.recommendedPlan(scaleUserId, userId));
    }

    /**
     * 目标体重差距提示
     * @return
     */
    @GetMapping("targetWeight")
    public ResDTO<TargetWeightGapDTO> targetWeightGap(){
        return ResDTO.ok(recordUserAssociationService.targetWeightGap());
    }
}
