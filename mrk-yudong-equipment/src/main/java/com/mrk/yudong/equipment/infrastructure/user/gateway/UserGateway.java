package com.mrk.yudong.equipment.infrastructure.user.gateway;

import com.merach.sun.user.dto.cmd.account.AccountUpdateCmd;
import com.merach.sun.user.dto.user.LatestHealthRecordDTO;
import com.merach.sun.user.dto.user.UserInfoDTO;

public interface UserGateway {

    UserInfoDTO getUserInfoByUserId(Long userId);

    Boolean updateAccount(AccountUpdateCmd accountUpdateCmd);


    LatestHealthRecordDTO getLatestData(Long userId);


}
