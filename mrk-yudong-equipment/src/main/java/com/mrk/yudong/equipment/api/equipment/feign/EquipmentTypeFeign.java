package com.mrk.yudong.equipment.api.equipment.feign;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.mrk.yudong.core.annotation.Anonymous;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipDictService;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentInfoService;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentTypeService;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.BaseTipConstant;
import com.mrk.yudong.share.constant.ResponseConstant;
import com.mrk.yudong.share.constant.equipment.EquipmentMetaConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
@RestController
@RequestMapping("/equipment/equipmentTypeController")
@RequiredArgsConstructor
@Slf4j
public class EquipmentTypeFeign {

    private final IEquipmentTypeService equipmentTypeService;

    private final IEquipmentInfoService equipmentInfoService;

    private final IEquipDictService equipDictService;

    /**
     * 设备选择项
     *
     * @return 设备选择项
     */
    @Anonymous
    @GetMapping("/option")
    public R option() {
        List<Dict> list = equipDictService.option(EquipmentMetaConstant.LATTER_OPTION);
        return R.ok(list);
    }

    /**
     * 验证绑定设备数量（无接口调用）
     *
     * @param userId      用户id
     * @param equipmentId 设备分类id
     * @param modelId     设备型号id
     * @return 用户设备数量
     */
    @Anonymous
    @GetMapping("/check/bind")
    @Deprecated
    public R bind(Long userId, Long equipmentId, String modelId) {
        if (userId == null || equipmentId == null || StrUtil.isBlank(modelId)) {
            return R.paramFail(BaseTipConstant.PARAM_FAIL);
        }
        BaseQuery<EquipmentInfo> baseQuery = new BaseQuery<>();
        baseQuery.eq("user_by",
                userId).eq("one_level_type_id",
                equipmentId).eq("bind_status",
                BaseConstant.INT_TRUE);
        baseQuery.in("two_level_type_id",
                StrUtil.split(modelId,
                        ',',
                        true,
                        true).stream().map(Long::parseLong).collect(Collectors.toList()));
        return R.ok(equipmentInfoService.count(baseQuery));
    }

    /**
     * 获取活动支持的设备型号
     *
     * @param equipmentId 设备分了id
     * @param modelIds    设备型号id
     * @return 活动支持的设备型号
     */
    @Anonymous
    @PostMapping("/activity/equip")
    public R equips(@RequestParam Long equipmentId, @RequestBody List<Long> modelIds) {
        if (equipmentId == null || CollUtil.isEmpty(modelIds)) {
            return R.paramFail(BaseTipConstant.PARAM_FAIL);
        }
        BaseQuery<EquipmentType> baseQuery = new BaseQuery<>();
        baseQuery.eq("parent_id",
                equipmentId).select("id",
                "type_name");
        if (!modelIds.contains(100L)) {
            baseQuery.in("id",
                    modelIds);
        }
        List<EquipmentType> list = equipmentTypeService.list(baseQuery);
        if (CollUtil.isEmpty(list)) {
            return R.ok();
        }
        return R.ok(list.stream().map(equipmentType -> {
            Dict dict = new Dict(2);
            dict.put("id",
                    equipmentType.getId());
            dict.put("name",
                    equipmentType.getTypeName());
            return dict;
        }).collect(Collectors.toList()));
    }

    /**
     * 后台-删除设备类型信息
     *
     * @param ids 信息ids
     * @return R
     */
    @DeleteMapping("/equipmentType")
    @Deprecated
    public R delEquipment(@RequestBody List<Long> ids) {
        if (ids == null) {
            return R.fail(ResponseConstant.DEL_FAIL);
        }
        boolean delStatus = equipmentTypeService.removeByIds(ids);
        if (delStatus) {
            return R.ok();
        }
        return R.fail(ResponseConstant.DEL_FAIL);
    }
}
