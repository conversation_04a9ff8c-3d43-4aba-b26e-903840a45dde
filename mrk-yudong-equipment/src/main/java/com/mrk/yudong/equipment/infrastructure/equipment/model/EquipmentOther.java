package com.mrk.yudong.equipment.infrastructure.equipment.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 其他设备信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@JsonIgnoreProperties(value = {"categoryId", "status", "createId", "createTime", "updateId", "updateTime"})
@Data
@EqualsAndHashCode(callSuper = false)
public class EquipmentOther extends Model<EquipmentOther> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 设备分类ID
     */
    private Long categoryId;

    /**
     * 显示名称
     */
    private String name;

    /**
     * 显示封面
     */
    private String cover;

    /**
     * 型号编码
     */
    private String modelCode;

    /**
     * 蓝牙传播名称
     */
    @TableField(exist = false)
    private String bluetoothName;

    /**
     * 蓝牙传播编码
     */
    private String bluetoothCode;

    /**
     * 是否支持连接APP：0-否，1-是
     */
    private Integer isLinkApp;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
