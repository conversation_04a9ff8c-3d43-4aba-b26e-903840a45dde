package com.mrk.yudong.equipment.infrastructure.producttest.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.equipment.infrastructure.producttest.model.TestEquFirmwareVersion;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 固件版本管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
public interface TestEquFirmwareVersionMapper extends BaseMapper<TestEquFirmwareVersion> {
    /**
     * 分页
     * @param page
     * @param equipTypeId
     * @param agreement
     * @return
     */
    Page<TestEquFirmwareVersion> getPage(IPage<TestEquFirmwareVersion> page, @Param("equipTypeId") Long equipTypeId, @Param("agreement") Integer agreement) ;

    /**
     * 获取所有历史版本
     * @param modelId 设备型号id
     * @param code 设备编号
     * @return
     */
    TestEquFirmwareVersion getNewFirmwareVersion(@Param("modelId")String modelId,@Param("code")String code);
}
