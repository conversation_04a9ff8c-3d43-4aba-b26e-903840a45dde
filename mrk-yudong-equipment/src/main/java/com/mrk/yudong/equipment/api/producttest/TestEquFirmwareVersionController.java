package com.mrk.yudong.equipment.api.producttest;

import com.mrk.yudong.core.annotation.Anonymous;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.equipment.biz.producttest.service.ITestEquFirmwareVersionService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * 固件版本管理
 *
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@RestController
@RequestMapping("/equipment/test-equ-firmware-version")
@RequiredArgsConstructor
public class TestEquFirmwareVersionController {

    private final ITestEquFirmwareVersionService firmwareVersionService;

    /**
     * 获取单条固件版本信息
     *
     * @param modelId 型号id
     * @param code  固件编号
     * @return 详细信息
     */
    @Anonymous
    @GetMapping("appFirmwareVersion")
    public R getTestEquFirmwareVersion(String modelId,String code){
        //当前最大的版本号
        return R.ok(firmwareVersionService.getNewFirmwareVersion(modelId,code));
    }
}
