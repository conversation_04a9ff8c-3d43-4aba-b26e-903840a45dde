package com.mrk.yudong.equipment.infrastructure.scale.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 健康标准信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Data
@TableName("equ_health_standards")
@EqualsAndHashCode(callSuper = false)
public class HealthStandards extends Model<HealthStandards> {

    private static final long serialVersionUID = 1L;

    /**
     * 标准id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 展示名称
     */
    private String name;

    /**
     * 属性id
     */
    private Integer attributesId;

    /**
     * 标准描述
     */
    private String description;

    /**
     * 区间颜色
     */
    private String rangeColor;

    /**
     * 背景颜色
     */
    private String backgroundColor;

    /**
     * 字体颜色
     */
    private String fontColor;

    /**
     * 前置条件
     */
    private String precondition;

    /**
     * 标准文案

     */
    private String standardsCopywriting;

    /**
     * 标准数据计算公式
     */
    private String standardsSectionFormula;

    /**
     * 标准区间计算公式
     */
    private String standardsDataFormula;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Integer updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    private Integer isDelete;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
