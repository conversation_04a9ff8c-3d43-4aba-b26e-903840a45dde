package com.mrk.yudong.equipment.task;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mrk.yudong.equipment.biz.scale.service.IHealthStandardsService;
import com.mrk.yudong.equipment.biz.scale.service.IRecordUserAssociationService;
import com.mrk.yudong.equipment.infrastructure.scale.model.RecordUserAssociation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: ljx
 * @create: 2023/8/9 14:36
 * @Version 1.0
 **/
@Slf4j
@RequiredArgsConstructor
@Component
public class ScaleTask extends JavaProcessor {


    private final IHealthStandardsService healthStandardsService;
    private final IRecordUserAssociationService recordUserAssociationService;
    @Override
    public ProcessResult process(JobContext context) {
        try {
            LambdaQueryWrapper<RecordUserAssociation> associationBaseQuery = new LambdaQueryWrapper<>();

//            associationBaseQuery.eq(RecordUserAssociation::getId, 1692390169102327808L);
            associationBaseQuery.eq(RecordUserAssociation::getMeasureId, 0L).eq(RecordUserAssociation::getInsertType,1);
//                    .ge(RecordUserAssociation::getCreateTime, "2023-08-17 00:00:00").eq(RecordUserAssociation::getInsertType,1);
            healthStandardsService.washStandardsData(recordUserAssociationService.list(associationBaseQuery));
            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("体脂秤数据清洗失败 error: ", e);
            return new ProcessResult(false);
        }
    }
}
