package com.mrk.yudong.equipment.biz.equipment.bo;

import lombok.Data;

@Data
public class ProductModelFeatureBO {
    /**
     * 是否支持ota：1是0否
     */
    private Integer isOta;

    /**
     * 是否清理运动数据：1是0否
     */
    private Integer isClean;
    
    /**
     * 是否是电磁控设备 1是0否
     */
    private Integer isElectromagneticControl;

    /**
     * 版本特征值
     */
    private Integer versionEigenValue;

    /**
     * 是否控制阻力，1是0否
     */
    private Integer controlResistance;

    /**
     * 最小阻力
     */
    private Integer minResistance;

    /**
     * 最大阻力
     */
    private Integer maxResistance;

    /**
     * 是否控制速度，1是0否
     */
    private Integer controlSpeed;

    /**
     * 最小速度
     */
    private Integer minSpeed;

    /**
     * 最大速度
     */
    private Integer maxSpeed;

    /**
     * 是否控制坡度，1是0否
     */
    private Integer controlSlope;

    /**
     * 最小坡度
     */
    private Integer minSlope;

    /**
     * 最大坡度
     */
    private Integer maxSlope;

    /**
     * 是否可调节档位，1是0否
     */
    private Integer controlGear;

    /**
     * 最小档位
     */
    private Integer minGear;

    /**
     * 最大档位
     */
    private Integer maxGear;

    /**
     * 是否支持阻力回显
     */
    private Integer isSupportResistanceEcho;
}
