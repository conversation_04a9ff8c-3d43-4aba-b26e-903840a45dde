package com.mrk.yudong.equipment.feign;

import com.mrk.yudong.share.dto.course.CoursePlanDTO;
import com.mrk.yudong.core.model.ResDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2021−03-31 3:00 下午
 */
@FeignClient(name = "yudong-course")
public interface CourseFeign {

    /**
     * 体脂秤推荐计划
     *
     * @param planIdList 训练计划id集合
     * @return com.mrk.yudong.core.model.ResDTO<java.util.List < com.mrk.yudong.share.dto.course.CoursePlanDTO>>
     * <AUTHOR>
     * @date 10:46 2022/7/5
     **/
    @PostMapping("/courseTrainingPlanController/coursePlanList")
    ResDTO<List<CoursePlanDTO>> coursePlanList(@RequestBody List<Long> planIdList);
}
