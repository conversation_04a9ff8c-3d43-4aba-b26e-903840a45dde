package com.mrk.yudong.equipment.api.heart.controller;


import cn.hutool.core.bean.BeanUtil;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.equipment.api.heart.dto.HeartRateWarningDTO;
import com.mrk.yudong.equipment.api.heart.dto.HeartRateWarningFrom;
import com.mrk.yudong.equipment.biz.heart.service.IHeartRateWarningService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * todo 心率预警 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-03
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/equipment/heart-rate-warning")
public class HeartRateWarningController {

    private final IHeartRateWarningService heartRateWarningService;

    /**
     * 查询心率预警信息
     *
     * @return com.mrk.yudong.core.model.ResDTO<com.mrk.yudong.equipment.infrastructure.heart.model.HeartRateWarning>
     * <AUTHOR>
     * @date 11:28 2023/1/3
     **/
    @GetMapping("getHeartRateWarning")
    public ResDTO<HeartRateWarningDTO> getHeartRateWarning() {
        return ResDTO.ok(BeanUtil.copyProperties(heartRateWarningService.getHeartRateWarning(), HeartRateWarningDTO.class));
    }

    /**
     * 开启、关闭,调整心率预警信息
     *
     * @return com.mrk.yudong.core.model.ResDTO<java.lang.Boolean>
     * <AUTHOR>
     * @date 11:29 2023/1/3
     **/
    @PostMapping("adjustHeartRateWarning")
    public ResDTO<Integer> adjustHeartRateWarning(@RequestBody @Valid HeartRateWarningFrom heartRateWarningFrom) {
        return ResDTO.ok(heartRateWarningService.adjustHeartRateWarning(heartRateWarningFrom));
    }

    /**
     * 心率预警弹窗
     *
     * @return com.mrk.yudong.core.model.ResDTO<com.mrk.yudong.equipment.infrastructure.heart.model.HeartRateWarning>
     * <AUTHOR>
     * @date 15:09 2023/1/3
     **/
    @PutMapping("heartRateWarningPopup")
    public ResDTO<Boolean> heartRateWarningPopup() {
        return ResDTO.ok(heartRateWarningService.heartRateWarningPopup());
    }
}
