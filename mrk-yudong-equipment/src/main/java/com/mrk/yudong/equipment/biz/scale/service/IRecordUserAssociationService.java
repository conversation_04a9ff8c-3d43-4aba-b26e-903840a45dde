package com.mrk.yudong.equipment.biz.scale.service;

import cn.hutool.core.lang.Dict;
import com.mrk.yudong.core.service.BaseService;
import com.mrk.yudong.equipment.api.scale.dto.LastScaleFrom;
import com.mrk.yudong.equipment.api.scale.dto.resp.ScaleHealthDTO;
import com.mrk.yudong.equipment.api.scale.dto.resp.TargetWeightGapDTO;
import com.mrk.yudong.equipment.infrastructure.scale.model.RecordUserAssociation;
import com.mrk.yudong.equipment.api.scale.vo.BodyFatScaleInfoVO;
import com.mrk.yudong.equipment.api.scale.vo.HealthReportVO;
import com.mrk.yudong.equipment.api.scale.vo.RecommendedPlanVO;
import com.mrk.yudong.equipment.api.scale.vo.ScalesTrendsVO;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 * 体脂秤用户与记录关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-17
 */
public interface IRecordUserAssociationService extends BaseService<RecordUserAssociation> {

    /**
     * 体脂秤体重相差弹窗控制
     * @param lastScaleDate 体重信息
     * <AUTHOR>
     * @date 15:19 2022/6/21
     * @return null
     **/
    Dict lastScaleDate(LastScaleFrom lastScaleDate);

    /**
     * 图表趋势
     * @param scalesTrendsVO 体脂秤变化趋势条件
     * <AUTHOR>
     * @date 20:14 2022/6/21
     * @return cn.hutool.core.lang.Dict
     **/
    Dict chartTrend(ScalesTrendsVO scalesTrendsVO);


    /** 体脂秤用户基础数据
     * @param scaleUserId 体脂秤用户id
     * <AUTHOR>
     * @date 16:37 2022/6/15
     * @return com.mrk.yudong.core.model.ResDTO<com.mrk.yudong.equipment.model.BodyFatScale>
     **/
    BodyFatScaleInfoVO bodyFatScaleInfo(Long scaleUserId);

    List<ScaleHealthDTO> listScaleHealth(@RequestParam("userId") Long userId);
    /** 健康报告
     * @param bodyFatScaleId 称量id
     * <AUTHOR>
     * @date 16:37 2022/6/15
     * @return com.mrk.yudong.core.model.ResDTO<com.mrk.yudong.equipment.model.BodyFatScale>
     **/
    HealthReportVO healthReport(Long bodyFatScaleId);
    /**
     * 结论文案
     * @param scaleUserId 体脂秤用户id
     * <AUTHOR>
     * @date 19:32 2022/7/4
     * @return java.lang.String
     **/
    String summarizeCopywriting(Long scaleUserId);

    /** 查询推荐计划
     * <AUTHOR>
     * @date 16:37 2022/6/15
     * @return com.mrk.yudong.core.model.ResDTO<com.mrk.yudong.equipment.model.BodyFatScale>
     **/
    List<RecommendedPlanVO> recommendedPlan(Long scaleUserId,Long userId);

    TargetWeightGapDTO targetWeightGap();
}
