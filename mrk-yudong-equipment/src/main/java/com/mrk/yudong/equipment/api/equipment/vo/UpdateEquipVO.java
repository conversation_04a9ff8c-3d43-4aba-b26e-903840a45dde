package com.mrk.yudong.equipment.api.equipment.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.merach.sun.device.dto.resp.model.ModelFeatureDTO;
import lombok.Data;

import java.util.List;

/**
 * @description: 绑定、更新设备返回对象
 * @author: ljx
 * @create: 2022/6/28 14:37
 * @Version 1.0
 **/
@Data
public class UpdateEquipVO {

    /**
     * 设备id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 一级设备类型id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long oneLevelTypeId;

    /**
     * 二级设备类型id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long twoLevelTypeId;

    /**
     * 二级设备类型图片
     */
    private String twoLevelImage;

    /**
     * 二级设备类型名称
     */
    private String twoLevelName;
    /**
     * 通信协议：1:麦瑞克,2:FTMS,3:智健,4:柏群,5:FTMS+智健
     */
    private Integer communicationProtocol;

    /**
     * 通信类型，1:彩屏，2：蓝牙电子表，3：wifi电子表，4:无，5：物联网双模
     */
    private Integer communicationType;

    /**
     * ota类型
     */
    private Integer otaType;
    /**
     * 特征值
     */
    private Integer eigenValue;
    /**
     * mac地址
     */
    private String code;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 蓝牙别名
     */
    private String bluetoothAlias;

    /**
     * 操作记录状态 1.首次绑定，2连接
     */
    private Integer operationRecordStatus;

    /**
     * 设备所属产品的ProductKey
     */
    private String productKey;

    /**
     * IoT 设备在产品内的唯一标识符
     */
    private String deviceName;

    /**
     * iot 为设备颁发的设备密钥
     */
    private String deviceSecret;

    /**
     * iot实例id
     */
    private String iotInstanceId;
    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品分类
     */
    private Integer productType;

    /**
     * 帮助中心
     */
    private String helpCenter;

    /**
     * 产品说明
     */
    private String productManual;


    /**
     * 是否merit设备
     */
    private Integer isMerit;

    /**
     * 是否支持ota
     */
    private Integer isOta;
    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 当前固件版本
     */
    private String firmwareVersion;
    /**
     * 特征描述
     */
    private List<ModelFeatureDTO> featureDescription;

    /**
     * 是否绑定操作
     */
    private Integer isBind;

    /**
     * 电极类型 1-4电极，2-8电极
     */
    private Integer electrodeType;

    /**
     * sn码，全局唯一设备标识
     */
    private String snCode;
}
