package com.mrk.yudong.equipment.api.scale.dto;

import com.mrk.yudong.equipment.api.scale.dto.cmd.HealthStandardCmd;
import com.mrk.yudong.equipment.api.scale.dto.cmd.HealthStandardIntervalCmd;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @description: 体脂秤测量数据
 * @author: ljx
 * @create: 2022/6/17 11:22
 * @Version 1.0
 **/
@Data
public class BodyFatScaleFrom {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 体脂秤用户id
     */
    @NotNull(message = "请选择称量用户")
    private Long scaleUserId;

    /**
     * 数据状态，1.正常，2异常（客户端无需填写）
     */
    private Integer status;

    /**
     * 本次测量id（客户端无需填写）
     */
    private Long snowflakeNextId;
    /**
     * 性别（客户端无需填写）
     */
    private Integer sex;

    /**
     * 1.四电极上报，2 手动，3 八电极上报
     */
    private Integer insertType;
    /**
     * 型号ID
     */
    private Long modelId;

    /**
     * 电极类型 1-4电极，2-8电极
     */
    @Size(min = 1, max = 2)
    private Integer electrodeType;
    /***********************以下为健康数据**************************/

    /**
     * 身体得分（健康评分）
     */
    private Integer score;

    /**
     * 重量（kg）
     */
    private String weight;

    /**
     * 身高
     */
    private String height;

    /**
     * bmi
     */
    private String bmi;

    /**
     * 身体年龄
     */
    private Integer bodyAge;

    /**
     * 体脂率（脂肪率）
     */
    private String bodyFatRate;

    /**
     * 肌肉（肌肉量）（kg）
     */
    private String muscle;

    /**
     * 去脂体重（kg）
     */
    private String leanBodyMass;

    /**
     * 皮下脂肪率
     */
    private String subcutaneousFat;

    /**
     * 内脏脂肪等级
     */
    private String visceralFat;

    /**
     * 基础代谢
     */
    private String basalMetabolism;

    /**
     * 骨骼肌量
     */
    private String skeletalMuscleRatio;

    /**
     * 蛋白质率
     */
    private String protein;

    /**
     * 水分（体水分率）
     */
    private String waterContent;
    /**
     * 体型（身体类型）
     */
    private String bodyType;

    /***********************八极体脂秤数据********************/


    /**
     * 体脂量（脂肪量）
     */
    private String bodyFatMass;

    /**
     * 肌肉率
     */
    private String muscleRate;

    /**
     * 建议卡路里摄入
     */
    private String suggestCalorieIntake;

    /**
     * 肥胖水平（肥胖度）
     */
    private String obesityLevel;

    /**
     * 理想体重
     */
    private String idealBodyWeight;

    /**
     * 控制体重
     */
    private String controlBodyWeight;

    /**
     * 体水分量
     */
    private String waterMass;

    /**
     * 蛋白量（蛋白质量）
     */
    private String proteinMass;

    /**
     * 无机盐量
     */
    private String inorganicSaltMass;

    /**
     * 身体细胞量
     */
    private String bodyCellMass;

    /**
     * 细胞外水量
     */
    private String extracellularWaterVolume;

    /**
     * 细胞内水量
     */
    private String intracellularWaterVolume;

    /**
     * 皮下脂肪量
     */
    private String subcutaneousFatMass;

    /**
     * 骨骼肌率
     */
    private String skeletalMuscleRate;

    /**
     * 骨骼肌质量指数
     */
    private String skeletalMuscleMassExponent;

    /**
     * 脂肪控制量
     */
    private String fatControl;

    /**
     * 肌肉控制量
     */
    private String muscleControl;
    /**
     * 心率
     */
    private String heartRate;

    /**
     * 推测腰臀比
     */
    private String waistHipRate;
    /**
     * 骨量
     */
    private String boneMass;

    /*******************************节段数据***************/

    /**
     * 左上肢脂肪率
     */
    private String leftArmFatRate;

    /**
     * 右上肢脂肪率
     */
    private String rightArmFatRate;

    /**
     * 左下肢脂肪率
     */
    private String leftLegFatRate;

    /**
     * 右下肢脂肪率
     */
    private String rightLegFatRate;

    /**
     * 躯干脂肪率
     */
    private String allBodyFatRate;

    /**
     * 左上肢肌肉
     */
    private String leftArmMuscle;

    /**
     * 左上肢肌肉率
     */
    private String leftArmMuscleRate;

    /**
     * 右上肢肌肉
     */
    private String rightArmMuscle;

    /**
     * 右上肢肌肉率
     */
    private String rightArmMuscleRate;

    /**
     * 左下肢肌肉
     */
    private String leftLegMuscle;

    /**
     * 左下肢肌肉率
     */
    private String leftLegMuscleRate;

    /**
     * 右下肢肌肉
     */
    private String rightLegMuscle;

    /**
     * 右下肢肌肉率
     */
    private String rightLegMuscleRate;

    /**
     * 躯干肌肉
     */
    private String trunkMuscle;

    /**
     * 躯干肌肉率
     */
    private String trunkMuscleRate;

    /**
     * 左上肢阻抗
     */
    private String leftArmImpedance;

    /**
     * 右上肢阻抗
     */
    private String rightArmImpedance;

    /**
     * 左下肢阻抗
     */
    private String leftLegImpedance;

    /**
     * 右下肢阻抗
     */
    private String rightLegImpedance;

    /**
     * 躯干阻抗
     */
    private String bodyImpedance;


    /**
     * 左上肢脂肪
     */
    private String leftArmFat;

    /**
     * 右上肢脂肪
     */
    private String rightArmFat;

    /**
     * 左下肢脂肪
     */
    private String leftLegFat;

    /**
     * 右下肢脂肪
     */
    private String rightLegFat;
    /**
     * 躯干脂肪量
     */
    private String allBodyFat;

    /**
     * 左上肢骨骼肌
     */
    private String leftArmSkeletalMuscle;

    /**
     * 右上肢骨骼肌
     */
    private String rigthArmSkeletalMuscle;

    /**
     * 左下肢骨骼肌
     */
    private String leftLegSkeletalMuscle;

    /**
     * 右下肢骨骼肌
     */
    private String righLegSkeletalMuscle;

    /**
     * 四肢骨骼肌质量指数
     */
    private String limbsSkeletalMuscle;

    /**
     * 脂肪均衡
     */
    private String fatBalance;

    /**
     * 肌肉均衡
     */
    private String muscleBalance;

    /**
     * 状态区间数据
     */
    private HealthStandardIntervalCmd healthStandardInterval;

    /**
     * 状态数据
     */
    private HealthStandardCmd healthStandard;

    /**
     * 上报数据
     */
    private String upData;

    /**
     * 是否需要计算
     */
    private Integer isNeedCalculate;

}
