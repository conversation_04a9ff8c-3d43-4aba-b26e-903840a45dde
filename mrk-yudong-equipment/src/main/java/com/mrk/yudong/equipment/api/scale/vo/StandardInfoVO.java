package com.mrk.yudong.equipment.api.scale.vo;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;

/**
 * @description: 体脂秤报告、基础信息
 * @author: ljx
 * @create: 2022/6/15 16:50
 * @Version 1.0
 **/
@Data
public class StandardInfoVO {

    /**
     * 健康数据id
     **/
    private Long id;

    /**
     * 健康属性id
     **/
    private Integer  healthAttributesId;
    /**
     * 名称
     **/
    private String name;
    /**
     * 图标
     **/
    private String icon;
    /**
     * 标准 code
     **/
    private String standard;

    /**
     * 标准名称
     **/
    private String standardName;

    /**
     * 标准区间
     **/
    private JSONArray standardRange;

    /**
     * 标准文案
     **/
    private String standardCopywriting;

    /**
     *  背景颜色
     */
    private String backGroundColor;
    /**
     *  区间code
     */
    private Integer code;

    /**
     *  字体颜色
     */
    private String fontColor;

    /**
     * 数据
     **/
    private String scaleData;

    /**
     * 单位
     **/
    private String unit;

    /**
     * 趋势：0无状态，1上升，2下降,
     */
    private Integer  trend;

    /**
     *  背景图
     */
    private String backgroundImage;

    /**
     *  是否支持跳转
     */
    private Boolean isSupportSkip = Boolean.TRUE;

    /**
     *  身体成分类型,1.基本指标，2.身体成分，3.其他指标，4.节段脂肪，5.节段肌肉，6.节段阻抗
     */
    private Integer bodyCompositionType;

}
