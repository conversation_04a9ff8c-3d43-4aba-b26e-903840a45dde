package com.mrk.yudong.equipment.biz.scale.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.merach.sun.device.api.ReportApi;
import com.merach.sun.device.dto.cmd.scale.CreateBodyFatScaleCmd;
import com.merach.sun.device.enums.CommunicationProtocolEnum;
import com.merach.sun.device.enums.ElectrodeTypeEnum;
import com.merach.sun.device.enums.ProductEnum;
import com.merach.sun.snowflake.core.SnowflakeTool;
import com.merach.sun.user.dto.cmd.account.AccountUpdateCmd;
import com.merach.sun.user.dto.cmd.account.UserHealthCmd;
import com.merach.sun.user.enums.UserHealthSourceEnum;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.equipment.api.scale.dto.BodyFatScaleFrom;
import com.mrk.yudong.equipment.api.scale.dto.cmd.HealthStandardCmd;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentInfoService;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentTypeService;
import com.mrk.yudong.equipment.biz.scale.bo.BodyFatScaleBO;
import com.mrk.yudong.equipment.biz.scale.bo.StandardsSectionBO;
import com.mrk.yudong.equipment.biz.scale.constant.HealthConstant;
import com.mrk.yudong.equipment.biz.scale.enums.BodyTypeEnum;
import com.mrk.yudong.equipment.biz.scale.enums.LefuBodySegmentStandardEnum;
import com.mrk.yudong.equipment.biz.scale.enums.MessageStatusEnum;
import com.mrk.yudong.equipment.biz.scale.service.*;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType;
import com.mrk.yudong.equipment.infrastructure.scale.mapper.RecordUserAssociationMapper;
import com.mrk.yudong.equipment.infrastructure.scale.model.*;
import com.mrk.yudong.equipment.infrastructure.user.gateway.UserGateway;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @description: 体脂秤上报数据服务类
 * @author: ljx
 * @create: 2023/8/3 16:24
 * @Version 1.0
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ScaleReportServiceImpl implements IScaleReportService {
    private final IScaleUserService scaleUserService;
    private final RecordUserAssociationMapper recordUserAssociationMapper;
    private final ReportApi reportApi;
    private final IHealthStandardsService healthStandardsService;
    private final IEquipmentInfoService equipmentInfoService;
    private final IEquModelAttributesStandardsRelService modelAttributesStandardsRelService;
    private final IScaleRecordService scaleRecordService;
    private final IHealthAttributeService healthAttributeService;
    private final StringRedisTemplate redisTemplate;
    private final IEquipmentTypeService equipmentTypeService;

    private final UserGateway userGateway;

    @Override
    public String reportBodyFatScale(BodyFatScaleFrom bodyFatScale) {
        log.info("体脂秤用户上报数据,userId:{},bodyFatScale:{}", SessionUtil.getId(), JSONObject.toJSONString(bodyFatScale));
        if (Objects.isNull(bodyFatScale)) {
            return null;
        }
        if (Boolean.FALSE.equals(initBobdyFatScale(bodyFatScale))) {
            return null;
        }

        //查询用户身高
        ScaleUser scaleUser = scaleUserService.getById(bodyFatScale.getScaleUserId());
        if (scaleUser == null) {
            log.warn("未查询到体脂秤用户：{}", bodyFatScale.getScaleUserId());
            return null;
        }
        bodyFatScale.setHeight(scaleUser.getHeight().toString());
        bodyFatScale.setSex(scaleUser.getSex());

        RecordUserAssociation recordUserAssociation = saveRecordUserAssociation(bodyFatScale);
        if (null == recordUserAssociation) {
            return null;
        }
        if (bodyFatScale.getInsertType().equals(HealthConstant.MANUAL_REPORTING)) {
            return manualReporting(bodyFatScale, recordUserAssociation);
        }
        cacheScaleData(bodyFatScale, recordUserAssociation);
        reportDevice(bodyFatScale, recordUserAssociation);
        updateMainUser(bodyFatScale, scaleUser, recordUserAssociation.getId());
        return recordUserAssociation.getId().toString();
    }

    @Override
    public Boolean saveRecords(BodyFatScaleBO bodyFatScale) {
        String lockKey = String.format(RedisKeyConstant.BODY_FAT_SACLE_LOCK, bodyFatScale.getBizId());
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 3, TimeUnit.SECONDS);
        if (lock == null || !lock) {
            log.warn("数据正在计算，bizId:{}", bodyFatScale.getBizId());
            return Boolean.FALSE;
        }
        try {
            return processBodyFatScaleData(bodyFatScale);
        } catch (Exception e) {
            log.warn("体脂秤明细计算异常,userId:{}", bodyFatScale.getUserId(), e);
            return Boolean.FALSE;
        } finally {
            redisTemplate.delete(lockKey);
        }
    }

    private Boolean processBodyFatScaleData(BodyFatScaleBO bodyFatScale) {
        BaseQuery<ScaleRecord> lq = new BaseQuery<>();
        lq.eq("measure_id", bodyFatScale.getBizId());
        if (Boolean.TRUE.equals(scaleRecordService.isExist(lq))) {
            log.warn("数据已存在，bizId:{}", bodyFatScale.getBizId());
            return Boolean.FALSE;
        }

        Integer messageStatus = MessageStatusEnum.CONSUME_SUCCESS.getCode();
        //查询用户当前使用的体脂称型号
        EquipmentInfo sacleInfo = getEquipmentInfo(bodyFatScale.getUserId());
        if (sacleInfo == null) {
            log.warn("未查询到用户当前使用的体脂称型号userId:{}", bodyFatScale.getUserId());
            return Boolean.FALSE;
        }

        //根据型号查询需要计算的规则
        List<ModelAttributesStandardsRel> modelAttributesStandardsRels = getModelAttributesStandardsRels(sacleInfo);
        if (CollUtil.isEmpty(modelAttributesStandardsRels)) {
            log.warn("未查询到用户当前使用的体脂称型号的计算规则关联关系userId:{},modelId：{}", bodyFatScale.getUserId(), sacleInfo.getTwoLevelTypeId());
            return Boolean.FALSE;
        }

        //计算组装 标准 区间
        RecordUserAssociation recordUserAssociation = recordUserAssociationMapper.selectById(bodyFatScale.getBizId());
        if (recordUserAssociation == null) {
            log.warn("未查询到测量记录：{}", bodyFatScale.getBizId());
            return null;
        }
        ScaleUser scaleUser = scaleUserService.getById(recordUserAssociation.getScaleUserId());
        if (scaleUser == null) {
            log.warn("未查询到体脂秤用户：{}", bodyFatScale.getBizId());
            return Boolean.FALSE;
        }
        Map<String, Object> currentStandardResult = calculateStandard(bodyFatScale, modelAttributesStandardsRels, scaleUser);
        if (MapUtil.isEmpty(currentStandardResult)) {
            log.warn("体脂秤标准计算失败,userId:{}", bodyFatScale.getUserId());
            return Boolean.FALSE;
        }

        boolean saveBatch = scaleRecordService.saveBatch(generateRecord(bodyFatScale, scaleUser, currentStandardResult));
        if (!saveBatch) {
            log.warn("体脂秤标准保存失败,userId:{}", bodyFatScale.getUserId());
            messageStatus = MessageStatusEnum.CONSUME_FAIL.getCode();
        }

        recordUserAssociation.setMessageStatus(messageStatus);
        return recordUserAssociationMapper.updateById(recordUserAssociation) > 0;
    }

    private HashMap<String, Object> getStandardSaction(BodyFatScaleFrom bodyFatScaleFrom) {
        HashMap<String, Object> map = new HashMap<>();
        if (Objects.isNull(bodyFatScaleFrom) || Objects.isNull(bodyFatScaleFrom.getHealthStandardInterval())) {
            return null;
        }

        HashMap standardSactions = JSON.parseObject(JSON.toJSONString(bodyFatScaleFrom.getHealthStandardInterval()), HashMap.class);
        standardSactions.forEach((k, v) -> {
            if (StrUtil.isNotBlank(v.toString())) {
                processIntervalData(v.toString(), k + "Section", map);
            }
        });
        return map;
    }

    @Nullable
    private BodyFatScaleFrom getBodyFatScaleFrom(Long bizId) {
        String weighingData = redisTemplate.opsForValue().get(String.format(RedisKeyConstant.BODY_FAT_SCALE_KEY, bizId.toString()));
        if (StrUtil.isBlank(weighingData)) {
            return null;
        }

        return JSONObject.parseObject(weighingData, BodyFatScaleFrom.class);
    }

    private DefaultContext<String, Object> setDefaultContext(ScaleUser scaleUser, Map<String, Object> bodyFatScaletMap) {
        DefaultContext<String, Object> context = new DefaultContext<>();
        //将bodyFatScale转换成map并放入context中
        context.putAll(bodyFatScaletMap);
        context.put("sex", scaleUser.getSex());
        context.put("age", DateUtil.ageOfNow(scaleUser.getBirthday()));
        return context;
    }

    private void setTrend(Map<Integer, String> lastWeighing, HealthAttribute healthAttribute, ScaleRecord scaleRecord) {
        if (MapUtil.isEmpty(lastWeighing)) {
            return;
        }
        String lastValue = lastWeighing.get(healthAttribute.getId());
        if (StrUtil.isBlank(lastValue)) {
            return;
        }
        if (StrUtil.isEmpty(scaleRecord.getHealthAttributesValue())) {
            scaleRecord.setHealthAttributesValue("0.00");
        }
        log.info("lastValue:{},healthAttributesValue:{}", lastValue, scaleRecord.getHealthAttributesValue());
        BigDecimal subtract = new BigDecimal(scaleRecord.getHealthAttributesValue()).subtract(new BigDecimal(lastValue));
        if (subtract.doubleValue() > 0) {
            scaleRecord.setTrend(HealthConstant.UP);
        } else if (subtract.doubleValue() < 0) {
            scaleRecord.setTrend(HealthConstant.DOWN);
        }
        scaleRecord.setTrendValue(subtract.setScale(2, RoundingMode.HALF_UP).doubleValue());

    }

    private List<ScaleRecord> generateRecord(BodyFatScaleBO bodyFatScale, ScaleUser scaleUser, Map<String, Object> currentStandardResult) {
        Map<Integer, String> lastWeighing = lastWeighing(scaleUser);
        List<ScaleRecord> scaleRecords = new ArrayList<>();
        List<HealthAttribute> healthAttributes = healthAttributeService.list();
        Map<String, Object> bodyFatScaleMap = BeanUtil.beanToMap(bodyFatScale);

        for (HealthAttribute healthAttribute : healthAttributes) {
            if (ElectrodeTypeEnum.FOUR.getCode().equals(bodyFatScale.getElectrodeType()) && healthAttribute.getId() > 15) {
                continue;
            }
            ScaleRecord scaleRecord = new ScaleRecord();
            scaleRecord.setMeasureId(bodyFatScale.getBizId());
            scaleRecord.setHealthAttributesId(healthAttribute.getId());
            scaleRecord.setHealthAttributesValue(MapUtil.getStr(bodyFatScaleMap, healthAttribute.getAttribute()));

            setTrend(lastWeighing, healthAttribute, scaleRecord);
            scaleRecord.setStandardStatus(MapUtil.getStr(currentStandardResult, healthAttribute.getId().toString()));

            String remark = JSONObject.toJSONString(currentStandardResult.get("section" + healthAttribute.getId()));
            if (StrUtil.isNotBlank(remark) && !"null".equals(remark)) {
                scaleRecord.setRemark(remark);
            }

            scaleRecord.setCreateId(bodyFatScale.getUserId());
            scaleRecord.setCreateTime(LocalDateTime.now());
            scaleRecords.add(scaleRecord);
            log.info("scaleRecord:{}", JSONObject.toJSONString(scaleRecord));
        }

        calculateAndAddBodyTypeRecord(scaleUser, scaleRecords);
        return scaleRecords;
    }

    private void calculateAndAddBodyTypeRecord(ScaleUser scaleUser, List<ScaleRecord> scaleRecords) {
        String bodyFatRate = null;
        String muscle = null;

        for (ScaleRecord record : scaleRecords) {
            if (StrUtil.isBlank(record.getRemark()) || "null".equals(record.getRemark())) {
                continue;
            }
            if (record.getHealthAttributesId().equals(HealthConstant.BODYFATRATE)) {
                bodyFatRate = parseRemarkForValue(record, HealthConstant.BODYFATRATE);
            }
            if (record.getHealthAttributesId().equals(HealthConstant.MUSCLE)) {
                muscle = parseRemarkForValue(record, HealthConstant.MUSCLE);
            }
        }

        BodyTypeEnum bodyType = BodyTypeEnum.calculateBodyType(scaleUser.getSex(), muscle, bodyFatRate);
        if (null != bodyType) {
            scaleRecords.stream().filter(v -> v.getHealthAttributesId().equals(HealthConstant.BODYTYPE)).findFirst().ifPresent(v -> v.setStandardStatus(bodyType.getCode().toString()));
        }
    }

    private String parseRemarkForValue(ScaleRecord record, int targetAttributeId) {
        return JSONArray.parseArray(record.getRemark(), StandardsSectionBO.class)
                .stream()
                .filter(v -> v.getCode().toString().equals(record.getStandardStatus()))
                .filter(v -> record.getHealthAttributesId() == targetAttributeId)
                .findFirst()
                .map(StandardsSectionBO::getName)
                .orElse(null);
    }

    private Map<Integer, String> lastWeighing(ScaleUser scaleUser) {
        RecordUserAssociation lastRecordUserAssociation = recordUserAssociationMapper.lastScaleDate(scaleUser.getId(), 0);
        Map<Integer, String> healthAttributesMap = null;
        if (lastRecordUserAssociation != null) {
            BaseQuery<ScaleRecord> scaleRecordBaseQuery = new BaseQuery<>();
            scaleRecordBaseQuery.eq("measure_id", lastRecordUserAssociation.getId());
            List<ScaleRecord> scaleRecords = scaleRecordService.list(scaleRecordBaseQuery);
            if (CollUtil.isNotEmpty(scaleRecords)) {
                healthAttributesMap = scaleRecords.stream().filter(v -> StrUtil.isNotBlank(v.getHealthAttributesValue())).collect(Collectors.toMap(ScaleRecord::getHealthAttributesId, ScaleRecord::getHealthAttributesValue));
            }
        }
        return healthAttributesMap;
    }

    private Map<String, Object> calculateStandard(BodyFatScaleBO bodyFatScale, List<ModelAttributesStandardsRel> modelAttributesStandardsRels, ScaleUser scaleUser) {
        Map<String, Object> result = new HashMap<>();
        //使用ql表达式计算规则
        ExpressRunner runner = new ExpressRunner();
        Map<String, Object> bodyFatScaletMap = BeanUtil.beanToMap(bodyFatScale);
        DefaultContext<String, Object> context = setDefaultContext(scaleUser, bodyFatScaletMap);
        //乐福八电极体脂秤直接获取缓存数据，将标准数据拆分成数组，匹配其所属的标准
        BodyFatScaleFrom bodyFatScaleFrom = getBodyFatScaleFrom(bodyFatScale.getBizId());
        Optional.ofNullable(getStandardSaction(bodyFatScaleFrom)).ifPresent(context::putAll);

        List<HealthStandards> healthStandards = healthStandardsService.listByIds(modelAttributesStandardsRels.stream().map(ModelAttributesStandardsRel::getStandardsId).collect(Collectors.toList()));
        if (CollUtil.isEmpty(healthStandards)) {
            log.warn("未查询到用户当前使用的体脂称型号的计算规则的标准");
            return null;
        }
        Map<Long, ModelAttributesStandardsRel> groupModelAttributesStandardsRelByStandardsId = modelAttributesStandardsRels.stream().collect(Collectors.toMap(ModelAttributesStandardsRel::getStandardsId, v -> v));
        for (HealthStandards healthStandard : healthStandards) {
            String dataFormula = healthStandard.getStandardsDataFormula();
            log.info("healthStandard:{}", healthStandard.getDescription());
            if (StrUtil.isNotBlank(dataFormula)) {
                log.info("数据计算公式：{}", dataFormula);
                try {
                    Object execute = runner.execute(dataFormula, context, null, true, true);
                    if (execute != null) {
                        JSONObject data = JSONObject.parseObject((String) execute);
                        Map<String, Object> dataInnerMap = data.getInnerMap();
                        context.putAll(dataInnerMap);
                        //设置标准区间
                        setStandardSection(result, healthStandard, dataInnerMap, groupModelAttributesStandardsRelByStandardsId);
                    }
                } catch (Exception e) {
                    log.info("体脂秤属性:{}区间计算公式执行异常：{}", healthStandard.getDescription(), e.getMessage());
                }
            }

            if (StrUtil.isNotBlank(healthStandard.getStandardsSectionFormula())) {
                log.info("标准计算公式：{}", healthStandard.getStandardsSectionFormula());
                try {
                    Object standardsSection = runner.execute(healthStandard.getStandardsSectionFormula(), context, null, true, true);
                    if (standardsSection != null) {
                        //命中标准
                        result.put(healthStandard.getAttributesId().toString(), healthStandard.getId());
                    }
                    log.info("计算结果：{}", standardsSection);
                } catch (Exception e) {
                    log.info("体脂秤属性:{}标准计算公式执行异常：{}", healthStandard.getDescription(), e.getMessage());
                }
            }
        }
        if (Objects.isNull(bodyFatScaleFrom) || Objects.isNull(bodyFatScaleFrom.getHealthStandard())) {
            return result;
        }
        setLefuBodySegmentStandard(result, bodyFatScaleFrom.getHealthStandard());
        return result;
    }

    private void setLefuBodySegmentStandard(Map<String, Object> result, HealthStandardCmd healthStandard) {
        if (StrUtil.isNotEmpty(healthStandard.getLeftArmFatStandard())) {
            result.put(HealthConstant.LEFTARMFATRATE.toString(), LefuBodySegmentStandardEnum.getIdByLefuId(healthStandard.getLeftArmFatStandard()));
        }
        if (StrUtil.isNotEmpty(healthStandard.getRightArmFatStandard())) {
            result.put(HealthConstant.RIGHTARMFATRATE.toString(), LefuBodySegmentStandardEnum.getIdByLefuId(healthStandard.getRightArmFatStandard()));
        }
        if (StrUtil.isNotEmpty(healthStandard.getLeftLegFatStandard())) {
            result.put(HealthConstant.LEFTLEGFATRATE.toString(), LefuBodySegmentStandardEnum.getIdByLefuId(healthStandard.getLeftLegFatStandard()));
        }
        if (StrUtil.isNotEmpty(healthStandard.getRightLegFatStandard())) {
            result.put(HealthConstant.RIGHTLEGFATRATE.toString(), LefuBodySegmentStandardEnum.getIdByLefuId(healthStandard.getRightLegFatStandard()));
        }
        if (StrUtil.isNotEmpty(healthStandard.getAllBodyFatStandard())) {
            result.put(HealthConstant.ALLBODYFATRATE.toString(), LefuBodySegmentStandardEnum.getIdByLefuId(healthStandard.getAllBodyFatStandard()));
        }
        if (StrUtil.isNotEmpty(healthStandard.getRightArmMuscleStandard())) {
            result.put(HealthConstant.RIGHTARMMUSCLERATE.toString(), LefuBodySegmentStandardEnum.getIdByLefuId(healthStandard.getRightArmMuscleStandard()));
        }
        if (StrUtil.isNotEmpty(healthStandard.getLeftArmMuscleStandard())) {
            result.put(HealthConstant.LEFTARMMUSCLERATE.toString(), LefuBodySegmentStandardEnum.getIdByLefuId(healthStandard.getLeftArmMuscleStandard()));
        }
        if (StrUtil.isNotEmpty(healthStandard.getRightLegMuscleStandard())) {
            result.put(HealthConstant.RIGHTLEGMUSCLERATE.toString(), LefuBodySegmentStandardEnum.getIdByLefuId(healthStandard.getRightLegMuscleStandard()));
        }
        if (StrUtil.isNotEmpty(healthStandard.getLeftLegMuscleStandard())) {
            result.put(HealthConstant.LEFTLEGMUSCLERATE.toString(), LefuBodySegmentStandardEnum.getIdByLefuId(healthStandard.getLeftLegMuscleStandard()));
        }
        if (StrUtil.isNotEmpty(healthStandard.getTrunkMuscleStandard())) {
            result.put(HealthConstant.TRUNKMUSCLERATE.toString(), LefuBodySegmentStandardEnum.getIdByLefuId(healthStandard.getTrunkMuscleStandard()));
        }
    }

    private void setStandardSection(Map<String, Object> standardInterval, HealthStandards healthStandard, Map<String, Object> dataInnerMap, Map<Long, ModelAttributesStandardsRel> groupModelAttributesStandardsRelBYStandardsId) {
        StandardsSectionBO standard = new StandardsSectionBO();
        standard.setName(healthStandard.getName());
        standard.setCode(healthStandard.getId());
        String min = "";
        if (dataInnerMap.containsKey("min")) {
            min = dataInnerMap.get("min").toString();
        } else if (dataInnerMap.containsKey("min".concat(healthStandard.getId().toString()))) {
            min = dataInnerMap.get("min".concat(healthStandard.getId().toString())).toString();
        }
        standard.setMinData(min);
        String max = "";
        if (dataInnerMap.containsKey("max")) {
            max = dataInnerMap.get("max").toString();
        } else if (dataInnerMap.containsKey("max".concat(healthStandard.getId().toString()))) {
            max = dataInnerMap.get("max".concat(healthStandard.getId().toString())).toString();
        }
        standard.setMaxData(max);
        ModelAttributesStandardsRel modelAttributesStandardsRel = MapUtil.get(groupModelAttributesStandardsRelBYStandardsId, healthStandard.getId(), ModelAttributesStandardsRel.class);
        if (modelAttributesStandardsRel != null) {
            standard.setSeq(modelAttributesStandardsRel.getSeq());
        }
        String sectionKey = "section".concat(healthStandard.getAttributesId().toString());
        JSONArray standardArray = new JSONArray();
        if (standardInterval.containsKey(sectionKey)) {
            //区间
            List<StandardsSectionBO> sectionBOS = JSONArray.parseArray(standardInterval.get(sectionKey).toString(), StandardsSectionBO.class);
            sectionBOS.add(standard);
            List<StandardsSectionBO> standardsSectionBOS = sectionBOS.stream().sorted(Comparator.comparing(StandardsSectionBO::getSeq)).collect(Collectors.toList());
            JSONArray jsonArray = new JSONArray();
            jsonArray.addAll(standardsSectionBOS);
            standardInterval.put(sectionKey, jsonArray);
        } else {
            standardArray.add(JSONObject.toJSON(standard));
            standardInterval.put(sectionKey, standardArray);
        }
    }

    private List<ModelAttributesStandardsRel> getModelAttributesStandardsRels(EquipmentInfo sacleInfo) {
        LambdaQueryWrapper<ModelAttributesStandardsRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ModelAttributesStandardsRel::getModelId, sacleInfo.getTwoLevelTypeId());
        return modelAttributesStandardsRelService.list(queryWrapper);
    }

    private EquipmentInfo getEquipmentInfo(Long userId) {
        List<EquipmentInfo> equipments = equipmentInfoService.findEquipmentByProductId(ProductEnum.BODY_SCALE.getId(), userId);
        if (CollUtil.isEmpty(equipments)) {
            return null;
        }
        return equipments.get(0);
    }

    private void updateMainUser(BodyFatScaleFrom bodyFatScale, ScaleUser scaleUser, Long measureId) {
        if (bodyFatScale.getInsertType().equals(HealthConstant.MANUAL_REPORTING) || scaleUser.getIsMain().equals(BaseConstant.INT_FALSE)) {
            return;
        }

        try {
            AccountUpdateCmd accountUpdateCmd = new AccountUpdateCmd();
            accountUpdateCmd.setAccountId(SessionUtil.getId());
            UserHealthCmd userHealth = new UserHealthCmd();
            userHealth.setHeight(BigDecimal.valueOf(scaleUser.getHeight()));
            userHealth.setWeight(new BigDecimal(bodyFatScale.getWeight()).setScale(1, RoundingMode.HALF_UP));
            userHealth.setOriginId(measureId);
            userHealth.setBodyFatRate(formatToTwoDecimalPlaces(bodyFatScale.getBodyFatRate()));
            if (ElectrodeTypeEnum.FOUR.getCode().equals(bodyFatScale.getElectrodeType())) {
                userHealth.setChannel(UserHealthSourceEnum.SCALE_AUTOMATIC.getCode());
            } else {
                userHealth.setChannel(UserHealthSourceEnum.EIGHT_SCALE_AUTOMATIC.getCode());
            }
            accountUpdateCmd.setUserHealth(userHealth);
            userGateway.updateAccount(accountUpdateCmd);
        } catch (Exception e) {
            log.warn("reportBodyFatScale->updateMainUser->updateUserHealth->error", e);
        }

    }

    private void reportDevice(BodyFatScaleFrom bodyFatScale, RecordUserAssociation recordUserAssociation) {
        log.info("reportBodyFatScale->reportDevice");
        CreateBodyFatScaleCmd createBodyFatScaleCmd = BeanUtil.copyProperties(bodyFatScale, CreateBodyFatScaleCmd.class);
        createBodyFatScaleCmd.setBizId(recordUserAssociation.getId());
        createBodyFatScaleCmd.setUserId(SessionUtil.getId());
        log.info("reportBodyFatScale->reportDevice->createBodyFatScaleCmd:{}", JSONObject.toJSONString(createBodyFatScaleCmd));
        reportApi.reportBodyFatScale(createBodyFatScaleCmd);
    }

    private boolean saveWeightRecord(BodyFatScaleFrom bodyFatScale, RecordUserAssociation recordUserAssociation) {
        ScaleRecord scaleRecord = new ScaleRecord();
        scaleRecord.setMeasureId(recordUserAssociation.getId());
        scaleRecord.setHealthAttributesId(HealthConstant.WEIGHT);
        scaleRecord.setHealthAttributesValue(bodyFatScale.getWeight());
        scaleRecord.setCreateId(SessionUtil.getId());
        scaleRecord.setCreateTime(LocalDateTime.now());
        return scaleRecordService.save(scaleRecord);
    }

    private RecordUserAssociation saveRecordUserAssociation(BodyFatScaleFrom bodyFatScale) {
        RecordUserAssociation recordUserAssociation = new RecordUserAssociation();
        recordUserAssociation.setCreateId(SessionUtil.getId());
        recordUserAssociation.setId(SnowflakeTool.getId());
        recordUserAssociation.setCreateTime(LocalDateTime.now());
        recordUserAssociation.setScaleUserId(bodyFatScale.getScaleUserId());
        if (StrUtil.isNotBlank(bodyFatScale.getBodyFatRate()) && !Double.valueOf(bodyFatScale.getBodyFatRate()).equals(0.0)) {
            recordUserAssociation.setStatus(HealthConstant.NORMAL_STATUS_1);
        } else {
            recordUserAssociation.setStatus(HealthConstant.ABNORMAL_STATUS_2);
        }
        recordUserAssociation.setElectrodeType(bodyFatScale.getElectrodeType());
        recordUserAssociation.setModelId(bodyFatScale.getModelId());
        recordUserAssociation.setInsertType(bodyFatScale.getInsertType());
        recordUserAssociation.setMessageStatus(MessageStatusEnum.SEND_SUCCESS.getCode());
        //同步数据状态
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("bodyFatScale", JSONObject.toJSONString(bodyFatScale));
        recordUserAssociation.setRemark(jsonObject.toString());
        int insert = recordUserAssociationMapper.insert(recordUserAssociation);
        if (insert > 0) {
            return recordUserAssociation;
        }
        return null;
    }

    private Boolean initBobdyFatScale(BodyFatScaleFrom bodyFatScale) {


        if (bodyFatScale.getScaleUserId() == null) {
            ScaleUser mainScaleUser = scaleUserService.getMainScaleUser(SessionUtil.getId());
            if (mainScaleUser != null) {
                bodyFatScale.setScaleUserId(mainScaleUser.getId());
            }
        }
        EquipmentInfo sacleInfo = getEquipmentInfo(SessionUtil.getId());
        if (sacleInfo == null) {
            log.warn("未查询到用户当前使用的体脂称型号userId:{}", SessionUtil.getId());
            return null;
        }
        setOldVersion(bodyFatScale, sacleInfo.getTwoLevelTypeId());
        bodyFatScale.setModelId(sacleInfo.getTwoLevelTypeId());
        if (StrUtil.isNotBlank(bodyFatScale.getBodyFatRate()) && !Double.valueOf(bodyFatScale.getBodyFatRate()).equals(0.0)) {
            bodyFatScale.setStatus(HealthConstant.NORMAL_STATUS_1);
        } else {
            bodyFatScale.setStatus(HealthConstant.ABNORMAL_STATUS_2);
        }

        return Boolean.TRUE;
    }

    /**
     * hotfix: 20240517 兼容客户端更新lefu sdk问题，原4电级sdk上报的数据需要服务端计算，对接新8电级sdk后，原4电级数据也不需要计算
     * *******以前为老版本
     */
    private void setOldVersion(BodyFatScaleFrom bodyFatScale, Long modelId) {
        EquipmentType model = equipmentTypeService.getById(modelId);
        if (model.getCommunicationProtocol().equals(CommunicationProtocolEnum.WOLAI.getCode())) {
            bodyFatScale.setIsNeedCalculate(BaseConstant.INT_TRUE);
            bodyFatScale.setElectrodeType(model.getElectrodeType());
        } else if (model.getCommunicationProtocol().equals(CommunicationProtocolEnum.LE_FU.getCode())) {
            bodyFatScale.setIsNeedCalculate(
                    (Objects.isNull(bodyFatScale.getElectrodeType()) || Objects.equals(bodyFatScale.getElectrodeType(), 0L))
                            ? BaseConstant.INT_TRUE
                            : BaseConstant.INT_FALSE
            );
        }

        bodyFatScale.setElectrodeType(model.getElectrodeType());
    }

    private String manualReporting(BodyFatScaleFrom bodyFatScale, RecordUserAssociation recordUserAssociation) {
        boolean saveWeightRecord = saveWeightRecord(bodyFatScale, recordUserAssociation);
        if (!saveWeightRecord) {
            log.warn("保存体重记录失败,userId:{}", bodyFatScale.getScaleUserId());
            return null;
        }
        return recordUserAssociation.getId().toString();
    }

    private void cacheScaleData(BodyFatScaleFrom bodyFatScale, RecordUserAssociation recordUserAssociation) {
        if (ElectrodeTypeEnum.FOUR.getCode().equals(bodyFatScale.getElectrodeType())) {
            return;
        }
        redisTemplate.opsForValue().set(
                String.format(RedisKeyConstant.BODY_FAT_SCALE_KEY, recordUserAssociation.getId().toString()),
                JSONObject.toJSONString(bodyFatScale),
                24,
                TimeUnit.HOURS);
    }

    private void processIntervalData(String intervalData, String sectionPrefix, Map<String, Object> map) {
        //去除intervalData中开头的[和结尾的]
        intervalData = intervalData.replaceAll("[\\[\\]]", "");
        sectionPrefix = StrUtil.removeAll(sectionPrefix, "Interval");

        List<String> intervals = StrUtil.split(intervalData, ",");

        String finalSectionPrefix = sectionPrefix;
        IntStream.range(0, intervals.size()).forEach(i -> {
            String interval = intervals.get(i).trim();
            try {
                if (!interval.isEmpty()) {
                    map.put(finalSectionPrefix + (i + 1), formatToTwoDecimalPlaces(interval));
                }
            } catch (NumberFormatException e) {
                log.warn("无法将间隔字符串: {}转换为BigDecimal", interval);
            }
        });
    }

    @NotNull
    private BigDecimal formatToTwoDecimalPlaces(String number) {
        return new BigDecimal(number).setScale(2, RoundingMode.HALF_UP);
    }
}
