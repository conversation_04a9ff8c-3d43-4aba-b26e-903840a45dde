package com.mrk.yudong.equipment.biz.scale.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.merach.sun.common.exception.BizException;
import com.merach.sun.user.dto.cmd.account.AccountUpdateCmd;
import com.merach.sun.user.dto.cmd.account.UserBasicInfoCmd;
import com.merach.sun.user.dto.cmd.account.UserHealthCmd;
import com.merach.sun.user.dto.user.UserInfoDTO;
import com.merach.sun.user.enums.SexEnum;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.equipment.api.scale.dto.cmd.ScaleUserCmd;
import com.mrk.yudong.equipment.biz.equipment.constant.EquipmentConstant;
import com.mrk.yudong.equipment.biz.scale.service.IScaleUserService;
import com.mrk.yudong.equipment.infrastructure.scale.mapper.ScaleUserMapper;
import com.mrk.yudong.equipment.infrastructure.scale.model.ScaleUser;
import com.mrk.yudong.equipment.infrastructure.user.gateway.UserGateway;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.share.constant.ResponseConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <p>
 * 体脂秤用户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-14
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ScaleUserServiceImpl extends BaseServiceImpl<ScaleUserMapper, ScaleUser> implements IScaleUserService {

    private final UserGateway userGateway;

    private final StringRedisTemplate stringRedisTemplate;

    @Override
    public ScaleUser saveScaleUser(ScaleUserCmd scaleUser) {
        Long userId = SessionUtil.getId();
        if (userId == null) {
            throw new BizException(ResponseConstant.COMMON_NOUSER);
        }

        String key = String.format(RedisKeyConstant.BODY_FAT_SCALE_USER_SAVE, userId);
        Boolean absent = stringRedisTemplate.opsForValue().setIfAbsent(key, key, Duration.ofSeconds(3L));
        if (!BooleanUtil.isTrue(absent)) {
            throw new BizException(String.valueOf(HttpStatus.TOO_MANY_REQUESTS.value()), "请求频繁，请稍后再试");
        }

        scaleUser.setUserId(userId);
        //查询当前是否存在主账号
        boolean isExist = this.isExist("user_id", ConditionEnum.EQ, userId);
        if (!isExist) {
            initMainScaleUser(scaleUser);
        }
        if (StrUtil.isBlank(scaleUser.getAvatar())) {
            scaleUser.setAvatar(EquipmentConstant.DEFAULT_AVATAR);
        }

        if (StrUtil.isNotBlank(scaleUser.getHeight()) && StrUtil.contains(scaleUser.getHeight(), "cm")) {
            scaleUser.setHeight(scaleUser.getHeight().split("cm")[0]);
        }

        ScaleUser dbScaleUser = BeanUtil.copyProperties(scaleUser, ScaleUser.class);
        dbScaleUser.setCreateId(userId);
        dbScaleUser.setCreateTime(LocalDateTime.now());
        this.save(dbScaleUser);

        stringRedisTemplate.delete(key);
        return dbScaleUser;
    }

    private void initMainScaleUser(ScaleUserCmd scaleUser) {
        UserInfoDTO appUserInfo = userGateway.getUserInfoByUserId(scaleUser.getUserId());
        if (Objects.isNull(appUserInfo)) {
            throw new BizException(ResponseConstant.COMMON_NOUSER);
        }
        scaleUser.setAvatar(appUserInfo.getAvatar());
        scaleUser.setNickName(appUserInfo.getNickname());
        scaleUser.setIsMain(BaseConstant.INT_TRUE);
    }

    @Override
    public ScaleUser updateScaleUser(ScaleUser scaleUser) {
        ScaleUser dbScaleUser = baseMapper.selectById(scaleUser.getId());
        boolean isMain = Objects.equals(dbScaleUser.getIsMain(), BaseConstant.INT_TRUE);
        if (isMain) {
            scaleUser.setAvatar(null);
            scaleUser.setNickName(null);
        }
        scaleUser.setUpdateId(SessionUtil.getId());
        scaleUser.setUpdateTime(LocalDateTime.now());
        baseMapper.updateById(scaleUser);
        if (isMain) {
            sendScaleUserUpdateMessage(scaleUser);
        }

        return dbScaleUser;
    }

    @Override
    public Boolean updateMainScaleUser(ScaleUser scaleUser) {
        LambdaQueryWrapper<ScaleUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ScaleUser::getUserId, scaleUser.getUserId())
                .eq(ScaleUser::getIsMain, BaseConstant.INT_TRUE)
                .eq(ScaleUser::getIsDelete, BaseConstant.INT_FALSE);
        return baseMapper.update(scaleUser, lambdaQueryWrapper) > 0;
    }

    @Override
    public ScaleUser getMainScaleUser(Long userId) {
        BaseQuery<ScaleUser> scaleRecordBaseQuery = new BaseQuery<>();
        scaleRecordBaseQuery.eq("user_id", userId).eq("is_main", BaseConstant.INT_TRUE);
        scaleRecordBaseQuery.last("limit 1");
        return baseMapper.selectOne(scaleRecordBaseQuery);
    }

    private void sendScaleUserUpdateMessage(ScaleUser scaleUser) {
        AccountUpdateCmd accountUpdateCmd = new AccountUpdateCmd();
        accountUpdateCmd.setAccountId(SessionUtil.getId());
        UserHealthCmd userHealth = new UserHealthCmd();
        userHealth.setHeight(BigDecimal.valueOf(scaleUser.getHeight()));
        accountUpdateCmd.setUserHealth(userHealth);
        UserBasicInfoCmd basicInfo = new UserBasicInfoCmd();
        basicInfo.setBirthday(LocalDate.parse(scaleUser.getBirthday()));
        basicInfo.setSexEnum(SexEnum.getInstance(scaleUser.getSex()));
        accountUpdateCmd.setUserBasicInfo(basicInfo);
        userGateway.updateAccount(accountUpdateCmd);
    }
}
