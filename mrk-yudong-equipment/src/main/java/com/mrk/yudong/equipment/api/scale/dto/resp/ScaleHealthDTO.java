package com.mrk.yudong.equipment.api.scale.dto.resp;

import lombok.Data;

/**
 * @description:
 * @author: ljx
 * @create: 2023/4/11 15:41
 * @Version 1.0
 **/
@Data
public class ScaleHealthDTO {
    /**
     * 测量id
     */
    private Long measureId;

    /**
     * 健康属性id
     */
    private Integer healthAttributesId;

    /**
     * 属性内容
     */
    private String healthAttributesValue;

    /**
     * 趋势：0无状态，1上升，2下降,
     */
    private Integer  trend;

    /**
     * 单位
     **/
    private String unit;
    /**
     * 趋势值
     */
    private Double  trendValue;
    /**
     * 标准状态
     */
    private String standardStatus;

    /**
     * 标准状态颜色
     */
    private String standardBackgroundColor;


    /**
     * 字体颜色
     */
    private String standardFontColor;
}
