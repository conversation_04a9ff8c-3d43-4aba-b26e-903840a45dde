package com.mrk.yudong.equipment.api.scale.vo;

import cn.hutool.core.lang.Dict;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description: 健康报告数据
 * @author: ljx
 * @create: 2022/6/15 16:47
 * @Version 1.0
 **/
@Data
public class HealthReportVO {

    /**
     * 测量id
     **/
    private Long id;

    /**
     * 测量时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm")
    private LocalDateTime createTime ;

    /**
     * 电极类型 1-4电极，2-8电极
     */
    private Integer electrodeType;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 得分
     */
    private String score;

    /**
     * 年龄
     */
    private Long age;

    /**
     *  性别 0-未知，1男，2女
     */
    private Integer sex;

    /**
     * 身高
     */
    private Double height;

    /**
     * 重量（kg）
     */
    private String weight;

    /**
     * bmi
     */
    private String bmi;

    /**
     * 身体年龄
     */
    private String bodyAge;

    /**
     * 体脂率
     */
    private String bodyFatRate;
    /**
     * 体重差
     */
    private Double weightDifference;

    /**
     * 体重趋势
     */
    private Integer weightTrend;
    /**
     * 体型名称
     */
    private String bodyTypeName;

    /**
     * 体型编号
     */
    private Integer bodyTypeCode;

    /**
     * 体型文案
     */
    private String bodyTypeCopywriting;
    /**
     * 体型列表
     */
    private  List<Dict>  bodyTypeList;

    /**
     * 体型九宫格列表
     */
    private  List<Dict>  scratchableLatexList;

    /**
     * 标准数组
     **/
    private List<StandardInfoVO> standardInfoList;
}
