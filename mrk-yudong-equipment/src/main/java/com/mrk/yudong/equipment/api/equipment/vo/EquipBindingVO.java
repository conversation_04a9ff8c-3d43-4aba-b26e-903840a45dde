package com.mrk.yudong.equipment.api.equipment.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2021−04-21 1:36 下午
 */
@Data
public class EquipBindingVO {
    private Long id;
    /**
     *  mac地址
     */
    private String code;
    /**
     * 一级类型名称
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long oneLevelTypeId;
    /**
     * 一级类型名称
     */
    private String oneLevelTypeName;
    /**
     * 二级类型名称
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long twoTypeId;
    /**
     * 二级类型名称
     */
    private String twoTypeName;
    /**
     * 是否绑定1是 0否
     */
    private Integer isBinding;
    /**
     * 展示图片
     */
    private String image;
    /**
     * 设备蓝牙名称
     */
    private String equipName;
    /**
     * 是否是merach 1是0否
     */
    private Integer isMerach;
    /**
     * 通信协议：1:麦瑞克,2:FTMS,3:智健,4:柏群,5:FTMS+智健
     */
    private Integer communicationProtocol;
    /**
     * ota类型
     */
    private Integer otaType;
    /**
     * 特征值
     */
    private Integer eigenValue;

    /**
     * 用户版本
     */
    private String userVersion;
}
