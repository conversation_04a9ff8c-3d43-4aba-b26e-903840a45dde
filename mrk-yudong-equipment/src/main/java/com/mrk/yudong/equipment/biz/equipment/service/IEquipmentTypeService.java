package com.mrk.yudong.equipment.biz.equipment.service;

import cn.hutool.core.lang.Dict;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.share.dto.equip.EquipmentTypeDTO;
import com.mrk.yudong.share.dto.equip.LiveMappingConvertDTO;
import com.mrk.yudong.share.vo.equip.LiveMappingConvertVO;
import com.mrk.yudong.core.service.BaseService;
import com.mrk.yudong.equipment.api.equipment.vo.EquipBindingVO;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
public interface IEquipmentTypeService extends BaseService<EquipmentType> {
    /**
     * 获取所有设备类型id，name
     *
     * @return List<EquipmentType>
     */
    List<EquipmentType> getEquipmentTypeName();

    /**
     * 从数据库中获取一级类型名称
     *
     * @param infoList 设备信息集合
     * @return 一级类型
     */
    List<EquipmentTypeDTO> getMysqlEquipType(List<EquipmentInfo> infoList);

    /**
     * 获取typeid,以及parent_id
     *
     * @param code           编号
     * @param prefix         前缀
     * @param oneLevelTypeId 分类id
     * @return typeId
     */
    List<EquipmentType> getTypeId(String code, Integer prefix, Long oneLevelTypeId);

    /**
     * 获取小件数据的分页
     *
     * @param page          分页
     * @param equipmentType 设备分类
     * @return 小件
     */
    IPage<EquipmentType> getPage(IPage<EquipmentType> page, EquipmentType equipmentType);


    /**
     * 获取小件数据
     *
     * @return 小件设备
     */
    EquipmentType getOtherInfo();

    /**
     * 刷新设备缓存
     */
    void refreshRedis();

    /**
     * 支持连接的设备信息
     *
     * @return 支持连接的设备信息
     */
    List<Dict> supportConnectionEquipList();

    /**
     * @param equipmentType   设备信息
     * @param meritEigenValue 电磁控特征值
     * @return com.mrk.yudong.core.model.ResDTO<com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType>
     * <AUTHOR>
     * @date 15:29 2022/6/27
     **/
    EquipmentType equipmentTypeChange(EquipmentType equipmentType, String meritEigenValue, String name);

    /**
     * 查询设备详情信息
     *
     * @param name           蓝牙名称
     * @param oneLevelTypeId 型号id
     * @return com.mrk.yudong.equipment.api.equipment.vo.EquipBindingVO
     * <AUTHOR>
     * @date 17:00 2022/9/14
     **/
    EquipBindingVO addEquipBindingVo(String name, String oneLevelTypeId);

    List<LiveMappingConvertDTO> listModelControlMapping(LiveMappingConvertVO liveMappingConvertVO);


    List<EquipmentType> getModel(String name, Long productId);
}
