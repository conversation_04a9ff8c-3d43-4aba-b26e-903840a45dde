package com.mrk.yudong.equipment.api.equipment.feign;

import com.alibaba.fastjson.JSON;
import com.merach.sun.device.api.DeviceApi;
import com.merach.sun.device.api.ProductModelApi;
import com.merach.sun.device.dto.qry.DeviceQry;
import com.merach.sun.device.dto.resp.device.DeviceDTO;
import com.mrk.yudong.core.annotation.Anonymous;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.share.constant.ResponseConstant;
import com.mrk.yudong.share.constant.equipment.EquipmentConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备绑定明细表 前端控制器
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/equipment-bind")
public class EquipmentBindFeign {

    private final ProductModelApi productModelApi;

    private final DeviceApi deviceApi;

    /**
     * 获取绑定的心率设备
     *
     * @param userId
     * @return
     */
    @Anonymous
    @GetMapping("/rate/bind")
    public ResDTO<Long> getBind(Long userId) {
        DeviceQry deviceQry = new DeviceQry();
        deviceQry.setUserId(userId);
        deviceQry.setProductId(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_100);
        DeviceDTO latestDevice = deviceApi.getLatestDevice(deviceQry);
        log.warn("=== 用户ID为：{}  绑定设备信息为：{} ===",
                 userId,
                 JSON.toJSONString(latestDevice));
        if (null == latestDevice) {
            return ResDTO.ok();
        }
        return ResDTO.ok(latestDevice.getDeviceId());
    }

    /**
     * 验证心率带设备ID
     *
     * @param id
     * @return
     */
    @Anonymous
    @GetMapping("/rate/check")
    public ResDTO checkRate(Long id) {
        if (id == null) {
            return ResDTO.fail(ResponseConstant.PARAM_ERROR);
        }
        boolean exist = productModelApi.modelExist(id);
        if (!exist) {
            return ResDTO.fail(ResponseConstant.UNABLE_LOCATE);
        }
        return ResDTO.ok();
    }
}
