package com.mrk.yudong.equipment.biz.equipment.service.impl;

import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentCategory;
import com.mrk.yudong.equipment.infrastructure.equipment.mapper.EquipmentCategoryMapper;
import com.mrk.yudong.equipment.biz.equipment.service.IEquipmentCategoryService;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 设备分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@Service
public class EquipmentCategoryServiceImpl extends BaseServiceImpl<EquipmentCategoryMapper, EquipmentCategory> implements IEquipmentCategoryService {

    /**
     * 根据类型获取设备分类
     *
     * @param categoryId
     * @param type
     * @return
     */
    @Override
    public EquipmentCategory getInfo(Long categoryId, Integer type) {
        return baseMapper.getInfo(categoryId, type);
    }

}
