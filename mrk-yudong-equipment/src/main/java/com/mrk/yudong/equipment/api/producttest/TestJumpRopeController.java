package com.mrk.yudong.equipment.api.producttest;


import com.mrk.yudong.core.annotation.Anonymous;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.equipment.api.producttest.dto.TestJumpRopeForm;
import com.mrk.yudong.equipment.biz.producttest.service.ITestJumpRopeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 *  产测跳绳 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/equipment/test-jump-rope")
public class TestJumpRopeController {

    private final ITestJumpRopeService testJumpRopeService;

    /**
     * 创建
     *
     * @param form 形式
     * @return {@link ResDTO}<{@link Boolean}>
     */
    @Anonymous
    @PostMapping("/create")
    public ResDTO<Boolean> create(@RequestBody @Valid TestJumpRopeForm form) {
        testJumpRopeService.create(form);
        return ResDTO.ok(true);
    }

}
