package com.mrk.yudong.equipment.biz.producttest.service;

import com.mrk.yudong.core.service.BaseService;
import com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType;
import com.mrk.yudong.equipment.infrastructure.producttest.model.TestEquipmentType;

import java.util.List;

/**
 * <p>
 * 设备类型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-23
 */
public interface ITestEquipmentTypeService extends BaseService<TestEquipmentType> {

    /**
     * 获取二级类型数量
     *
     * @return
     */
    List<EquipmentType> getTwoTypeNum();
}
