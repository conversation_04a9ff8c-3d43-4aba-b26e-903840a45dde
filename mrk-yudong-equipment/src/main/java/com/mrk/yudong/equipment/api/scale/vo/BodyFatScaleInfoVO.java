package com.mrk.yudong.equipment.api.scale.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description:
 * @author: ljx
 * @create: 2022/6/15 16:47
 * @Version 1.0
 **/
@Data
public class BodyFatScaleInfoVO {

    /**
     * 测量id
     **/
    private Long id;
    /**
     * 得分
     */
    private String score;

    /**
     * 重量（kg）
     */
    private String weight;

    /**
     * 重量趋势：0无状态，1上升，2下降,
     */
    private Integer weightTrend;

    /**
     * 重量趋势值
     */
    private String weightTrendValue;

    /**
     * 重量标准名称
     */
    private String weightStandardName;
    /**
     * bmi
     */
    private String bmi;

    /**
     * 身体年龄
     */
    private String bodyAge;

    /**
     * 测量时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime createTime;


    /**
     * 昵称
     */
    private String nickName;

    /**
     * 头像
     */
    private String avatar;


    /**
     * 数据状态 1正常2异常
     */
    private Integer status;

    /**
     * 电极类型 1-4电极，2-8电极
     */
    private Integer electrodeType;

    /**
     * 标准数组
     **/
    private List<StandardInfoVO> standardInfoList;


    /**
     * 八电极标准数组
     **/
    private List<StandardInfoVO> eightElectrodeStandards;
}
