package com.mrk.yudong.equipment.api.scale.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @description: 体脂秤变化趋势图
 * @author: ljx
 * @create: 2022/6/15 11:04
 * @Version 1.0
 **/
@Data
public class ScalesTrendsVO {
    /**
     * 开始时间
     **/
    @NotBlank(message = "请选择开始时间")
    private String startDate;
    /**
     * 结束时间
     **/
    @NotBlank(message = "请选择结束时间")
    private String endDate;
    /**
     * 体脂秤用户id
     **/
    private Long scaleUserId;
    /**
     * 查询类型
     **/
    @NotNull(message = "请选择查询类型")
    private Integer seachType;
}
