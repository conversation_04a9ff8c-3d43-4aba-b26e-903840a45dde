package com.mrk.yudong.equipment.biz.producttest.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.equipment.infrastructure.producttest.mapper.TestEquFirmwareVersionMapper;
import com.mrk.yudong.equipment.infrastructure.producttest.model.TestEquFirmwareVersion;
import com.mrk.yudong.equipment.biz.producttest.service.ITestEquFirmwareVersionService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 固件版本管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@Service
public class TestEquFirmwareVersionServiceImpl extends BaseServiceImpl<TestEquFirmwareVersionMapper, TestEquFirmwareVersion> implements ITestEquFirmwareVersionService {
    @Override
    public IPage<TestEquFirmwareVersion> getPage(IPage<TestEquFirmwareVersion> page, TestEquFirmwareVersion firmwareVersion) {
        return baseMapper.getPage(page,firmwareVersion.getEquipTypeId(),firmwareVersion.getOtaType());
    }

    @Override
    public TestEquFirmwareVersion getNewFirmwareVersion(String modelId, String code) {
        return baseMapper.getNewFirmwareVersion(modelId,code);
    }
}
