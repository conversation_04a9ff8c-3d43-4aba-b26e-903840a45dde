package com.mrk.yudong.equipment.biz.equipment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.core.service.BaseService;
import com.mrk.yudong.equipment.infrastructure.equipment.model.FirmwareVersion;

import java.util.List;

/**
 * <p>
 * 固件版本管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
public interface IFirmwareVersionService extends BaseService<FirmwareVersion> {

    /**
     * 分页信息
     * @param page 分页数据
     * @param firmwareVersion 查询信息
     * @return
     */
    IPage<FirmwareVersion> getPage(IPage<FirmwareVersion> page , FirmwareVersion firmwareVersion) ;

    /**
     * 获取所有历史版本
     * @param modelId 设备型号id
     * @return
     */
    List<FirmwareVersion> getHistoryFirmwareVersion( String modelId);


    /**
     * 获取最新固件
     * @param modelId 设备型号id
     * @param code 设备编号
     * @return
     */
    FirmwareVersion getNewFirmwareVersion(Long modelId, String code,String name);

    FirmwareVersion getDeviceModelFromwareVersion(Long devicerUserRelOriginId, String moduleNumber,String currentVersion, String name);
}
