package com.mrk.yudong.equipment.biz.equipment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.merach.sun.device.api.FirmwareApi;
import com.merach.sun.device.dto.qry.NewFirmwareVersionQry;
import com.merach.sun.device.dto.resp.firmware.AppNewFirmwareVersionDTO;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.equipment.infrastructure.equipment.mapper.FirmwareVersionMapper;
import com.mrk.yudong.equipment.infrastructure.equipment.model.FirmwareVersion;
import com.mrk.yudong.equipment.biz.equipment.service.IFirmwareVersionService;
import com.mrk.yudong.equipment.utils.VersionCompare;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 固件版本管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FirmwareVersionServiceImpl extends BaseServiceImpl<FirmwareVersionMapper, FirmwareVersion> implements IFirmwareVersionService {
    private final FirmwareApi firmwareApi;

    @Override
    public IPage<FirmwareVersion> getPage(IPage<FirmwareVersion> page, FirmwareVersion firmwareVersion) {
        Integer isTra = SessionUtil.getIsTra();
        return baseMapper.getPage(page, firmwareVersion.getEquipTypeId(), firmwareVersion.getOtaType(), isTra);
    }

    @Override
    public List<FirmwareVersion> getHistoryFirmwareVersion(String modelId) {
        List<FirmwareVersion> list = null;
        if (StrUtil.isNotBlank(modelId)) {
            BaseQuery<FirmwareVersion> baseQuery = new BaseQuery<>();
            baseQuery.eq("equip_model_id", modelId);
            Integer isTra = SessionUtil.getIsTra();
            if (isTra.equals(0)) {
                baseQuery.eq("is_tra", 0);
            }
            baseQuery.orderByDesc("update_time");
            list = list(baseQuery);
        }
        return list;
    }

    @Override
    public FirmwareVersion getNewFirmwareVersion(Long modelId, String code, String name) {
        //查询出所有的版本信息
        Integer isTra = SessionUtil.getIsTra();
        List<FirmwareVersion> firmwareVersionList = baseMapper.getNewFirmwareVersion(modelId, code, isTra);
        if (CollUtil.isEmpty(firmwareVersionList) && StrUtil.isNotEmpty(name)) {
            code = StrUtil.split(name, "-").get(0);
            firmwareVersionList = baseMapper.getNewFirmwareVersion(modelId, code, isTra);
        }
        FirmwareVersion newFirmwareVersion = null;
        String maxVersion = null;
        boolean isForceUpdate = false;
        for (int i = 0; i < firmwareVersionList.size(); i++) {
            String version = firmwareVersionList.get(i).getVersion();
            if (i == 0) {
                maxVersion = firmwareVersionList.get(0).getVersion();
            }

            //判断版本是否是最新版本
            int compareVersion;
            if (version.contains(".")) {
                String[] vs = version.split("V");
                String v;
                if (vs.length > 1) {
                    v = vs[1];
                } else {
                    v = version;
                }
                compareVersion = VersionCompare.compareVersion(maxVersion, v);
            } else if (version.length() == 2) {
                //0C
                compareVersion = version.compareTo(maxVersion);
            } else {
                //009500
                Integer userVersion = Integer.valueOf(version);
                Integer dateVersion = Integer.valueOf(maxVersion);
                compareVersion = userVersion.compareTo(dateVersion);
            }
            if (compareVersion > 0) {
                maxVersion = firmwareVersionList.get(i).getVersion();
                if (!isForceUpdate) {
                    isForceUpdate = firmwareVersionList.get(i).getUpdateType().equals(BaseConstant.APP_VERSION_TYPE_FORCE_UPDATE);
                }
            } else {
                maxVersion = version;
            }
        }
        String finalMaxVersion = maxVersion;
        List<FirmwareVersion> firmwareVersions = firmwareVersionList.stream().filter(v -> v.getVersion().equals(finalMaxVersion)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(firmwareVersions)) {
            newFirmwareVersion = firmwareVersions.get(0);
            if (isForceUpdate) {
                newFirmwareVersion.setUpdateType(BaseConstant.APP_VERSION_TYPE_FORCE_UPDATE);
            }
        }

        return newFirmwareVersion;
    }

    @Override
    public FirmwareVersion getDeviceModelFromwareVersion(Long devicerUserRelOriginId, String moduleNumber, String currentVersion, String name) {
        if (devicerUserRelOriginId == null || StrUtil.isBlank(moduleNumber) || StrUtil.isBlank(currentVersion)) {
            FirmwareVersion firmwareVersion = new FirmwareVersion();
            firmwareVersion.setIsLastVersion(BaseConstant.INT_TRUE);
            return firmwareVersion;
        }

        NewFirmwareVersionQry newFirmwareVersionQry = new NewFirmwareVersionQry();
        newFirmwareVersionQry.setCurrentVersion(currentVersion);
        newFirmwareVersionQry.setUserId(SessionUtil.getId());
        newFirmwareVersionQry.setModuleNumber(moduleNumber);
        newFirmwareVersionQry.setDevicerUserRelOriginId(devicerUserRelOriginId);
        log.info("getDeviceModelFromwareVersion->newFirmwareVersionQry:{}", newFirmwareVersionQry);
        AppNewFirmwareVersionDTO newFirmwareVersion;
        try{
            newFirmwareVersion = firmwareApi.getNewFirmwareVersion(newFirmwareVersionQry);
        }catch (Exception e){
            log.warn("用户:{},更新软件失败:{}",SessionUtil.getId(),e.toString());
            FirmwareVersion firmwareVersion = new FirmwareVersion();
            firmwareVersion.setIsPop(BaseConstant.INT_FALSE);
            firmwareVersion.setIsLastVersion(BaseConstant.INT_TRUE);
            return firmwareVersion;
        }


        //体脂称设备使用name字段作为code
        if (StrUtil.isNotBlank(name) && newFirmwareVersion.getIsLastVersion().equals(BaseConstant.INT_FALSE) && StrUtil.isBlank(newFirmwareVersion.getDownloadLink())) {
            log.info("使用name：{}查询数据 ", name);
            newFirmwareVersionQry.setModuleNumber(StrUtil.split(name, "-").get(0));
            newFirmwareVersion = firmwareApi.getNewFirmwareVersion(newFirmwareVersionQry);
        }

        return convertFirmwareVersion(newFirmwareVersion);
    }

    private FirmwareVersion convertFirmwareVersion(AppNewFirmwareVersionDTO newFirmwareVersion) {
        FirmwareVersion firmwareVersion = BeanUtil.copyProperties(newFirmwareVersion, FirmwareVersion.class);
        firmwareVersion.setVersionAddress(newFirmwareVersion.getDownloadLink());
        firmwareVersion.setVersion(newFirmwareVersion.getVersionNumber());
        firmwareVersion.setCode(newFirmwareVersion.getModuleNumber());
        firmwareVersion.setOtaType(newFirmwareVersion.getOtaProtocol());
        firmwareVersion.setEquipTypeId(newFirmwareVersion.getProductId());
        firmwareVersion.setEquipModelId(newFirmwareVersion.getProductModelId());
        return firmwareVersion;
    }

}
