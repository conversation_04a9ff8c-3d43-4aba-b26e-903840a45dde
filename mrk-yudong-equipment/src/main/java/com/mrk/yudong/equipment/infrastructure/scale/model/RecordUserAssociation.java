package com.mrk.yudong.equipment.infrastructure.scale.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 体脂秤用户与记录关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("equ_record_user_association")
public class RecordUserAssociation extends Model<RecordUserAssociation> {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 体脂秤用户id
     */
    private Long scaleUserId;

    /**
     *测量id
     */
    private Long measureId;

    /**
     *数据状态 1正常2异常
     */
    private Integer status;


    /**
     *消息状态 1.发送成功 2.消费成功 3.消费失败
     */
    private Integer messageStatus;

    /**
     * 1.自动，2 手动
     */
    private Integer insertType;
    /**
     *结论文案id
     */
    private Integer conclusionId;

    /**
     *json串
     */
    private String remark;
    /**
     * 型号ID
     */
    private Long modelId;

    /**
     * 电极类型 1-4电极，2-8电极
     */
    private Integer electrodeType;

    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    private Integer updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    private Integer isDelete;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
