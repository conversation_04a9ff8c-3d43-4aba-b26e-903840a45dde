package com.mrk.yudong.equipment.api.scale.vo;

import lombok.Data;

/**
 * @description: 历史体重
 * @author: ljx
 * @create: 2022/6/21 17:59
 * @Version 1.0
 **/
@Data
public class HistoricalWeightVO {
    /**
     * 用户测量关联id
     */
    private  Long id;

    /**
     * 用户测量id
     */
    private  Long measureId;

    /**
     * 1.自动，2 手动
     */
    private Integer insertType;

    /**
     * 体重
     */
    private  String weight;

    /**
     * bmi
     */
    private  String bmi;

    /**
     * 体脂率
     */
    private String bodyFatRate;

    /**
     * 时间戳
     */
    private  Long dateTime;

    /**
     * 电极信息，1:4电极，2:8电极
     */
    private Integer electrodeType;


}
