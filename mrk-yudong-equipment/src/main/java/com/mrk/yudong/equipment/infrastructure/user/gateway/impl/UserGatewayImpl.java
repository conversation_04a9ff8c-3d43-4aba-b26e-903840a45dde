package com.mrk.yudong.equipment.infrastructure.user.gateway.impl;

import com.merach.sun.user.api.AccountApi;
import com.merach.sun.user.api.UserApi;
import com.merach.sun.user.api.UserHealthApi;
import com.merach.sun.user.api.UserHealthRecordApi;
import com.merach.sun.user.dto.cmd.account.AccountUpdateCmd;
import com.merach.sun.user.dto.qry.user.UserQry;
import com.merach.sun.user.dto.user.LatestHealthRecordDTO;
import com.merach.sun.user.dto.user.UserInfoDTO;
import com.mrk.yudong.equipment.infrastructure.user.gateway.UserGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Slf4j
@RequiredArgsConstructor
public class UserGatewayImpl implements UserGateway {

    private final UserApi userApi;
    private final AccountApi accountApi;
    private final UserHealthApi userHealthApi;
    @Override
    public UserInfoDTO getUserInfoByUserId(Long userId) {
        if (Objects.isNull(userId) || userId <= 0L) {
            return null;
        }
        UserQry userQry = new UserQry();
        userQry.setAccountId(userId);
        return userApi.getUserInfo(userQry);
    }

    @Override
    public Boolean updateAccount(AccountUpdateCmd accountUpdateCmd) {
        if (Objects.isNull(accountUpdateCmd)) {
            return false;
        }
        return accountApi.updateAccount(accountUpdateCmd);
    }

    @Override
    public LatestHealthRecordDTO getLatestData(Long userId) {
        return userHealthApi.getLatestData(userId);
    }

}
