package com.mrk.yudong.equipment.api.equipment.dto;

import lombok.Data;

/**
 * @description: 协议设备
 * @author: ljx
 * @create: 2022/6/27 15:01
 * @Version 1.0
 **/
@Data
public class AgreementEquipDTO {
    /**
     * 协议特征值
     */
    private String agreementEigenValue;
    /**
     * 是否电磁控特征值
     */
    private String meritEigenValue;
    /**
     * 是否电磁控
     */
    private Integer isElectromagneticControl;
    /**
     * 阻力调节
     */
    private Integer showResistance;
    /**
     * 坡度调节
     */
    private Integer showSlope;
    /**
     * 协议类型
     */
    private Integer communicationProtocol;

    /**
     * ota类型
     */
    private Integer otaType;
}
