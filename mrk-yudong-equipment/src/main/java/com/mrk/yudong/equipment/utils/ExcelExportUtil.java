package com.mrk.yudong.equipment.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.URLUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * 导出工具类
 */
public class ExcelExportUtil {

    public static void export(HttpServletResponse response, String filename, List<String> cellName,List<String> cellValue, List<Map<String, Object>> list) {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        SXSSFSheet sheet = workbook.createSheet(filename);
        sheet.setDefaultRowHeight((short) (3 * 256));

        int rowIndex = 0;
        CellStyle titleCellStyle = buildCellStyle(workbook, true);
        SXSSFRow titleRow = sheet.createRow(rowIndex);
        for (int i = 0; i < cellName.size(); i++) {
            sheet.setColumnWidth(i, 25 * 256);
            SXSSFCell cell = titleRow.createCell(i);
            cell.setCellStyle(titleCellStyle);
            cell.setCellValue(cellName.get(i));
        }

        if (CollUtil.isNotEmpty(list)) {
            CellStyle cellStyle = buildCellStyle(workbook, false);
            for (Map<String, Object> data : list) {
                rowIndex++;
                SXSSFRow sxssfRow = sheet.createRow(rowIndex);

                for (int i = 0; i < cellValue.size(); i++) {
                    SXSSFCell cell = sxssfRow.createCell(i);
                    String value = MapUtil.getStr(data, cellValue.get(i), "");
                    cell.setCellStyle(cellStyle);
                    cell.setCellValue(value);
                }
            }
        }

        filename = URLUtil.encode(filename + ".xlsx");
        response.addHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + filename);
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);

        try {
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            workbook.close();
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static CellStyle buildCellStyle(SXSSFWorkbook workbook, boolean bold) {
        Font font = workbook.createFont();
        font.setFontName("隶书");
        font.setBold(bold);

        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setFont(font);

        cellStyle.setFillForegroundColor(IndexedColors.TAN.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        cellStyle.setBorderTop(BorderStyle.MEDIUM);
        cellStyle.setBorderRight(BorderStyle.MEDIUM);
        cellStyle.setBorderBottom(BorderStyle.MEDIUM);
        cellStyle.setBorderLeft(BorderStyle.MEDIUM);

        return cellStyle;
    }

}
