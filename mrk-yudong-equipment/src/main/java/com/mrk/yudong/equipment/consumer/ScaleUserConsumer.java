package com.mrk.yudong.equipment.consumer;

import cn.hutool.core.util.StrUtil;
import com.mrk.yudong.equipment.biz.scale.service.IScaleUserService;
import com.mrk.yudong.equipment.consumer.message.ScaleUserMessage;
import com.mrk.yudong.equipment.infrastructure.scale.model.ScaleUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.Objects;

@Service
@Slf4j
@RequiredArgsConstructor
@RocketMQMessageListener(topic = "${rocketmq.mrk-sun-user.topic}", consumerGroup = "${rocketmq.mrk-sun-user.group}", selectorExpression = "update_scale_user")
public class ScaleUserConsumer implements RocketMQListener<ScaleUserMessage> {
    private final IScaleUserService scaleUserService;

    @Override
    public void onMessage(ScaleUserMessage scaleUserMessage) {
        log.info("同步体脂秤主账号信息：{}", scaleUserMessage);
        try {
            if (paramsCheck(scaleUserMessage)) {
                return;
            }
            ScaleUser scaleUser = new ScaleUser();
            scaleUser.setUserId(scaleUserMessage.getAccountId());
            scaleUser.setNickName(scaleUserMessage.getNickname());
            scaleUser.setAvatar(scaleUserMessage.getAvatar());
            scaleUser.setSex(scaleUserMessage.getSex() == 0 ? null : scaleUserMessage.getSex());
            if (Objects.nonNull(scaleUserMessage.getBirthday())) {
                scaleUser.setBirthday(scaleUserMessage.getBirthday().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
            if (Objects.nonNull(scaleUserMessage.getHeight())) {
                scaleUser.setHeight(scaleUserMessage.getHeight().doubleValue());
            }
            Boolean updateMainScaleUser = scaleUserService.updateMainScaleUser(scaleUser);
            if (!updateMainScaleUser) {
                log.info("同步体脂秤主账号信息失败：{}", scaleUser);
            }
        } catch (Exception e) {
            log.info("同步体脂秤主账号信息失败：{}", e.getMessage());
        }

    }

    private static boolean paramsCheck(ScaleUserMessage scaleUserMessage) {
        return (StrUtil.isBlank(scaleUserMessage.getAvatar())
                && StrUtil.isBlank(scaleUserMessage.getNickname())
                && Objects.nonNull(scaleUserMessage.getBirthday())
                && Objects.nonNull(scaleUserMessage.getSex())
        )
                || Objects.isNull(scaleUserMessage.getAccountId());
    }
}
