package com.mrk.yudong.equipment.biz.heart.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.merach.sun.user.api.UserApi;
import com.merach.sun.user.dto.user.UserInfoDTO;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.equipment.api.heart.dto.HeartRateWarningFrom;
import com.mrk.yudong.equipment.biz.heart.constant.HeartRateWarningConstant;
import com.mrk.yudong.equipment.biz.heart.service.IHeartRateWarningService;
import com.mrk.yudong.equipment.infrastructure.heart.mapper.HeartRateWarningMapper;
import com.mrk.yudong.equipment.infrastructure.heart.model.HeartRateWarning;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

/**
 * <p>
 * 心率预警 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-03
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HeartRateWarningServiceImpl extends BaseServiceImpl<HeartRateWarningMapper, HeartRateWarning> implements IHeartRateWarningService {

    private final UserApi userApi;

    @Override
    public HeartRateWarning getHeartRateWarning() {
        HeartRateWarning heartRateWarning = getRateWarning();
        if (heartRateWarning != null) {
            return heartRateWarning;
        }

        //无数据则生成一条关闭的未弹窗数据
        insertHeartRateWarning();
        return getRateWarning();
    }

    @Override
    public Integer adjustHeartRateWarning(HeartRateWarningFrom heartRateWarningFrom) {
        HeartRateWarning heartRateWarning = new HeartRateWarning();
        if (Objects.equals(heartRateWarningFrom.getIsOpen(), BaseConstant.INT_TRUE)) {
            heartRateWarning.setIsOpen(BaseConstant.INT_TRUE);
            if (isSetThreshold(heartRateWarningFrom)) {
                heartRateWarning.setThreshold(heartRateWarningFrom.getThreshold());
            }
        } else {
            heartRateWarning.setIsOpen(BaseConstant.INT_FALSE);
        }
        boolean extis = baseMapper.selectCount(new LambdaQueryWrapper<HeartRateWarning>()
                .eq(HeartRateWarning::getUserId, SessionUtil.getId())
                .eq(HeartRateWarning::getIsDelete, BaseConstant.INT_FALSE)) > 0;
        if (extis) {
            updateHeartRateWarning(heartRateWarning);
        } else {
            insertHeartRateWarning();
        }


        return getRateWarning().getThreshold();
    }

    @Override
    public Boolean heartRateWarningPopup() {
        HeartRateWarning heartRateWarning = new HeartRateWarning();
        heartRateWarning.setIsPopup(BaseConstant.INT_TRUE);
        return updateHeartRateWarning(heartRateWarning);
    }

    private boolean updateHeartRateWarning(HeartRateWarning heartRateWarning) {
        LambdaQueryWrapper<HeartRateWarning> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(HeartRateWarning::getUserId, SessionUtil.getId())
                .eq(HeartRateWarning::getIsDelete, BaseConstant.INT_FALSE);

        return baseMapper.update(heartRateWarning, lambdaQueryWrapper) > 0;
    }

    private HeartRateWarning getRateWarning() {
        LambdaQueryWrapper<HeartRateWarning> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(HeartRateWarning::getUserId, SessionUtil.getId())
                .eq(HeartRateWarning::getIsDelete, BaseConstant.INT_FALSE)
                .last("LIMIT 1");
        return baseMapper.selectOne(lambdaQueryWrapper);
    }

    private void insertHeartRateWarning() {
        HeartRateWarning heartRateWarning = new HeartRateWarning();
        heartRateWarning.setUserId(SessionUtil.getId());
        heartRateWarning.setIsOpen(BaseConstant.INT_FALSE);
        heartRateWarning.setIsPopup(BaseConstant.INT_FALSE);
        heartRateWarning.setThreshold(BigDecimal.valueOf((HeartRateWarningConstant.MAX_HEART_RATE - getUserAge(SessionUtil.getId())) * HeartRateWarningConstant.WARNING_COEFFICIENT).intValue());
        if (heartRateWarning.getThreshold() < HeartRateWarningConstant.MIN_WARNING) {
            heartRateWarning.setThreshold(HeartRateWarningConstant.MIN_WARNING);
        }
        baseMapper.insert(heartRateWarning);
    }

    private boolean isSetThreshold(HeartRateWarningFrom heartRateWarningFrom) {
        return null != heartRateWarningFrom.getThreshold() && !Objects.equals(heartRateWarningFrom.getThreshold(), BaseConstant.INT_FALSE)
                && heartRateWarningFrom.getThreshold() >= HeartRateWarningConstant.MIN_WARNING && heartRateWarningFrom.getThreshold() <= HeartRateWarningConstant.MAX_WARNING;
    }

    private Integer getUserAge(Long userId) {
        UserInfoDTO userInfoDTO = userApi.userInfo(userId);
        if (!Objects.isNull(userInfoDTO) && !Objects.isNull(userInfoDTO.getBirthday())) {
            return (int) userInfoDTO.getBirthday().until(LocalDate.now(), ChronoUnit.YEARS);
        }
        log.info("未查询到用户：{}年龄", userId);
        //默认30岁
        return HeartRateWarningConstant.DEFAULT_AGE;
    }
}
