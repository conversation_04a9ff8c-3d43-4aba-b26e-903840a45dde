package com.mrk.yudong.equipment.api.heart.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: ljx
 * @create: 2023/1/3 13:08
 * @Version 1.0
 **/
@Data
public class HeartRateWarningFrom {

    /**
     * 是否开启状态 0关闭，1开启
     */
    @NotNull(message = "开启状态不能为空")
    @Min(0)
    @Max(1)
    private Integer isOpen;

    /**
     * 预警值
     */
    private Integer threshold;
}
