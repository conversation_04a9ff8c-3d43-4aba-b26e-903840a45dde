package com.mrk.yudong;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 重启1
 */
@ConfigurationPropertiesScan("com.mrk.yudong.**.properties")
@EnableDiscoveryClient
@EnableFeignClients(basePackages = { "com.mrk.yudong.equipment.feign","com.merach.sun.user.api","com.merach.sun.device.api","com.merach.sun.data.api"})
@SpringBootApplication
@EnableScheduling
public class EquipmentApplication {

    public static void main(String[] args) {

        SpringApplication.run(EquipmentApplication.class, args);
    }

}
