<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.equipment.infrastructure.scale.mapper.RecordUserAssociationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.equipment.infrastructure.scale.model.RecordUserAssociation">
        <id column="id" property="id"/>
        <result column="scale_user_id" property="scaleUserId"/>
        <result column="measure_id" property="measureId"/>
        <result column="status" property="status"/>
        <result column="conclusion_id" property="conclusionId"/>
        <result column="remark" property="remark"/>
        <result column="create_id" property="createId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>
    <select id="lastScaleDate" resultType="com.mrk.yudong.equipment.infrastructure.scale.model.RecordUserAssociation">
        select *
        from equ_record_user_association
        WHERE scale_user_id = #{scaleUserId}
        <if test="isContainManualReport != null and isContainManualReport ==1 ">
            and insert_type != 2
        </if>
        and is_delete = 0 and message_status = 2 order by create_time desc limit 1
    </select>

</mapper>
