<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.equipment.infrastructure.equipment.mapper.EquipmentBindMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentBind">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="mac" property="mac" />
        <result column="user_id" property="userId" />
        <result column="category_id" property="categoryId" />
        <result column="equipment_id" property="equipmentId" />
        <result column="bind_status" property="bindStatus" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 根据用户ID和设备分类ID获取最新的绑定信息 -->
    <select id="getTopInfo" resultMap="BaseResultMap">
        SELECT * FROM equipment_bind WHERE user_id = #{userId} AND category_id = #{categoryId} AND bind_status = #{bindStatus} ORDER BY IFNULL(create_time, update_time) DESC LIMIT 1
    </select>

</mapper>
