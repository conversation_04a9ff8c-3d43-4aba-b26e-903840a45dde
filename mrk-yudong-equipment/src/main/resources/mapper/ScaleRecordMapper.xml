<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.equipment.infrastructure.scale.mapper.ScaleRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.equipment.infrastructure.scale.model.ScaleRecord">
        <id column="id" property="id" />
        <result column="measure_id" property="measureId" />
        <result column="health_attributes_id" property="healthAttributesId" />
        <result column="health_attributes_value" property="healthAttributesValue" />
        <result column="standard_status" property="standardStatus" />
        <result column="remark" property="remark" />
        <result column="create_id" property="createId" />
        <result column="create_time" property="createTime" />
        <result column="update_id" property="updateId" />
        <result column="update_time" property="updateTime" />
        <result column="is_delete" property="isDelete" />
    </resultMap>
    <select id="minWeightRecordList" resultType="com.mrk.yudong.equipment.infrastructure.scale.model.ScaleRecord">
        SELECT * from (
              SELECT
                  ROW_NUMBER() over(PARTITION BY DATE(create_time) ORDER BY health_attributes_value+0 ) ns,
                  health_attributes_id,
                  health_attributes_value,
                  create_time
              FROM
                  equ_scale_record
              WHERE
                  is_delete = 0
                AND (
                      measure_id IN
                      <foreach collection="measureIdList" close=")" open="(" separator="," item="item">
                          #{item}
                      </foreach>
                  AND health_attributes_id = #{seachType} and CAST(health_attributes_value AS FLOAT)>0 ) ) a where a.ns =1 ORDER BY create_time
    </select>

</mapper>
