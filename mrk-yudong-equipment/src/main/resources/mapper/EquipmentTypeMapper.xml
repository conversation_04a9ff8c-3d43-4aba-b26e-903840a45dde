<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.equipment.infrastructure.equipment.mapper.EquipmentTypeMapper">

    <!-- 通用查询映射结果1 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType">
        <id column="id" property="id" />
        <result column="type_name" property="typeName" />
        <result column="type_images" property="typeImages" />
        <result column="icon_images" property="iconImages" />
        <result column="tag_icon" property="tagIcon" />
        <result column="type_version" property="typeVersion" />
        <result column="max_electromagnetic_control" property="maxElectromagneticControl" />
        <result column="product_type" property="productType" />
        <result column="product_manual" property="productManual" />
        <result column="help_center" property="helpCenter" />
        <result column="show_resistance" property="showResistance" />
        <result column="show_speed" property="showSpeed" />
        <result column="show_slope" property="showSlope" />
        <result column="show_gear" property="showGear" />
        <result column="sort" property="sort" />
        <result column="is_delete" property="isDelete" />
        <result column="level" property="level" />
        <result column="parent_id" property="parentId" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="typeNum" property="typeNum"/>
    </resultMap>
    <!--计数-->
    <resultMap id="countResultMap" type="com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="typeNum" property="typeNum"/>
    </resultMap>

    <select id="getTwoTypeNum" resultMap="countResultMap">
        SELECT
            COUNT( id ) as typeNum,parent_id
        FROM
            equ_equipment_type
        WHERE
            is_delete = 0

        <if test="isTra == null or isTra==0">
            and is_tra = 0
        </if>
        GROUP BY parent_id
    </select>

    <select id="getPage" resultType="com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType" parameterType="com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType">
        select * from equ_equipment_type
        where is_delete = 0
        <if test="equipmentType.parentId != null">
           and parent_id = #{equipmentType.parentId}
        </if>
        <if test="equipmentType.level != null">
            and level = #{equipmentType.level}
        </if>
        <if test="equipmentType.isTra != null">
            and is_tra &lt;= #{equipmentType.isTra}
        </if>
        <if test="equipmentType.typeName != null and equipmentType.typeName != ''">
            and type_name = #{equipmentType.typeName}
        </if>
        <if test="equipmentType.productType != null and equipmentType.productType != ''">
            and product_type = #{equipmentType.productType}
        </if>
        <if test="equipmentType.type != null">
            and type = #{equipmentType.type}
        </if>
        <if test="equipmentType.code != null">
            and code = #{equipmentType.code}
        </if>
        <if test="equipmentType.level != null">
            <if test="equipmentType.level == '' ">
                <if test="equipmentType.type == 1">
                    or id = 3 or is_show = 1
                </if>
                ORDER BY sort
            </if>
            <if test="equipmentType.level != ''">
                ORDER BY update_time desc
            </if>
        </if>
    </select>
    <select id="getOtherInfo" resultType="com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType">
        select * from equ_equipment_type
        where id = 3
    </select>
</mapper>
