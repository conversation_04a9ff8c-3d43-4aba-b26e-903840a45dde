<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.equipment.infrastructure.producttest.mapper.TestEquipmentTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.equipment.infrastructure.producttest.model.TestEquipmentType">
        <id column="id" property="id" />
        <result column="prefix_id" property="prefixId" />
        <result column="code" property="code" />
        <result column="type_name" property="typeName" />
        <result column="level" property="level" />
        <result column="parent_id" property="parentId" />
        <result column="type_images" property="typeImages" />
        <result column="type_version_id" property="typeVersionId" />
        <result column="instrument_type" property="instrumentType" />
        <result column="sort" property="sort" />
        <result column="is_delete" property="isDelete" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
    </resultMap>
    <!--计数-->
    <resultMap id="countResultMap" type="com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentType">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="typeNum" property="typeNum"/>
    </resultMap>
    <select id="getTwoTypeNum" resultMap="countResultMap">
        SELECT
            COUNT( id ) as typeNum,parent_id
        FROM
            test_equipment_type
        WHERE
        is_delete = 0
        GROUP BY parent_id
    </select>
</mapper>
