<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.equipment.infrastructure.equipment.mapper.EquipmentCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentCategory">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="name" property="name" />
        <result column="cover" property="cover" />
        <result column="icon" property="icon" />
        <result column="status" property="status" />
        <result column="create_id" property="createId" />
        <result column="create_time" property="createTime" />
        <result column="update_id" property="updateId" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 根据类型获取设备分类 -->
    <select id="getInfo" resultMap="BaseResultMap">
        SELECT ec.*, ( SELECT `name` FROM equip_dict WHERE dict_key = 'EQUIPMENT_CATEGORY_TYPE' AND `value` = ec.type ) name FROM equipment_category ec WHERE ec.`status` = 1
        <if test="categoryId != null">
            AND ec.id = #{categoryId}
        </if>
        <if test="type != null">
            AND ec.type = #{type}
        </if>
    </select>

</mapper>
