<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.equipment.infrastructure.equipment.mapper.EquipmentInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="one_level_type_id" property="oneLevelTypeId" />
        <result column="two_level_type_id" property="twoLevelTypeId" />
        <result column="bind_status" property="bindStatus" />
        <result column="is_delete" property="isDelete" />
        <result column="user_by" property="userBy" />
        <result column="user_version" property="userVersion" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="bind_time" property="bindTime" />
        <result column="oneLevelTypeName" property="oneLevelTypeName" />
        <result column="userCount" property="userCount" />
    </resultMap>

    <select id="getHomePage" resultType="com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo">
        SELECT id,
               one_level_type_id,
               two_level_type_id,
               NAME,
               update_time,
               bind_time,
               create_time,
               connect_time,
               (select type_images from equ_equipment_type where id = two_level_type_id) modelImages
        FROM equ_equipment_info
        WHERE id in (select id
                     FROM (SELECT rank() over (partition by one_level_type_id order by bind_time DESC) ranks,id
                           FROM equ_equipment_info
                           WHERE user_by = #{userId}
                             AND bind_status = 1
                             AND is_delete = 0) a
                     where a.ranks = 1)
        ORDER BY bind_time DESC
    </select>

    <select id="connectSort" resultType="com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo">
        SELECT
            i.code,i.name,i.connect_time,i.bind_time,i.one_level_type_id,t.type_images twoLevelImage
        FROM
            equ_equipment_info i
                LEFT JOIN equ_equipment_type t on   i.two_level_type_id=t.id
        WHERE
            i.is_delete = 0
          AND i.user_by =  #{userId}
          AND i.bind_status = 1
    </select>

    <delete id="physicsDeleteById" >
        DELETE from  equ_equipment_info where  id= #{id}
    </delete>


    <select id="getByConditions" resultType="com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo">
        select * from equ_equipment_info where is_delete = 0
            and user_by = #{userId}
            <if test ="productId != null and productId != ''">
                and one_level_type_id = #{productId}
            </if>
            <if test ="productId != null and productId != ''">
                and two_level_type_id = #{modelId}
            </if>
            <if test ="bluetoothName != null and bluetoothName != ''">
                and name = #{bluetoothName}
            </if>
        order by connect_time limit 1;
    </select>

    <select id="getNewestBind" resultType="com.mrk.yudong.equipment.infrastructure.equipment.model.EquipmentInfo">
        SELECT * FROM equ_equipment_info WHERE is_delete = 0
        AND bind_status = 1
        AND one_level_type_id IN (1,2,5,6)
        AND user_by = #{userId}
        ORDER BY bind_time DESC LIMIT 1
    </select>
</mapper>
