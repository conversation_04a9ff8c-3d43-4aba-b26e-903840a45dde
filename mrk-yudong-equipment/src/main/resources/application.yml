logging:
  config: classpath:logback-spring.xml
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - traceId:%X{EagleEye-TraceID} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - traceId:%X{EagleEye-TraceID} - %msg%n"

server:
  port: 7002


spring:
  application:
    name: yudong-equipment
  messages:
    basename: messages/msg
  main:
    allow-bean-definition-overriding: true
  redis:
    timeout: 3s
    ssl: false
    jedis:
      pool:
        enabled: true
        min-idle: 50
        time-between-eviction-runs: 5s
        max-active: 200
        max-idle: 200
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      pool-name: MyHikariCP
      auto-commit: true
      minimum-idle: 5
      maximum-pool-size: 10
      connection-timeout: 5000
      idle-timeout: 600000
      max-lifetime: 1800000
      keepalive-time: 300000
      leak-detection-threshold: 60000
  cloud:
    nacos:
      discovery:
        failure-tolerance-enabled: true


mrk:
  base:
    enable-auth: true
    country: china


mybatis-plus:
  configuration:
    cache-enabled: false
    map-underscore-to-camel-case: true
    call-setters-on-nulls: true
  mapper-locations:
    - classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.mrk.yudong.**.model
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: isDelete
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)


feign:
  client:
    config:
      default:
        connect-timeout: 15000
        read-timeout: 15000
        logger-level: NONE
    refresh-enabled: true
  httpclient:
    enabled: false
    max-connections: 500
    max-connections-per-route: 125
    connection-timeout: 15000
    ok-http:
      read-timeout: 15s
  okhttp:
    enabled: true
  circuitbreaker:
    enabled: false
  compression:
    request:
      enabled: true
    response:
      enabled: true


resilience4j:
  circuitbreaker:
    configs:
      default:
        ignoreExceptions:
          - com.merach.sun.common.lang.exception.BusinessException
  timelimiter:
    configs:
      default:
        timeoutDuration: 15s
        cancelRunningFuture: true


management:
  endpoint:
    health:
      probes:
        enabled: true
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
