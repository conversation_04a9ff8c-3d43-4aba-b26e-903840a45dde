<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>mrk-yudong-parent</artifactId>
        <groupId>com.mrk.yudong</groupId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mrk-yudong-equipment</artifactId>

    <properties>
        <poi.version>5.0.0</poi.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mrk.yudong</groupId>
            <artifactId>mrk-yudong-core</artifactId>
            <version>${com.mrk.yudong.version}</version>
        </dependency>

        <dependency>
            <groupId>com.merach.sun</groupId>
            <artifactId>device-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.merach.sun</groupId>
            <artifactId>user-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>

        <dependency>
            <groupId>com.merach.sun</groupId>
            <artifactId>data-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun.schedulerx</groupId>
            <artifactId>schedulerx2-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>QLExpress</artifactId>
            <version>3.3.1</version>
        </dependency>

        <dependency>
            <groupId>com.merach.sun</groupId>
            <artifactId>merach-sun-rocketmq-starter</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>