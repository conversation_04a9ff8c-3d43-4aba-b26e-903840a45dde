package com.mrk.yudong;

import com.mrk.yudong.admin.rabbit.IRabbitOutput;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.stream.annotation.EnableBinding;

@EnableBinding(value = {IRabbitOutput.class})
@ConfigurationPropertiesScan("com.mrk.yudong.**.properties")
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.merach.sun.**.api", "com.mrk.yudong.**.feign"})
@SpringBootApplication
public class AdminApplication {
    public static void main(String[] args) {
        SpringApplication.run(AdminApplication.class, args);
    }

}
