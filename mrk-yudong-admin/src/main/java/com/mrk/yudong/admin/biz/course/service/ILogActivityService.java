package com.mrk.yudong.admin.biz.course.service;

import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.admin.infrastructure.course.model.LogActivity;
import com.mrk.yudong.core.service.BaseService;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 活动数据记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface ILogActivityService extends BaseService<LogActivity> {

    /**
     * 获取活动PV总统计数据
     *
     * @param activityId
     * @param beginDate
     * @param endDate
     * @return
     */
    JSONObject getActivitySumPV(Long activityId, LocalDate beginDate, LocalDate endDate);

    /**
     * 获取活动PV总统计数据
     *
     * @param activityId
     * @param beginDate
     * @param endDate
     * @return
     */
    JSONObject getActivitySumUV(Long activityId, LocalDate beginDate, LocalDate endDate);
    /**
     * 获取列表分类列表
     *
     * @param activityId
     * @param beginDate
     * @param endDate
     * @return
     */
    List<JSONObject> getPVList(Long activityId, LocalDate beginDate, LocalDate endDate);

}
