package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.course.model.LogActivity;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 活动数据记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface LogActivityMapper extends BaseMapper<LogActivity> {

    /**
     * 获取活动PV总统计数据
     *
     * @param activityId
     * @param beginDate
     * @param endDate
     * @return
     */
    JSONObject getActivitySumPV(@Param("activityId") Long activityId, @Param("beginDate") LocalDate beginDate, @Param("endDate") LocalDate endDate);

    /**
     * 获取活动UV总统计数据
     *
     * @param activityId
     * @param beginDate
     * @param endDate
     * @return
     */
    JSONObject getActivitySumUV(@Param("activityId") Long activityId, @Param("beginDate") LocalDate beginDate, @Param("endDate") LocalDate endDate);

    /**
     * 获取列表分类列表
     *
     * @param activityId
     * @param beginDate
     * @param endDate
     * @return
     */
    List<JSONObject> getPVList(@Param("activityId") Long activityId, @Param("beginDate") LocalDate beginDate, @Param("endDate") LocalDate endDate);

}
