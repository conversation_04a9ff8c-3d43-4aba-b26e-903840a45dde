//package com.mrk.yudong.admin.infrastructure.user.mapper;
//
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.mrk.yudong.admin.infrastructure.user.model.Product;
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import org.apache.ibatis.annotations.Param;
//
//import java.util.List;
//
///**
// * <p>
// * 会员商品表 Mapper 接口
// * </p>
// *
// * <AUTHOR>
// * @since 2021-03-23
// */
//public interface ProductMapper extends BaseMapper<Product> {
//
//    IPage<Product> query(Page<Product> page, @Param("name") String name, @Param("status") Integer status, @Param("terminal") Integer terminal, @Param("isTra") Integer isTra, @Param("productType") Integer productType);
//
//    List<String> getUseCode();
//
//}
