package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 课程表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class Course extends Model<Course> {


    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 课程名称
     */
    private String name;

    /**
     * 课程类型：1-普通课程，2-Merit课程
     */
    private Integer type;

    /**
     * 品牌类型：1-merit，2-绝影
     */
    private Integer brandType;

    /**
     * 是否需要VIP：0-否，1-是
     */
    private Integer isVip;

    /**
     * 会员类型：10-VIP、20-SVIP、30-绝影会员
     */
    private Integer vipType;

    /**
     * 设备类型：1-运动类型，2-小件类型
     */
    private Integer equipmentType;

    /**
     * 设备ID
     */
    private Long equipmentId;

    /**
     * 型号ID
     */
    private Long modelId;

    /**
     * 课程封面
     */
    private String cover;

    /**
     * 课程介绍
     */
    private String introduce;

    /**
     * 锻炼部位
     */
    private String part;

    /**
     * 适合人群
     */
    private String crowd;

    /**
     * 禁忌人群
     */
    private String taboo;

    /**
     * 教练ID
     */
    private Long coachId;

    /**
     * 授课时间（预计直播时间）
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime liveTime;

    /**
     * 是否限免：0-否，1-是
     */
    private Integer isFree;

    /**
     * 限时免费时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime freeTime;

    /**
     * 课程时长：分钟
     */
    private Integer courseTime;

    /**
     * 课程等级：1-入门（M1），2-初级（M2），3-中级（M3），4-高级（M4），5-挑战（M5）
     */
    private Integer grade;

    /**
     * 预计消耗
     */
    private Integer kcal;

    /**
     * 距离
     */
    private Double distance;

    /**
     * 速度
     */
    private Double speed;

    /**
     * 热词：JSON数组
     */
    private String hotWords;

    /**
     * 提交审核时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime reviewStartTime;

    /**
     * 最终审核人ID
     */
    private Long reviewId;

    /**
     * 最终审核备注
     */
    private String reviewRemark;

    /**
     * 审核结束时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime reviewEndTime;

    /**
     * 课程状态：0-草稿，10-待审核，20-审核拒绝，30-审核通过（上线，等待直播），40-直播中，50-直播结束，60-转码完成，9-下线
     */
    private Integer status;

    /**
     * 上一个状态
     */
    @JsonIgnore
    @TableField(exist = false)
    private Integer preStatus;

    /**
     * 推流开始时间
     */
    private LocalDateTime pushStartTime;

    /**
     * 推流结束时间
     */
    private LocalDateTime pushEndTime;

    /**
     * 实际直播时间
     */
    private LocalDateTime actualLiveTime;

    /**
     * 直播关闭时间
     */
    private LocalDateTime liveCloseTime;

    /**
     * vod视频ID
     */
    private String videoId;

    /**
     * 视频类型：1-横版，2-竖版
     */
    private Integer videoMediaType;

    /**
     * 录制开始时间
     */
    private LocalDateTime transcribeStartTime;

    /**
     * 录制结束时间
     */
    private LocalDateTime transcribeEndTime;

    /**
     * 剪辑开始时间
     */
    private LocalDateTime clipStartTime;

    /**
     * 剪辑结束时间
     */
    private LocalDateTime clipEndTime;

    /**
     * 转码完成时间
     */
    private LocalDateTime encodeTime;

    /**
     * 分享数量
     */
    private Integer shareNum;

    /**
     * 播放数量
     */
    private Integer playNum;

    /**
     * 机器人数量
     */
    private Integer robotNum;

    /**
     * 机器人在线播放数量
     */
    private Integer robotPlayNum;

    /**
     * 直播实际在线播放人数
     */
    private Integer livePlayNum;

    /**
     * 在线状态：0-下线，1-在线
     */
    private Integer lineStatus;

    /**
     * 下线理由
     */
    private String lineMsg;

    /**
     * 下线人ID
     */
    private Long lineId;

    /**
     * 下线时间
     */
    private LocalDateTime lineTime;

    /**
     * 课程渠道：1-直播，2-录播录入
     */
    private Integer channel;

    /**
     * 是否为预发数据：0-否，1-是
     */
    private Integer isTra;
    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 音乐名称
     */
    private String musicName;

    /**
     * 比赛目标值
     */
    private Integer raceTarget;

    /**
     * 比赛目标值提醒
     */
    private String raceTargetTip;

    /**
     * 比赛状态
     */
    private Integer raceStatus;

    /**
     * 比赛开始时间
     */
    private LocalDateTime raceStartTime;

    /**
     * 比赛结束时间
     */
    private LocalDateTime raceEndTime;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 预览视频生成类型1.系统生成，2.手动上传
     */
    private Integer previewVideoGenerationType;

    /**
     * 课程id
     */
    private Long categoryId;

    /**
     * 预览视频地址
     */
    private String previewVideo;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
