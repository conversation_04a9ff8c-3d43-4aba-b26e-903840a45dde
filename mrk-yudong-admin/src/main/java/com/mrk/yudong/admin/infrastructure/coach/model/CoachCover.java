package com.mrk.yudong.admin.infrastructure.coach.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 教练封面图片表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-19
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CoachCover extends Model<CoachCover> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 教练ID
     */
    private Long coachId;

    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public CoachCover(Long coachId, String imgUrl, Long createId) {
        this.coachId = coachId;
        this.imgUrl = imgUrl;
        this.createId = createId;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
