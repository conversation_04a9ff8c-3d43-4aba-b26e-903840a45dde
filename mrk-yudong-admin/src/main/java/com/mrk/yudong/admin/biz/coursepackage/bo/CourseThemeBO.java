package com.mrk.yudong.admin.biz.coursepackage.bo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class CourseThemeBO {
    private Long id;

    private String name;

    private Integer displayLocation;

    private String themeMap;

    private String cover;

    /**
     * 新版主题封面
     */
    private String themeCover;

    private Long equipmentId;

    private String introduce;

    private LocalDateTime beginTime;

    private LocalDateTime endTime;

    private Integer isForever;

    private Integer sort;

    private List<Long> courseIds;

    private List<CourseThemeCourseBO> courses;

    private Long updateId;

    private List<Long> tagIds;

    private Integer status;

    private LocalDateTime updateTime;
    private String categoryCode;
}
