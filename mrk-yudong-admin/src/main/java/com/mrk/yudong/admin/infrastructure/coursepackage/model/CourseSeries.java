package com.mrk.yudong.admin.infrastructure.coursepackage.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 系列课程
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CourseSeries extends Model<CourseSeries> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 系列名称
     */
    private String name;

    /**
     * 系列描述
     */
    private String introduce;

    /**
     * 系列图片
     */
    private String cover;

    /**
     * 是否大图
     */
    private Integer isMainCover;

    /**
     * 系列排序
     */
    private Integer seq;

    /**
     * 系列状态
     */
    private Integer status;

    /**
     * 删除状态
     */
    private Integer isDelete;

    /**
     * 包含的课包数量
     */
    private Integer packageCount;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
