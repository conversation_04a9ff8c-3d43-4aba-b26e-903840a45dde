package com.mrk.yudong.admin.biz.course.service;

import com.mrk.yudong.admin.infrastructure.course.model.LogCourseDetail;
import com.mrk.yudong.core.service.BaseService;

/**
 * <p>
 * 课程详情记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-05
 */
public interface ILogCourseDetailService extends BaseService<LogCourseDetail> {

    /**
     * 获取去重用户数量
     *
     * @param courseId
     * @return
     */
    int getGroupUserNum(Long courseId);

}
