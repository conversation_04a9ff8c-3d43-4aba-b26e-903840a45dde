package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 课程评分表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CourseScore extends Model<CourseScore> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 教练评分
     */
    private Integer coachScore;

    /**
     * 风格评分
     */
    private Integer styleScore;

    /**
     * 强度评分
     */
    private Integer powerScore;

    /**
     * 播放课程状态：1-直播，2-录播
     */
    private Integer playStatus;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
