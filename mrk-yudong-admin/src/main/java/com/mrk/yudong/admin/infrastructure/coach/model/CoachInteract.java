package com.mrk.yudong.admin.infrastructure.coach.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 教练互动词表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CoachInteract extends Model<CoachInteract> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 教练ID
     */
    private Long coachId;

    /**
     * 互动词ID
     */
    private Long interactId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public CoachInteract(Long coachId, Long interactId) {
        this.coachId = coachId;
        this.interactId = interactId;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
