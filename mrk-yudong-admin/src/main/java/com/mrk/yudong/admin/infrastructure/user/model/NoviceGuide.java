package com.mrk.yudong.admin.infrastructure.user.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.ResponseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * <p>
 * 新手指引
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class NoviceGuide extends Model<NoviceGuide> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 设备类型id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @NotNull(message = ResponseConstant.EQUIP_SELECT_DEVICE_TYPE)
    private Long equipTypeId;
    /**
     * 设备类型名称
     */
    @TableField(exist = false)
    private String equipTypeName;
    /**
     * 标题
     */
    @NotBlank(message = "请填写标题")
    @Size(max = 20, min = 2, message = "标题长度为2-20字符")
    private String title;

    /**
     * 新手指引主图
     */
    private String image;
    /**
     * 新手指引视频
     */
    private String video;
    /**
     * 内容
     */
    @NotBlank(message = "请输入新手指引内容信息")
    private String content;

    /**
     * 操作人
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long operationBy;
    /**
     * 操作人名称
     */
    @TableField(exist = false)
    private String operationByName;
    /**
     * 操作时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime operationDate;


}
