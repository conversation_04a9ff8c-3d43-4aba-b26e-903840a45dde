package com.mrk.yudong.admin.biz.device.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.merach.sun.device.dto.qry.ProductModelFirmwareQry;
import com.mrk.yudong.admin.api.device.dto.cmd.firmware.CreateModelFirmwareCmd;
import com.mrk.yudong.admin.api.device.dto.cmd.firmware.UpdateModelFirmwareCmd;
import com.mrk.yudong.admin.api.device.dto.resp.firmware.BackstageModelFirmwareDetailDTO;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 产品
 *
 * <AUTHOR>
 * @create 2023−01-08 10:29
 */
public interface IProductModelFirmwareService {
    BackstageModelFirmwareDetailDTO createProductModelFirmware(@RequestBody CreateModelFirmwareCmd cmd);


    BackstageModelFirmwareDetailDTO updateProductModelFirmware(@RequestBody UpdateModelFirmwareCmd cmd);

    Boolean deleteProductModelFirmware(Long id);


    BackstageModelFirmwareDetailDTO getProductModelFirmware(@SpringQueryMap ProductModelFirmwareQry productModelFirmwareQry);


    List<BackstageModelFirmwareDetailDTO> listProductModelFirmwares(@SpringQueryMap ProductModelFirmwareQry productModelFirmwareQry);


    Page<BackstageModelFirmwareDetailDTO> pageProductModelFirmwares(@SpringQueryMap ProductModelFirmwareQry productModelFirmwareQry);

}
