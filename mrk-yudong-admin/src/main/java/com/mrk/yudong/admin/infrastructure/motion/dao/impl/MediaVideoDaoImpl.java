package com.mrk.yudong.admin.infrastructure.motion.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.api.motion.dto.qry.MediaPageQry;
import com.mrk.yudong.admin.infrastructure.motion.dao.MediaVideoDao;
import com.mrk.yudong.admin.infrastructure.motion.mapper.MediaVideoMapper;
import com.mrk.yudong.admin.infrastructure.motion.model.MediaVideo;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Component
public class MediaVideoDaoImpl extends BaseServiceImpl<MediaVideoMapper, MediaVideo> implements MediaVideoDao {
    @Override
    public MediaVideo createVideo(MediaVideo creation) {
        this.save(creation);
        return creation;
    }

    @Override
    public MediaVideo updateVideo(MediaVideo updated) {
        this.updateById(updated);
        return getById(updated.getId());
    }

    @Override
    public Boolean deleteVideo(Long id) {
        if (ObjectUtil.isNull(id)) {
            return Boolean.FALSE;
        }

        LambdaQueryWrapper<MediaVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MediaVideo::getId, id);
        return this.remove(queryWrapper);
    }

    @Override
    public MediaVideo getById(Long id) {
        return this.baseMapper.selectById(id);
    }

    @Override
    public MediaVideo getByVideoId(String videoId) {
        LambdaQueryWrapper<MediaVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MediaVideo::getVideoId, videoId);
        queryWrapper.last("LIMIT 1");

        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public IPage<MediaVideo> pageVideos(MediaPageQry pageQry) {
        LambdaQueryWrapper<MediaVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotNull(pageQry.getId()), MediaVideo::getId, pageQry.getId());
        queryWrapper.eq(ObjectUtil.isNotNull(pageQry.getVideoId()), MediaVideo::getVideoId, pageQry.getVideoId());
        queryWrapper.eq(ObjectUtil.isNotNull(pageQry.getType()), MediaVideo::getSubType, pageQry.getType());
        queryWrapper.eq(ObjectUtil.isNotNull(pageQry.getSubType()), MediaVideo::getSubType, pageQry.getSubType());
        queryWrapper.likeRight(ObjectUtil.isNotNull(pageQry.getName()), MediaVideo::getName, pageQry.getName());
        queryWrapper.orderByDesc(MediaVideo::getUpdateTime);

        return baseMapper.selectPage(new Page<>(pageQry.getCurrent(), pageQry.getSize()), queryWrapper);
    }

    @Override
    public List<MediaVideo> findVideosByIds(Set<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return List.of();
        }

        LambdaQueryWrapper<MediaVideo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MediaVideo::getId, ids);

        return baseMapper.selectList(queryWrapper);
    }
}
