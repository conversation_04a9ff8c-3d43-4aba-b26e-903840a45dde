package com.mrk.yudong.admin.biz.course.service;

import com.mrk.yudong.admin.infrastructure.course.model.CourseLink;
import com.mrk.yudong.core.service.BaseService;
import com.mrk.yudong.share.po.CourseLinkPO;

import java.util.List;

/**
 * <p>
 * 课程小节表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
public interface ICourseLinkService extends BaseService<CourseLink> {

    /**
     * 根据课程ID和环节ID获取课程小节信息列表
     *
     * @param courseId
     * @param catalogueId
     * @return
     */
    List<CourseLinkPO> list(Long courseId, Long catalogueId);

}
