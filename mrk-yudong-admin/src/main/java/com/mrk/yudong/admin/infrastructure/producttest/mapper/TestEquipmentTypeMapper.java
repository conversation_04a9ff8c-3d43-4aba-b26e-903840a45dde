package com.mrk.yudong.admin.infrastructure.producttest.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.equipment.model.EquipmentType;
import com.mrk.yudong.admin.infrastructure.producttest.model.TestEquipmentType;

import java.util.List;

/**
 * <p>
 * 设备类型 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-23
 */
public interface TestEquipmentTypeMapper extends BaseMapper<TestEquipmentType> {
    /**
     * 获取二级类型
     * @return
     */
    List<EquipmentType> getTwoTypeNum();
}
