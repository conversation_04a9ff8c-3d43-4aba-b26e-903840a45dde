package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.course.model.PlanReportConfig;
import com.mrk.yudong.core.model.PageDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 训练计划报告地标/食物配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
public interface PlanReportConfigMapper extends BaseMapper<PlanReportConfig> {

    PageDTO<PlanReportConfig> query(PageDTO<PlanReportConfig> page, @Param("type") Integer type);

    List<JSONObject> getNum();

}
