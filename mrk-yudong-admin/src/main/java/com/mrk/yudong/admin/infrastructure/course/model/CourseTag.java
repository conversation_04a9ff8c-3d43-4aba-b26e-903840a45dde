package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 课程标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
@JsonIgnoreProperties(value = {"createId", "createTime", "updateId"})
@Data
@EqualsAndHashCode(callSuper = false)
public class CourseTag extends Model<CourseTag> {


    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 显示名称
     */
    @NotBlank(message = "显示名称不能为空")
    @Length(max = 32, message = "显示名称长度不能超过32位")
    private String name;

    /**
     * 排序
     */
    @NotNull(message = "请填写排序信息")
    private Integer sort;

    /**
     * 设备类型id
     */
    private String equipTypeId;

    /**
     * 上级ID
     */
    private Long parentId;

    /**
     * 被使用次数
     */
    @TableField(exist = false)
    private Integer num;

    /**
     * 是否隐藏：0-否，1-是
     */
    @NotNull(message = "请选择是否隐藏")
    @Range(min = BaseConstant.INT_FALSE, max = BaseConstant.INT_TRUE, message = "是否隐藏数据不合法")
    private Integer isHide;

    /**
     * 是否为预发环境：0-否，1-是
     */
    private Integer isTra;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 最近更新人
     */
    @TableField(exist = false)
    private String updateName;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
