package com.mrk.yudong.admin.infrastructure.user.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.user.model.AppVersion;

import java.util.List;

/**
 * <p>
 * app版本控制表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-06
 */
public interface AppVersionMapper extends BaseMapper<AppVersion> {


    /**
     * 获取选择数据字典
     *
     * @param isTra
     * @return
     */
    List<JSONObject> option(Integer isTra);

}
