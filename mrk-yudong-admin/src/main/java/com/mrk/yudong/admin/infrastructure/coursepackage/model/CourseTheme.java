package com.mrk.yudong.admin.infrastructure.coursepackage.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.LinkedHashSet;

/**
 * <p>
 * 课程主题表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CourseTheme extends Model<CourseTheme> {


    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 类型：1-主题，2-标签
     */
    @NotNull(message = "请选择类型")
    @Range(min = 1, max = 2, message = "主题类型不合法")
    private Integer type;

    /**
     * 显示位置：1.首页推荐板块，2设备板块，3-超燃脂自由练主题板块，4-超燃脂自由练专项训练板块
     */
    @Range(min = 1, max = 4, message = "显示位置数据不合法")
    private Integer displayLocation;

    /**
     * 主题类型：1.课程，2训练计划,3.实景视频
     */
    private Integer useType;

    /**
     * 展现形式：1大图，2合辑
     */
    @Range(min = 1, max = 2, message = "展现形式不合法")
    private Integer displayForm;

    /**
     * 首页主题图
     */
    private String themeMap;

    /**
     * 主题名称
     */
    @NotBlank(message = "主题名称不能为空")
    @Length(max = 16, message = "主题名称长度不能超过16位")
    private String name;

    /**
     * 设备ID
     */
    private Long equipmentId;

    /**
     * 主题封面
     */
    private String cover;

    /**
     * 新版主题封面
     */
    private String themeCover;

    /**
     * 主题介绍
     */
    private String introduce;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime endTime;

    /**
     * 排序号
     */
    @NotNull(message = "排序后不能为空")
    @Min(value = 0, message = "排序号不能小于0")
    private Integer sort;

    /**
     * 是否永久显示：0-否，1-是
     */
    @NotNull(message = "请选择是否长期显示")
    @Range(max = 1L, message = "是否长期显示数据不合法")
    private Integer isForever;

    /**
     * 课程ID
     */
    @TableField(exist = false)
    private LinkedHashSet<Long> courseIds;

    /**
     * 计划ID
     */
    @TableField(exist = false)
    private LinkedHashSet<Long> planIds;

    /**
     * 实景视频ID
     */
    @TableField(exist = false)
    private LinkedHashSet<Long> liveVideoIds;

    /**
     * 状态：0-下线，1-上线
     */
    private Integer status;

    /**
     * 是否为预发数据：0-否，1-是
     */
    private Integer isTra;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
     * 健身目的
     */
    private String categoryCode;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
