package com.mrk.yudong.admin.infrastructure.marketing.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2023−04-17 17:25
 */
@Getter
public enum MarketingSalesStatusEnum {

    NOT_STARTED(0, "未开始"),
    ONGOING(1, "进行中"),
    FINISHED(2, "已结束");

    private final int code;
    private final String description;

    MarketingSalesStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static MarketingSalesStatusEnum fromCode(Integer code) {
        for (MarketingSalesStatusEnum status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid MarketingSalesStatus code: " + code);
    }
}

