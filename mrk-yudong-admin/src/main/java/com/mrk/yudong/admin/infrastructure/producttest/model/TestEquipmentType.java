package com.mrk.yudong.admin.infrastructure.producttest.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 设备类型
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TestEquipmentType extends Model<TestEquipmentType> {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 蓝牙广播名前缀id
     */
    private Integer prefixId;

    /**
     * 设备型号所属编号
     */
    private String code;

    /**
     * 设备类型名称
     */
    @NotBlank(message = "请填写设备名称")
    private String typeName;

    /**
     * 目录等级
     */
    private Integer level;

    /**
     * 上级id
     */
    private Long parentId;

    /**
     * 展示图片
     */
    @NotBlank(message = "展示图片不能为空")
    private String typeImages;

    /**
     * 固件型号
     */
    private Long typeVersionId;

    /**
     * 仪表类型 1.飞梭
     */
    private Integer instrumentType;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 删除状态0：未删除，1：已删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    private Long updateBy;

    /**
     * 功能参数集合
     */
    @TableField(exist = false)
    private List<TestModelParamsAssocited> functionParamsList;

    /**
     * 设备参数集合
     */
    @TableField(exist = false)
    private List<TestModelParamsAssocited> equipParamsList;
    /**
     * 固件版本信息集合
     */
    @TableField(exist = false)
    private List<TestEquFirmwareVersion> FirmwareInfoList;
    /**
     * 固件版本信息
     */
    @TableField(exist = false)
    private TestEquFirmwareVersion FirmwareInfo;
    /**
     * 仪表类型名称 飞梭
     */
    @TableField(exist = false)
    private String instrumentTypeName;
    /**
     * 分类id
     */
    @TableField(exist = false)
    private Long typeId;
    /**
     * 型号数量
     */
    @TableField(exist = false)
    private Integer num;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
