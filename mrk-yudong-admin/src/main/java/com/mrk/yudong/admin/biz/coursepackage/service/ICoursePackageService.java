package com.mrk.yudong.admin.biz.coursepackage.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.api.coursepackage.dto.packages.cmd.CreateCoursePackageCmd;
import com.mrk.yudong.admin.api.coursepackage.dto.packages.cmd.UpdateCoursePackageCmd;
import com.mrk.yudong.admin.api.coursepackage.dto.packages.qry.CoursePackagePageQry;
import com.mrk.yudong.admin.api.coursepackage.dto.packages.resp.CoursePackageDTO;
import com.mrk.yudong.admin.api.coursepackage.dto.packages.resp.PageCoursePackageDTO;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CoursePackage;
import com.mrk.yudong.core.service.BaseService;

/**
 * <p>
 * 课程包 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
public interface ICoursePackageService extends BaseService<CoursePackage> {
    Boolean createCoursePackage(CreateCoursePackageCmd coursePackageCmd);
    Boolean updateCoursePackage(UpdateCoursePackageCmd coursePackageCmd);
    Boolean deleteCoursePackage(Long id);
    CoursePackageDTO getCoursePackage(Long id);
    Page<PageCoursePackageDTO> pageCoursePackage(CoursePackagePageQry coursePackagePageQry);
    Boolean changeCoursePackageStatus(Long id, int status);
}
