package com.mrk.yudong.admin.security.handler;

import com.mrk.yudong.core.model.R;
import com.mrk.yudong.share.constant.BaseTipConstant;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
public class AuthenticationEntryPointHandler implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e) throws IOException, ServletException {
        R.writer(R.fail(HttpStatus.UNAUTHORIZED.value(), BaseTipConstant.REQUEST_UNAUTHORIZED_VOID), response);
    }

}
