package com.mrk.yudong.admin.biz.course.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mrk.yudong.admin.infrastructure.course.mapper.CoursePlanAssociatedMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CoursePlanAssociated;
import com.mrk.yudong.admin.biz.course.service.ICoursePlanAssociatedService;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 课程训练计划关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
@Service
public class CoursePlanAssociatedServiceImpl extends BaseServiceImpl<CoursePlanAssociatedMapper, CoursePlanAssociated> implements ICoursePlanAssociatedService {

    @Override
    public int getCountByCourseId(Long courseId) {
        LambdaQueryWrapper<CoursePlanAssociated> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CoursePlanAssociated::getCourseId, courseId);
        return baseMapper.selectCount(queryWrapper);
    }

}
