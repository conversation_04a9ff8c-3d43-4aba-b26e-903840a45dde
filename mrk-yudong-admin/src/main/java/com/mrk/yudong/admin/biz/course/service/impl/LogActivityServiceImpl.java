package com.mrk.yudong.admin.biz.course.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.admin.infrastructure.course.mapper.LogActivityMapper;
import com.mrk.yudong.admin.infrastructure.course.model.LogActivity;
import com.mrk.yudong.admin.biz.course.service.ILogActivityService;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <p>
 * 活动数据记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class LogActivityServiceImpl extends BaseServiceImpl<LogActivityMapper, LogActivity> implements ILogActivityService {

    /**
     * 获取活动PV总统计数据
     *
     * @param activityId
     * @param beginDate
     * @param endDate
     * @return
     */
    @Override
    public JSONObject getActivitySumPV(Long activityId, LocalDate beginDate, LocalDate endDate) {
        return baseMapper.getActivitySumPV(activityId, beginDate, endDate);
    }

    /**
     * 获取活动PV总统计数据
     *
     * @param activityId
     * @param beginDate
     * @param endDate
     * @return
     */
    @Override
    public JSONObject getActivitySumUV(Long activityId, LocalDate beginDate, LocalDate endDate) {
        return baseMapper.getActivitySumUV(activityId, beginDate, endDate);
    }

    @Override
    public List<JSONObject> getPVList(Long activityId, LocalDate beginDate, LocalDate endDate) {
        List<JSONObject> pvList = baseMapper.getPVList(activityId, beginDate, endDate);
        if (CollUtil.isEmpty(pvList)) {
            return pvList;
        }

        int year = LocalDate.now().getYear();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");
        pvList.forEach(dict -> {
            LocalDate joinTime = LocalDate.parse(dict.getString("joinTime"));
            if (year == joinTime.getYear()) {
                dict.replace("joinTime", joinTime.format(formatter));
            }
        });
        return pvList;
    }

}
