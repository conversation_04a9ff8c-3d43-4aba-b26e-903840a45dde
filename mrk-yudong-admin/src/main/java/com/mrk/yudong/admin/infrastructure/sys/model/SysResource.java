package com.mrk.yudong.admin.infrastructure.sys.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 后台资源表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysResource extends Model<SysResource> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 资源名称
     */
    @NotBlank(message = "资源名称不能为空")
    @Length(max = 16, message = "资源名称不能超过16位长度")
    private String name;

    /**
     * 资源编码
     */
    @NotBlank(message = "资源编码不能为空")
    @Length(max = 32, message = "资源编码不能超过32位长度")
    private String code;

    /**
     * 资源地址
     */
    private String url;

    /**
     * 访问方式
     */
    @NotBlank(message = "访问方式不能为空")
    private String method;

    /**
     * 资源类型：1-菜单，2-按钮，3-功能
     */
    @NotNull(message = "资源类型不能为空")
    private Integer type;

    /**
     * 资源类型文字显示
     */
    @TableField(exist = false)
    private String typeDesc;

    /**
     * 排序号
     */
    @NotNull(message = "排序号不能为空")
    private Integer sort;

    /**
     * 上级资源ID
     */
    private Long parentId;

    /**
     * 资源状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 资源状态文字显示
     */
    @TableField(exist = false)
    private String statusDesc;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Long updateBy;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
