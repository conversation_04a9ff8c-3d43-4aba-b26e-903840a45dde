package com.mrk.yudong.admin.biz.course.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.infrastructure.course.model.CourseHotword;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.service.BaseService;

/**
 * <p>
 * 课程热词表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
public interface ICourseHotwordService extends BaseService<CourseHotword> {

    R remove(Long id);

    IPage<CourseHotword> query(PageDTO<CourseHotword> pageDTO, String name, Long equipmentId, Integer stage, Integer isTra);

}
