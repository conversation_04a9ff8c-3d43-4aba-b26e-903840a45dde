package com.mrk.yudong.admin.infrastructure.user.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 训练数据统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-10
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TrainStatistic extends Model<TrainStatistic> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 设备ID
     */
    private Long equipmentId;

    /**
     * 训练用户数量
     */
    private Long userNum;

    /**
     * 耗时：秒
     */
    private Long takeTime;

    /**
     * 消耗
     */
    private Double kcal;

    /**
     * 统计日期
     */
    @JsonIgnore
    private LocalDate statisticTime;

    /**
     * 显示时间
     */
    @TableField(exist = false)
    private String time;

    /**
     * 总数量
     */
    @TableField(exist = false)
    private Long sumNum;

    /**
     * 创建时间
     */
    @JsonIgnore
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public TrainStatistic(Long equipmentId, LocalDate statisticTime) {
        this.equipmentId = equipmentId;
        this.statisticTime = statisticTime;
    }

    public TrainStatistic(Long equipmentId, Long userNum, Long takeTime, Double kcal, LocalDate statisticTime) {
        this.equipmentId = equipmentId;
        this.userNum = userNum;
        this.takeTime = takeTime;
        this.kcal = kcal;
        this.statisticTime = statisticTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
