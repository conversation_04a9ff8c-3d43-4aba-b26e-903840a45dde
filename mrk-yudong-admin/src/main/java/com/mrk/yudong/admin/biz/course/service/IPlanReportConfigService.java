package com.mrk.yudong.admin.biz.course.service;

import com.mrk.yudong.admin.api.course.query.PlanReportConfigQuery;
import com.mrk.yudong.admin.infrastructure.course.model.PlanReportConfig;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.service.BaseService;

import java.util.Map;

/**
 * <p>
 * 训练计划报告地标/食物配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
public interface IPlanReportConfigService extends BaseService<PlanReportConfig> {

    PageDTO<PlanReportConfig> query(PlanReportConfigQuery planReportConfigQuery);

    Map<Integer, Integer> getNum();

    /**
     * 【新增记录】地标/食物配置信息
     * @param planReportConfig 训练计划报告配置实体类
     */
    PlanReportConfig createPlanReportConfig(PlanReportConfig planReportConfig);

    /**
     * 【修改记录】地标/食物配置信息
     */
    PlanReportConfig updatePlanReportConfig(PlanReportConfig planReportConfig);
}
