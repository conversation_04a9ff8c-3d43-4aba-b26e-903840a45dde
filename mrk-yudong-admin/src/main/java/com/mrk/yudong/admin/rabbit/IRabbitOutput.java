package com.mrk.yudong.admin.rabbit;

import com.mrk.yudong.share.constant.QueueConstant;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

@Component
public interface IRabbitOutput {

    @Output(QueueConstant.USER_COURSE_RACE_QUEUE)
    MessageChannel userCourseRaceOutput();

    @Output(QueueConstant.WEBSOCKET_QUEUE)
    MessageChannel websocketOutput();

    @Output(QueueConstant.CLOSE_WEBSOCKET_QUEUE)
    MessageChannel closeSocketOutput();

}
