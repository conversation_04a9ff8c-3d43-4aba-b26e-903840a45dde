package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.api.course.po.*;
import com.mrk.yudong.admin.api.course.query.CourseOptionPageQry;
import com.mrk.yudong.admin.biz.course.bo.CourseOptionBO;
import com.mrk.yudong.admin.infrastructure.course.model.Course;
import com.mrk.yudong.admin.api.course.vo.CourseQueryVO;
import com.mrk.yudong.admin.api.course.vo.PlanCourseQueryVO;
import com.mrk.yudong.admin.api.course.vo.ReviewQueryVO;
import com.mrk.yudong.admin.api.course.vo.ThemeCourseQueryVO;
import com.mrk.yudong.core.model.PageDTO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 课程表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
public interface CourseMapper extends BaseMapper<Course> {

    /**
     * 分页查询课程列表
     *
     * @param page
     * @param courseQueryVO
     * @return
     */
    IPage<CoursePO> query(PageDTO<CoursePO> page, @Param("courseQueryVO") CourseQueryVO courseQueryVO);

    /**
     * 查询课程列表
     *
     * @param courseQueryVO
     * @return
     */
    List<JSONObject> queryMap(@Param("courseQueryVO") CourseQueryVO courseQueryVO);

    /**
     * 课程详情
     *
     * @param courseId
     * @return
     */
    CourseDetailPO detail(@Param("courseId") Long courseId);

    /**
     * 查询今日直播课
     *
     * @param page
     * @param isTra
     * @param channel
     * @return
     */
    IPage<TodayLivePO> queryTodayLive(PageDTO<TodayLivePO> page, @Param("isTra") Integer isTra, @Param("channel") Integer channel);

    /**
     * 审核记录列表
     *
     * @param page
     * @param reviewQueryVO
     * @return
     */
    IPage<CoursePO> reviewList(PageDTO<CoursePO> page, @Param("reviewQueryVO") ReviewQueryVO reviewQueryVO);

    /**
     * 审核课程导出
     *
     * @param reviewQueryVO
     * @return
     */
    List<JSONObject> reviewExport(@Param("reviewQueryVO") ReviewQueryVO reviewQueryVO);

    /**
     * 课程主题查询课程列表
     *
     * @param page
     * @param themeCourseQueryVO
     * @return
     */
    IPage<JSONObject> themeCourseQuery(PageDTO<Course> page, @Param("themeCourseQueryVO") ThemeCourseQueryVO themeCourseQueryVO);

    /**
     * 直播课数量查询
     *
     * @return
     */
    JSONObject liveCount(@Param("isTra") Integer isTra, @Param("channel") Integer channel);

    /**
     * 查询教练所属课程
     *
     * @param page
     * @param coachId
     * @return
     */
    IPage<JSONObject> queryBuCoachId(PageDTO<Map<String, Object>> page, @Param("coachId") Long coachId);

    /**
     * 查询计划课程列表
     *
     * @param page
     * @param planCourseQueryVO
     * @return
     */
    IPage<PlanCoursePO> queryPlanCourse(PageDTO<PlanCoursePO> page, @Param("planCourseQueryVO") PlanCourseQueryVO planCourseQueryVO);

    /**
     * 查询课程统计数据
     *
     * @param page
     * @param courseQueryVO
     * @return
     */
    IPage<CoursePO> queryStatistics(PageDTO<CoursePO> page, @Param("courseQueryVO") CourseQueryVO courseQueryVO);

    /**
     * 查询课程统计数据导出
     *
     * @param courseQueryVO 查询参数
     * @return java.util.List<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @date 2022/3/1 13:11
     */
    List<JSONObject> queryStatisticsExport(@Param("courseQueryVO") CourseQueryVO courseQueryVO);

    /**
     * 修改训练计划上下线状态
     *
     * @param ids
     * @return
     */
    boolean updateActivityId(@Param("ids") Set<Long> ids);

    /**
     * 重新开播
     *
     * @param courseId 课程ID
     * @param status   课程状态
     * @param liveTime 开播时间
     * @return int
     * <AUTHOR>
     * @date 2022/5/16 16:21
     */
    int updateCourseStatus(@Param("courseId") Long courseId, @Param("status") Integer status, @Param("liveTime") LocalDateTime liveTime);


    /**
     * 获取所有类型的课程数量
     * @return
     */
    List<CourseNumPO> getAllCourseNum();

    IPage<CourseOptionBO> pageCourseOptions(PageDTO<Course> page, @Param("courseOptionQry") CourseOptionPageQry qry);


}
