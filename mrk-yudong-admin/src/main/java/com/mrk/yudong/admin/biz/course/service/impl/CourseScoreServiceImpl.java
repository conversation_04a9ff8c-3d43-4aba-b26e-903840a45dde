package com.mrk.yudong.admin.biz.course.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.admin.infrastructure.course.model.CourseScore;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseScoreMapper;
import com.mrk.yudong.admin.biz.course.service.ICourseScoreService;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 课程评分表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@Service
public class CourseScoreServiceImpl extends BaseServiceImpl<CourseScoreMapper, CourseScore> implements ICourseScoreService {

    @Override
    public List<JSONObject> getScoreList(Long courseId, String column) {
        return baseMapper.getScoreList(courseId, column);
    }

}
