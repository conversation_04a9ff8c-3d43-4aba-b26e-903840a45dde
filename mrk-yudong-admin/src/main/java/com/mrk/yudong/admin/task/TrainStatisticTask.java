package com.mrk.yudong.admin.task;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.mrk.yudong.admin.biz.user.service.IRabbitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 训练数据统计
 *
 * <AUTHOR>
 * @date 2023/2/7 15:26
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class TrainStatisticTask extends JavaProcessor {

    private final IRabbitService rabbitService;

    @Override
    public ProcessResult process(JobContext context) {
        LocalDate localDate = LocalDate.now().minusDays(1L);
        log.warn("=== 训练数据统计: {} ===", localDate);
        try {
            rabbitService.trainStatistic(localDate.toString());
            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("训练数据统计 error: ", e);
            return new ProcessResult(false);
        }
    }

}
