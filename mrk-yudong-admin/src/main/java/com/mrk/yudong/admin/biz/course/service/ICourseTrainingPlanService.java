package com.mrk.yudong.admin.biz.course.service;

import com.mrk.yudong.admin.api.course.vo.CourseCatalogueVO;
import com.mrk.yudong.admin.infrastructure.course.model.CourseTrainingPlan;
import com.mrk.yudong.core.service.BaseService;

import java.util.List;

/**
 * <p>
 * 训练计划 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
public interface ICourseTrainingPlanService extends BaseService<CourseTrainingPlan> {
    /**
     * 修改训练计划上下线状态
     *
     * @param id
     * @param oper 1上线，2下线
     * @return
     */
    boolean updateOnlineStatus(Long id, Integer oper);

    /**
     * 获取训练计划条数
     *
     * @return
     */
    List<CourseCatalogueVO> getCountList();


}
