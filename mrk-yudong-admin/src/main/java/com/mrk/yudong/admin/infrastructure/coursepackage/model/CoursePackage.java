package com.mrk.yudong.admin.infrastructure.coursepackage.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 课程包
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CoursePackage extends Model<CoursePackage> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 课包名称
     */
    private String name;

    /**
     * 课包图片
     */
    private String cover;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 描述
     */
    private String description;

    /**
     * 课包包含的产品id
     */
    private String productIds;

    /**
     * 课包包含的课程数量
     */
    private Integer courseCount;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    private Integer isDelete;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
