package com.mrk.yudong.admin.biz.productiontest.service.impl;

import com.mrk.yudong.admin.biz.productiontest.service.ITestEquipmentTypeService;
import com.mrk.yudong.admin.infrastructure.equipment.model.EquipmentType;
import com.mrk.yudong.admin.infrastructure.producttest.mapper.TestEquipmentTypeMapper;
import com.mrk.yudong.admin.infrastructure.producttest.model.TestEquipmentType;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 设备类型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-23
 */
@Service
public class TestEquipmentTypeServiceImpl extends BaseServiceImpl<TestEquipmentTypeMapper, TestEquipmentType> implements ITestEquipmentTypeService {

    @Override
    public List<EquipmentType> getTwoTypeNum() {
        return baseMapper.getTwoTypeNum();
    }
}
