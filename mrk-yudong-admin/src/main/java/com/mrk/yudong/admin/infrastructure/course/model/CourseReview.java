package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 课程审核明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CourseReview extends Model<CourseReview> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 审核人ID
     */
    private Long sysUserId;

    /**
     * 审核结果：0-拒绝，1-通过
     */
    private Integer review;

    /**
     * 审核备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public CourseReview(Long courseId, Long sysUserId, Integer review, String remark) {
        this.courseId = courseId;
        this.sysUserId = sysUserId;
        this.review = review;
        this.remark = remark;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
