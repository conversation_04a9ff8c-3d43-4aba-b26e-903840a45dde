package com.mrk.yudong.admin.infrastructure.device.model;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 设备字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EquipDict extends Model<EquipDict> {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String name;

    private String dictKey;

    private String subInfo;
    
    private String value;

    private Integer sort;

    //是否需要校验mr
    private Integer mr;

    //需要数据的位数
    private Integer num;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
