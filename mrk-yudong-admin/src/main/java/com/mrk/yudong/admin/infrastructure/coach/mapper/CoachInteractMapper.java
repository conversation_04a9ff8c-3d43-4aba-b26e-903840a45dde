package com.mrk.yudong.admin.infrastructure.coach.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.coach.model.CoachInteract;
import com.mrk.yudong.admin.infrastructure.course.model.InteractInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 教练互动词表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
public interface CoachInteractMapper extends BaseMapper<CoachInteract> {

    /**
     * 根据教练ID获取互动词
     *
     * @param coachId
     * @return
     */
    List<InteractInfo> getListByCoachId(@Param("coachId") Long coachId);

}
