package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 音乐表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Music extends Model<Music> {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 音乐名称
     */
    private String name;

    /**
     * 地址
     */
    @NotBlank(message = "请上传音乐")
    private String url;

    /**
     * 图片
     */
    private String image;

    /**
     * 音频质量
     */
    private String definition;

    /**
     * 时长，单位秒
     */
    private Integer duration;

    /**
     * 标签关联id（暂无使用）
     */
    private Long tagId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 音乐状态 1上架中
     */
    private Integer status;

    /**
     * 备注json
     */
    private String remark;

    /**
     * 逻辑删除
     */
    private Integer isDelete;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime updateTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
