package com.mrk.yudong.admin.biz.xenjoy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.merach.sun.misc.enums.TerminalEnum;
import com.merach.sun.user.api.AccountApi;
import com.merach.sun.user.api.MemberApi;
import com.merach.sun.user.api.UserApi;
import com.merach.sun.user.dto.qry.AccountQry;
import com.merach.sun.user.dto.user.UserInfoDTO;
import com.merach.sun.user.dto.vip.cmd.OpenVipCmd;
import com.merach.sun.user.enums.AccountEnum;
import com.merach.sun.user.enums.vip.VipFlowOperationTypeEnum;
import com.merach.sun.user.enums.vip.VipFlowTypeEnum;
import com.merach.sun.user.enums.vip.VipTypeEnum;
import com.mrk.yudong.admin.api.xenjoy.dto.XenjoyConfigDTO;
import com.mrk.yudong.admin.api.xenjoy.dto.XenjoyOptionDTO;
import com.mrk.yudong.admin.api.xenjoy.query.XenjoyQuery;
import com.mrk.yudong.admin.api.xenjoy.vo.XenjoyConfigVo;
import com.mrk.yudong.admin.biz.sys.service.ISysUserService;
import com.mrk.yudong.admin.biz.xenjoy.service.IXenjoyConfigService;
import com.mrk.yudong.admin.infrastructure.xenjoy.mapper.XenjoyConfigMapper;
import com.mrk.yudong.admin.infrastructure.xenjoy.model.XenjoyConfig;
import com.mrk.yudong.core.exception.MyException;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.QueryUtil;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.enums.MemberProductSkuTypeEnum;
import com.mrk.yudong.share.vo.common.OptionVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 绝影会员卡配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class XenjoyConfigServiceImpl extends BaseServiceImpl<XenjoyConfigMapper, XenjoyConfig> implements IXenjoyConfigService {

    private final UserApi userApi;

    private final ISysUserService sysUserService;

    private final MemberApi memberApi;

    private final AccountApi accountApi;

    @Override
    public PageDTO<XenjoyConfigDTO> page(XenjoyQuery query) {
        LambdaQueryChainWrapper<XenjoyConfig> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.eq(query.getConfigId() != null, XenjoyConfig::getId, query.getConfigId());
        wrapper.eq(StrUtil.isNotBlank(query.getMobile()), XenjoyConfig::getMobile, query.getMobile());
        QueryUtil.setNumberQuery(wrapper, XenjoyConfig::getPrice, query.getPrice());
        QueryUtil.setNumberQuery(wrapper, XenjoyConfig::getNum, query.getNum());
        wrapper.eq(query.getSkuType() != null, XenjoyConfig::getSkuType, query.getSkuType());
        wrapper.eq(query.getStatus() != null, XenjoyConfig::getStatus, query.getStatus());
        QueryUtil.setDateTimeQuery(wrapper, XenjoyConfig::getAssignedTime, query.getAssignedTime());
        wrapper.eq(query.getCreateId() != null, XenjoyConfig::getCreateId, query.getCreateId());
        wrapper.eq(query.getUpdateId() != null, XenjoyConfig::getUpdateId, query.getUpdateId());
        QueryUtil.setDateTimeQuery(wrapper, XenjoyConfig::getCreateTime, query.getCreateTime());
        QueryUtil.setDateTimeQuery(wrapper, XenjoyConfig::getUpdateTime, query.getUpdateTime());
        wrapper.orderByDesc(XenjoyConfig::getCreateTime);
        PageDTO<XenjoyConfig> page = wrapper.page(PageDTO.of(query.getCurrent(), query.getSize()));
        return PageDTO.of(page.getCurrent(), page.getSize(), page.getTotal(), this.transformation(page.getRecords()));
    }

    private List<XenjoyConfigDTO> transformation(List<XenjoyConfig> records) {
        if (CollUtil.isEmpty(records)) {
            return new ArrayList<>(0);
        }

        Set<Long> userIds = records.stream().flatMap(v -> Stream.of(v.getCreateId(), v.getUpdateId())).collect(Collectors.toSet());
        Map<Long, String> userNameData = sysUserService.getOperationUserNameData(userIds);
        return records.stream().map(v -> {
            XenjoyConfigDTO configDTO = BeanUtil.copyProperties(v, XenjoyConfigDTO.class);
            configDTO.setConfigId(v.getId());
            Opt.ofNullable(userNameData.get(v.getCreateId())).ifPresent(configDTO::setCreateName);
            Opt.ofNullable(userNameData.get(v.getUpdateId())).ifPresent(configDTO::setUpdateName);
            Opt.ofNullable(MemberProductSkuTypeEnum.getInstance(v.getSkuType())).ifPresent(e -> configDTO.setSkuTypeName(e.getDesc()));
            configDTO.setFiles(StrUtil.split(v.getFileUrl(), StrPool.C_COMMA, true, true));
            return configDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public XenjoyOptionDTO option() {
        XenjoyOptionDTO optionDTO = new XenjoyOptionDTO();
        optionDTO.setSkuTypes(Stream.of(MemberProductSkuTypeEnum.values())
                .map(v -> new OptionVo(v.getDesc(), String.valueOf(v.getCode()))).collect(Collectors.toList()));
        optionDTO.setUsers(sysUserService.userOptions());
        return optionDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResDTO<Boolean> save(XenjoyConfigVo xenjoyConfigVo) {
        ResDTO<Boolean> resDTO = this.checkParam(xenjoyConfigVo);
        if (ResDTO.isFail(resDTO)) {
            return resDTO;
        }

        XenjoyConfig xenjoyConfig = BeanUtil.copyProperties(xenjoyConfigVo, XenjoyConfig.class);
        xenjoyConfig.setFileUrl(String.join(StrPool.COMMA, xenjoyConfigVo.getFiles()));
        boolean save = this.save(xenjoyConfig);
        if (save) {
            this.assignedVip(xenjoyConfig);
            this.updateById(xenjoyConfig);
        }
        return save ? ResDTO.ok(true) : ResDTO.fail();
    }

    private ResDTO<Boolean> checkParam(XenjoyConfigVo xenjoyConfigVo) {
        if (!Validator.isMobile(xenjoyConfigVo.getMobile())) {
            return ResDTO.paramFail("无效的手机号码");
        }

        if (!MemberProductSkuTypeEnum.isExist(xenjoyConfigVo.getSkuType())) {
            return ResDTO.paramFail("无效类型");
        }

        return ResDTO.ok(true);
    }

    private void assignedVip(XenjoyConfig xenjoyConfig) {
        String mobile = xenjoyConfig.getMobile();
        boolean exist = this.checkAccountExistenceByUserIdOrMobile(null, mobile);
        if (!exist) {
            return;
        }

        // 开始发放绝影会员
        UserInfoDTO userInfo = userApi.userInfo(mobile, AccountEnum.MOBILE);
        Integer num = xenjoyConfig.getNum();
        MemberProductSkuTypeEnum skuTypeEnum = MemberProductSkuTypeEnum.getInstance(xenjoyConfig.getSkuType());
        int days = skuTypeEnum.getDays() * num;
        log.warn("mobile: {} 用户已存在，开始发放绝影会员。 userId: {} 发放信息: {}", mobile, userInfo.getAccountId(), xenjoyConfig);

        OpenVipCmd openVipCmd = new OpenVipCmd()
                .setUserId(userInfo.getAccountId())
                .setType(VipFlowTypeEnum.X_VIP.getCode())
                .setTitle(VipFlowTypeEnum.X_VIP.getDesc())
                .setPackageType(null)
                .setVipType(VipTypeEnum.X_VIP.getCode())
                .setOperationType(VipFlowOperationTypeEnum.OPEN.getCode())
                .setDays(days)
                .setTerminal(TerminalEnum.UNKNOWN.getCode())
                .setBizId(xenjoyConfig.getId().toString())
                .setSubAccount(true);
        boolean operation = memberApi.openVip(openVipCmd);
        if (!operation) {
            throw new MyException(HttpStatus.NOT_IMPLEMENTED.value(), "开通绝影会员失败");
        }
        xenjoyConfig.setStatus(BaseConstant.INT_TRUE).setAssignedTime(LocalDateTime.now());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResDTO<Boolean> update(Long configId, XenjoyConfigVo xenjoyConfigVo) {
        XenjoyConfig dbData = this.getById(configId);
        if (dbData == null) {
            return ResDTO.fail("无效的配置信息");
        }

        if (Objects.equals(dbData.getStatus(), BaseConstant.INT_TRUE)) {
            return ResDTO.fail("已发放数据不支持修改");
        }

        ResDTO<Boolean> resDTO = this.checkParam(xenjoyConfigVo);
        if (ResDTO.isFail(resDTO)) {
            return resDTO;
        }

        XenjoyConfig xenjoyConfig = BeanUtil.copyProperties(xenjoyConfigVo, XenjoyConfig.class);
        xenjoyConfig.setFileUrl(String.join(StrPool.COMMA, xenjoyConfigVo.getFiles())).setId(configId);
        this.assignedVip(xenjoyConfig);
        return this.updateById(xenjoyConfig) ? ResDTO.ok(true) : ResDTO.fail();
    }

    @Override
    public ResDTO<Boolean> remove(Long configId) {
        XenjoyConfig dbData = this.getById(configId);
        if (dbData == null) {
            return ResDTO.fail("无效的配置信息");
        }

        if (Objects.equals(dbData.getStatus(), BaseConstant.INT_TRUE)) {
            return ResDTO.fail("已发放数据不支持删除");
        }

        return this.removeById(configId) ? ResDTO.ok(true) : ResDTO.fail();
    }

    @Override
    public ResDTO<Boolean> retryOpen(Long configId) {
        XenjoyConfig xenjoyConfig = this.getById(configId);
        if (xenjoyConfig == null) {
            return ResDTO.fail("无效的配置信息");
        }

        if (Objects.equals(xenjoyConfig.getStatus(), BaseConstant.INT_TRUE)) {
            return ResDTO.fail("已发放数据不支持重新发放");
        }

        try {
            this.assignedVip(xenjoyConfig);
            return this.updateById(xenjoyConfig) ? ResDTO.ok(true) : ResDTO.fail();
        } catch (Exception e) {
            log.warn(String.format("configId: %s retryOpen xenjoy vip is error.", configId), e);
            return ResDTO.fail("重新发放失败，请检查该用户是否注册");
        }
    }


    private Boolean checkAccountExistenceByUserIdOrMobile(Long userId, String mobile) {
        AccountQry accountQry = new AccountQry();
        if (StrUtil.isNotBlank(mobile)) {
            accountQry.setMobile(mobile);
        }
        if (Objects.nonNull(userId)) {
            accountQry.setAccountId(userId);
        }

        return accountApi.checkAccountExistence(accountQry);
    }

}
