package com.mrk.yudong.admin.biz.productiontest.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.biz.productiontest.service.ITestAppVersionService;
import com.mrk.yudong.admin.infrastructure.producttest.mapper.TestAppVersionMapper;
import com.mrk.yudong.admin.infrastructure.producttest.model.TestAppVersion;
import com.mrk.yudong.admin.infrastructure.producttest.vo.AppVersionQueryVO;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * app版本控制表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
@Service
public class TestAppVersionServiceImpl extends BaseServiceImpl<TestAppVersionMapper, TestAppVersion> implements ITestAppVersionService {
    /**
     * 根据终端获取最新版本信息
     *
     * @param terminal
     * @return
     */
    @Override
    public TestAppVersion getTop(Integer terminal) {
        return baseMapper.getTop(SessionUtil.getIsTra(), terminal);
    }

    /**
     * 查询版本列表
     *
     * @param page
     * @param appVersionQueryVO
     * @return
     */
    @Override
    public IPage<TestAppVersion> query(PageDTO<TestAppVersion> page, AppVersionQueryVO appVersionQueryVO) {
        return baseMapper.query(page, appVersionQueryVO);
    }

    /**
     * 获取选择数据字典
     *
     * @return
     */
    @Override
    public List<JSONObject> option() {
        return baseMapper.option(SessionUtil.getIsTra());
    }
}
