package com.mrk.yudong.admin.biz.productiontest.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.biz.productiontest.service.ITestEquFirmwareVersionService;
import com.mrk.yudong.admin.infrastructure.producttest.mapper.TestEquFirmwareVersionMapper;
import com.mrk.yudong.admin.infrastructure.producttest.model.TestEquFirmwareVersion;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 固件版本管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@Service
public class TestEquFirmwareVersionServiceImpl extends BaseServiceImpl<TestEquFirmwareVersionMapper, TestEquFirmwareVersion> implements ITestEquFirmwareVersionService {
    @Override
    public IPage<TestEquFirmwareVersion> getPage(IPage<TestEquFirmwareVersion> page, TestEquFirmwareVersion firmwareVersion) {
        return baseMapper.getPage(page,firmwareVersion.getEquipTypeId(),firmwareVersion.getOtaType());
    }

    @Override
    public List<TestEquFirmwareVersion> getHistoryFirmwareVersion(String modelId) {
        List<TestEquFirmwareVersion> list = null;
        if(StrUtil.isNotBlank(modelId)){
            BaseQuery<TestEquFirmwareVersion> baseQuery = new BaseQuery<>();
            baseQuery.eq("equip_model_id",modelId);
            baseQuery.orderByDesc("update_time");
            list = list(baseQuery);
        }
        return list;
    }

    @Override
    public TestEquFirmwareVersion getNewFirmwareVersion(String modelId, String code) {
        return baseMapper.getNewFirmwareVersion(modelId,code);
    }
}
