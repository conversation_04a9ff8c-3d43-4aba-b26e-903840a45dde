package com.mrk.yudong.admin.infrastructure.marketing.gateway;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.api.marketing.dto.cmd.CreateMarketingSalesCmd;
import com.mrk.yudong.admin.api.marketing.dto.cmd.UpdateMarketingSalesCmd;
import com.mrk.yudong.admin.api.marketing.dto.qry.MarketingSalesSearchQry;
import com.mrk.yudong.admin.api.marketing.dto.resp.MarketingSalesInfoDTO;
import com.mrk.yudong.admin.infrastructure.marketing.model.MarketingSales;
import com.mrk.yudong.core.service.BaseService;

import java.time.LocalDateTime;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
public interface IMarketingSalesService extends BaseService<MarketingSales> {
    Boolean create(CreateMarketingSalesCmd cmd);

    Boolean update(UpdateMarketingSalesCmd cmd);
    Boolean hasTimeConflict(LocalDateTime beginTime, LocalDateTime endTime, Long excludeId);

    boolean changeSalesStatus(Long id, Integer status);

    Page<MarketingSalesInfoDTO> getSalesByConditions(MarketingSalesSearchQry searchDTO);


}
