package com.mrk.yudong.admin.infrastructure.motion.dao;

import com.mrk.yudong.admin.infrastructure.motion.model.MotionCategoryRel;

import java.util.List;
import java.util.Set;

public interface MotionCategoryRelDao {
    List<MotionCategoryRel> findByMotionIds(Set<Long> motionId);

    Boolean deleteByMotionId(Long motionId);

    Boolean batchCreate(List<MotionCategoryRel> motionCategoryRelList);

    Set<Long> findMotionIdsByCategoryId(Long categoryId);

    Set<Long> findMotionIdsBySubCategoryId(Long subCategoryId);
}
