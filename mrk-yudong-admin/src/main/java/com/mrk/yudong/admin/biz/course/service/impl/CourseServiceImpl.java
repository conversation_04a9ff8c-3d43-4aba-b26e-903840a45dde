package com.mrk.yudong.admin.biz.course.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.merach.sun.content.api.LiveApi;
import com.merach.sun.content.dto.cmd.live.ActualTimeUpdateCmd;
import com.merach.sun.content.dto.cmd.live.LiveSyncCmd;
import com.merach.sun.misc.api.CategoryApi;
import com.merach.sun.misc.dto.category.resp.CategoryDTO;
import com.merach.sun.misc.form.CategoryFrom;
import com.mrk.yudong.admin.api.category.vo.CategoryVO;
import com.mrk.yudong.admin.api.course.po.*;
import com.mrk.yudong.admin.api.course.query.CourseOptionPageQry;
import com.mrk.yudong.admin.api.course.vo.*;
import com.mrk.yudong.admin.biz.app.service.impl.MusicServiceImpl;
import com.mrk.yudong.admin.biz.course.bo.CourseOptionBO;
import com.mrk.yudong.admin.biz.course.service.*;
import com.mrk.yudong.admin.biz.linestatusrecord.service.LineStatusRecordService;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseMapper;
import com.mrk.yudong.admin.infrastructure.course.model.*;
import com.mrk.yudong.admin.infrastructure.linestatus.model.LineStatusRecord;
import com.mrk.yudong.admin.rabbit.IRabbitOutput;
import com.mrk.yudong.admin.utils.ListUtil;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.share.bo.RaceInfoBO;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.share.constant.ResponseConstant;
import com.mrk.yudong.share.constant.course.CourseConstant;
import com.mrk.yudong.share.constant.equipment.EquipmentConstant;
import com.mrk.yudong.share.enums.linestatus.LineStatusBusinessEnum;
import com.mrk.yudong.share.enums.linestatus.LineStatusEnum;
import com.mrk.yudong.share.po.CourseCataloguePO;
import com.mrk.yudong.share.util.WebsocketSignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 课程表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CourseServiceImpl extends BaseServiceImpl<CourseMapper, Course> implements ICourseService {

    private final ICourseStatusService courseStatusService;

    private final ICourseCatalogueService courseCatalogueService;

    private final ICourseLinkService courseLinkService;

    private final ICourseReviewService courseReviewService;

    private final StringRedisTemplate redisTemplate;

    private final ICourseMakeService courseMakeService;

    private final ICourseVideoService courseVideoService;

    private final IAsyncTaskService asyncTaskService;

    private final ICourseFeedbackService courseFeedbackService;

    private final ICourseTagDetailService courseTagDetailService;

    private final ICourseTagService courseTagService;

    private final ICourseInteractService courseInteractService;

    private final IInteractInfoService interactInfoService;

    private final ICatalogueHotwordService catalogueHotwordService;

    private final IRabbitOutput rabbitOutput;

    private final ICourseMusicService courseMusicService;

    private final ICourseTagRelService courseTagRelService;

    private final MusicServiceImpl musicService;

    private final CategoryApi categoryApi;

    private final ListUtil listUtil;

    private final LiveApi liveApi;
    private final LineStatusRecordService lineStatusRecordService;

    /**
     * 配置课程信息
     *
     * @param coursePO
     * @param courseVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R saveOrUpdate(Course coursePO, CourseVO courseVO) {
        Integer channel = coursePO.getChannel();
        boolean channel3 = CourseConstant.CHANNEL_3.equals(channel);
        LinkedHashSet<TagVO> tagVOS = courseVO.getTagVOS();
        LinkedHashSet<Long> interactIds = courseVO.getInteractIds();

        if (CollUtil.isNotEmpty(tagVOS)) {
            Set<Long> tagIds = tagVOS.stream().map(TagVO::getTagId).collect(Collectors.toSet());
            int count = courseTagService.count("id", ConditionEnum.IN, tagIds);
            if (count != tagIds.size()) {
                return R.paramFail("存在非法标签");
            }
        }
        if (!channel3) {
            if (CollUtil.isNotEmpty(interactIds)) {
                int count = interactInfoService.count("id", ConditionEnum.IN, interactIds);
                if (count < interactIds.size()) {
                    return R.paramFail("存在非法互动词");
                }
            }
        }

        boolean isAdd = coursePO.getId() == null;
        if (StrUtil.isBlank(coursePO.getVideoId())) {
            coursePO.setVideoId(null);
        }

        if (coursePO.getIsVip() == null) {
            coursePO.setIsVip(coursePO.getType().equals(CourseConstant.TYPE_NORMAL) ? BaseConstant.INT_FALSE : BaseConstant.INT_TRUE);
        }

        List<CourseVideo> courseVideos = null;

        if (((CourseConstant.CHANNEL_2.equals(channel) && coursePO.getStatus().equals(CourseConstant.STATUS_50))) && StrUtil.isNotBlank(coursePO.getVideoId())) {
            courseVideos = courseVideoService.list("video_id", ConditionEnum.EQ, coursePO.getVideoId(), "id");
            if (CollUtil.isNotEmpty(courseVideos)) {
                coursePO.setStatus(CourseConstant.STATUS_60);
            }
        }


        if (StrUtil.isNotBlank(courseVO.getPreviewVideo())) {
            coursePO.setPreviewVideo(courseVO.getPreviewVideo());
        }
        if (null != courseVO.getPreviewVideoGenerationType() && courseVO.getPreviewVideoGenerationType() != 0) {
            coursePO.setPreviewVideoGenerationType(courseVO.getPreviewVideoGenerationType());
        }
        boolean saveOrUpdate = this.saveOrUpdate(coursePO);
        if (saveOrUpdate) {
            Long courseId = coursePO.getId();
            Integer preStatus = coursePO.getPreStatus();
            Integer status = coursePO.getStatus();

            if (channel.equals(CourseConstant.CHANNEL_1)) {
                createCourseLive(coursePO);
            }

            if (isAdd || !status.equals(preStatus)) {
                CourseStatus courseStatus = new CourseStatus(courseId, preStatus, status, SessionUtil.getId());
                courseStatusService.save(courseStatus);
            }

            if (CollUtil.isNotEmpty(courseVideos)) {
                courseVideos.forEach(courseVideo -> {
                    courseVideo.setCourseId(courseId);
                    if (channel3) {
                        courseVideo.setType(CourseConstant.VIDEO_TYPE_3);
                    } else {
                        courseVideo.setType(CourseConstant.VIDEO_TYPE_1);
                    }

                });
                courseVideoService.updateBatchById(courseVideos);
            }

            if (CollUtil.isNotEmpty(tagVOS)) {
                courseTagDetailService.remove("course_id", ConditionEnum.EQ, courseId);
                List<CourseTagDetail> tagDetails = tagVOS.stream().map(tagVO -> new CourseTagDetail(courseId, tagVO.getParentId(), tagVO.getTagId())).collect(Collectors.toList());
                courseTagDetailService.saveBatch(tagDetails, tagDetails.size());
            }

            if (CollUtil.isNotEmpty(interactIds)) {
                courseInteractService.remove("course_id", ConditionEnum.EQ, courseId);
                List<CourseInteract> courseInteracts = interactIds.stream().map(interactId -> new CourseInteract(courseId, interactId)).collect(Collectors.toList());
                courseInteractService.saveBatch(courseInteracts, courseInteracts.size());
                String key = RedisKeyConstant.COURSE_INTERACT_LIST_KEY.replace("${courseId}", courseId.toString());
                redisTemplate.delete(key);
            }

            if (channel3) {
                //初始化音乐数据
                courseMusicService.remove("course_id", ConditionEnum.EQ, courseId);
                List<CourseMusic> courseMusicList = courseVO.getCourseMusicList();
                if (CollUtil.isNotEmpty(courseMusicList)) {
                    courseMusicList.forEach(v -> {
                        v.setCourseId(courseId);
                        v.setCreateId(SessionUtil.getId());
                    });
                }
                courseMusicService.saveBatch(courseMusicList);
                //关联训练模式标签
                courseTagRelService.remove("course_id", ConditionEnum.EQ, courseId);
                CourseTagRel courseTagRel = new CourseTagRel();
                courseTagRel.setCourseId(courseId);
                courseTagRel.setCreateId(SessionUtil.getId());
                courseTagRel.setUpdateId(SessionUtil.getId());
                courseTagRel.setTagId(courseVO.getTrainingModeId());
                courseTagRelService.save(courseTagRel);
            }

            List<CourseCatalogueVO> catalogueVOS = courseVO.getCatalogueVOS();
            if (CollUtil.isNotEmpty(catalogueVOS)) {
                courseCatalogueService.remove("course_id", ConditionEnum.EQ, courseId);
                courseLinkService.remove("course_id", ConditionEnum.EQ, courseId);
                catalogueHotwordService.remove("course_id", ConditionEnum.EQ, courseId);

                log.warn("教案参数：{}", JSON.toJSONString(catalogueVOS));
                int sort = 0;
                CourseCatalogue courseCataloguePO = null;
                Long equipmentId = coursePO.getEquipmentId();
                for (CourseCatalogueVO courseCatalogueVO : catalogueVOS) {
                    CourseCatalogue courseCatalogue = this.buildCourseCatalogue(courseCatalogueVO, courseId, sort, courseCataloguePO, equipmentId);
                    courseCatalogueService.save(courseCatalogue);

                    Long courseCatalogueId = courseCatalogue.getId();
                    int endTime = 0;
                    if (courseCataloguePO != null) {
                        endTime = courseCataloguePO.getEndTime();
                    }
                    List<CourseLink> courseLinks = this.buildCourseLink(courseCatalogueVO.getCourseLinkVOS(), courseId, courseCatalogueId, endTime);
                    courseLinkService.saveBatch(courseLinks, courseLinks.size());

                    if (CollUtil.isNotEmpty(courseCatalogueVO.getHotWordIds())) {
                        List<CatalogueHotword> catalogueHotWords = courseCatalogueVO.getHotWordIds().stream().map(hotWordId -> new CatalogueHotword(courseId, courseCatalogueId, hotWordId)).collect(Collectors.toList());
                        catalogueHotwordService.saveBatch(catalogueHotWords, catalogueHotWords.size());
                    }

                    sort++;
                    courseCataloguePO = courseCatalogue;
                }
            }

            String key = RedisKeyConstant.COURSE_CATALOGUE_KEY.replace("${courseId}", courseId.toString());
            String videosKey = RedisKeyConstant.COURSE_VIDEOS_KEY.replace("${courseId}", courseId.toString());
            String linkKey = RedisKeyConstant.COURSE_LINK_KEY.replace("${courseId}", courseId.toString());
            String fatPropKey = RedisKeyConstant.FAT_BURNING_PROP_KEY.replace("${courseId}", courseId.toString());
            redisTemplate.delete(List.of(key, videosKey, linkKey, key + ":SECOND", fatPropKey));

            // 清除课程类型的redis数据
            redisTemplate.delete(RedisKeyConstant.COURSE_NUM);

            return R.ok(courseId);
        }

        return R.fail(ResponseConstant.COMMON_OPERATION_FAILED);
    }

    private void createCourseLive(Course course) {
        LiveSyncCmd liveCmd = new LiveSyncCmd();
        liveCmd.setStartTime(course.getLiveTime());
        liveCmd.setDuration(course.getCourseTime());
        liveCmd.setUserId(course.getCreateId());
        liveCmd.setCourseId(course.getId());
        liveApi.syncLive(liveCmd);
    }

    /**
     * 构建课程教案-环节数据
     *
     * @param courseCatalogueVO
     * @param courseId
     * @param sort
     * @param courseCataloguePO
     * @return
     */
    private CourseCatalogue buildCourseCatalogue(CourseCatalogueVO courseCatalogueVO, Long courseId, int sort, CourseCatalogue courseCataloguePO, Long equipmentId) {
        CourseCatalogue courseCatalogue = new CourseCatalogue();
        courseCatalogue.setCreateId(SessionUtil.getId());

        courseCatalogue.setCourseId(courseId);
        courseCatalogue.setName(courseCatalogueVO.getName());
        courseCatalogue.setSort(sort);
        courseCatalogue.setDescribeInfo(courseCatalogueVO.getDescribeInfo());
        double sumKcal = 0;
        if (!equipmentId.equals(EquipmentConstant.EQUIPMENT_CATEGORY_TYPE_37)) {
            sumKcal = courseCatalogueVO.getCourseLinkVOS().stream().mapToDouble(courseLinkVO -> courseLinkVO.getKcal() == null ? 0.0 : courseLinkVO.getKcal()).sum();
        }
        courseCatalogue.setKcal(sumKcal);
        int sumSustainTime = courseCatalogueVO.getCourseLinkVOS().stream().mapToInt(CourseLinkVO::getSustainTime).sum();
        String beginStr;

        if (sort > 0 && courseCataloguePO != null) {
            courseCatalogue.setBeginTime(courseCataloguePO.getEndTime());
            courseCatalogue.setEndTime(courseCataloguePO.getEndTime() + sumSustainTime);
            int minute = courseCataloguePO.getEndTime() / 60;
            int second = courseCataloguePO.getEndTime() % 60;
            beginStr = minute + ":" + second;
        } else {
            beginStr = "0:0";
            courseCatalogue.setBeginTime(0);
            courseCatalogue.setEndTime(sumSustainTime);
        }

        int minute = courseCatalogue.getEndTime() / 60;
        int second = courseCatalogue.getEndTime() % 60;
        String title = beginStr + "-" + minute + ":" + second;
        courseCatalogue.setTitle(title);

        return courseCatalogue;
    }

    /**
     * 构建课程教案-小节数据
     *
     * @param courseLinkVOS
     * @param courseId
     * @param courseCatalogueId
     * @param endTime
     * @return
     */
    private List<CourseLink> buildCourseLink(List<CourseLinkVO> courseLinkVOS, Long courseId, Long courseCatalogueId, Integer endTime) {
        List<CourseLink> list = new ArrayList<>(courseLinkVOS.size());
        int sort = 0;
        CourseLink courseLinkPO = null;
        for (CourseLinkVO courseLinkVO : courseLinkVOS) {
            CourseLink courseLink = new CourseLink();
            BeanUtil.copyProperties(courseLinkVO, courseLink);
            courseLink.setCreateId(SessionUtil.getId());

            courseLink.setCourseId(courseId);
            courseLink.setCatalogueId(courseCatalogueId);
            courseLink.setSort(sort);
            if (sort > 0) {
                courseLink.setBeginTime(courseLinkPO.getEndTime());
                courseLink.setEndTime(courseLinkVO.getSustainTime() + courseLinkPO.getEndTime());
            } else {
                courseLink.setBeginTime(endTime);
                courseLink.setEndTime(courseLinkVO.getSustainTime() + endTime);
            }
            int minute = courseLink.getBeginTime() / 60;
            int second = courseLink.getBeginTime() % 60;
            String title = minute + ":" + second;
            courseLink.setBeginDesc(title);

            sort++;
            courseLinkPO = courseLink;
            list.add(courseLink);
        }

        return list;
    }

    /**
     * 审核课程
     *
     * @param courseId
     * @param review
     * @param remark
     * @param course
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R review(Long courseId, Integer review, String remark, Course course) {
        Long sysUserId = SessionUtil.getId();
        Course coursePO = new Course();
        coursePO.setId(courseId);
        coursePO.setReviewId(sysUserId);
        coursePO.setReviewRemark(remark);
        coursePO.setReviewEndTime(LocalDateTime.now());
        coursePO.setUpdateId(sysUserId);

        if (review.equals(BaseConstant.INT_FALSE)) {
            coursePO.setStatus(CourseConstant.STATUS_20);
        } else {
            coursePO.setStatus(CourseConstant.STATUS_30);
        }

        boolean update = this.updateById(coursePO);
        if (update) {
            redisTemplate.delete(RedisKeyConstant.TOP3_LIVE_COURSE_KEY);

            CourseStatus courseStatus = new CourseStatus(courseId, course.getStatus(), coursePO.getStatus(), sysUserId);
            courseStatusService.save(courseStatus);

            CourseReview courseReview = new CourseReview(courseId, sysUserId, review, remark);
            courseReviewService.save(courseReview);
            return R.ok();
        }

        return R.fail(ResponseConstant.COMMON_OPERATION_FAILED);
    }

    /**
     * 审核记录列表
     *
     * @param page
     * @param reviewQueryVO
     * @return
     */
    @Override
    public IPage<CoursePO> reviewList(PageDTO<CoursePO> page, ReviewQueryVO reviewQueryVO) {
        return baseMapper.reviewList(page, reviewQueryVO);
    }

    /**
     * 删除课程
     *
     * @param courseId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R remove(Long courseId) {
        boolean remove = this.removeById(courseId);
        if (remove) {
            courseStatusService.remove("course_id", ConditionEnum.EQ, courseId);

            courseCatalogueService.remove("course_id", ConditionEnum.EQ, courseId);

            courseLinkService.remove("course_id", ConditionEnum.EQ, courseId);

            return R.ok();
        }

        return R.fail(ResponseConstant.COMMON_OPERATION_FAILED);
    }

    /**
     * 分页查询课程列表
     *
     * @param page
     * @param courseQueryVO
     * @return
     */
    @Override
    public IPage<CoursePO> query(PageDTO<CoursePO> page, CourseQueryVO courseQueryVO) {
        return baseMapper.query(page, courseQueryVO);
    }

    /**
     * 查询课程列表
     *
     * @param courseQueryVO
     * @return
     */
    @Override
    public List<JSONObject> queryMap(CourseQueryVO courseQueryVO) {
        return baseMapper.queryMap(courseQueryVO);
    }

    /**
     * 课程详情
     *
     * @param courseId
     * @return
     */
    @Override
    public ResDTO<CourseDetailPO> detail(Long courseId) {
        CourseDetailPO courseDetailPO = baseMapper.detail(courseId);
        if (courseDetailPO == null) {
            return ResDTO.fail("无效的课程ID");
        }
        // 设置课程类型名称
        courseDetailPO.setCategoryName(getCategoryName(courseDetailPO.getCategoryId()));

        List<CourseCataloguePO> courseCataloguePOS = courseCatalogueService.listByCourseId(courseDetailPO.getCourseId());
        if (CollUtil.isNotEmpty(courseCataloguePOS)) {
            courseCataloguePOS.forEach(courseCataloguePO -> {
                courseCataloguePO.setCourseLinkPOS(courseLinkService.list(courseDetailPO.getCourseId(), courseCataloguePO.getId()));
                courseCataloguePO.setHotWords(catalogueHotwordService.getHotWordList(courseCataloguePO.getId()));
            });
        }

        courseDetailPO.setCourseCataloguePOS(courseCataloguePOS);
        if (StrUtil.isNotBlank(courseDetailPO.getHotWord())) {
            courseDetailPO.setHotWords(JSON.parseArray(courseDetailPO.getHotWord(), String.class));
        }
        if (StrUtil.isNotBlank(courseDetailPO.getRaceTargetTip())) {
            courseDetailPO.setRaceTargetTips(JSON.parseArray(courseDetailPO.getRaceTargetTip(), Integer.class));
        }

        List<CourseTag> tagList = courseTagDetailService.getTagList(courseId);
        if (CollUtil.isNotEmpty(tagList)) {
            List<JSONObject> tags = tagList.stream().filter(Objects::nonNull).map(courseTag -> {
                JSONObject dict = new JSONObject(4);
                dict.put("id", courseTag.getId());
                dict.put("parentId", courseTag.getParentId());
                dict.put("name", courseTag.getName());
                dict.put("isHide", courseTag.getIsHide());
                return dict;
            }).collect(Collectors.toList());

            courseDetailPO.setTags(tags);
        }

        List<InteractInfo> interactInfos = courseInteractService.getListByCourseId(courseId);
        courseDetailPO.setInteractInfos(interactInfos);

        List<JSONObject> videos = courseVideoService.getVideos(courseId);
        courseDetailPO.setVideos(videos);
        if (courseDetailPO.getChannel().equals(CourseConstant.CHANNEL_3)) {
            BaseQuery<CourseMusic> baseQuery = new BaseQuery<>();
            baseQuery.eq("course_id", courseDetailPO.getCourseId()).orderByAsc("sort");
            List<CourseMusic> courseMusicList = courseMusicService.list(baseQuery);
            if (CollUtil.isNotEmpty(courseMusicList)) {
                List<Long> musicId = courseMusicList.stream().map(CourseMusic::getMusicId).collect(Collectors.toList());
                List<Music> musicList = musicService.listByIds(musicId);
                if (CollUtil.isNotEmpty(musicList)) {
                    List<Music> newMusicList = new ArrayList<>();
                    musicId.forEach(v -> {
                        Music sortMusic = musicList.stream().filter(music -> v.equals(music.getId())).findFirst().orElse(null);
                        newMusicList.add(sortMusic);
                    });
                    courseDetailPO.setCourseMusicList(newMusicList);
                }
            }
        }
        return ResDTO.ok(courseDetailPO);
    }

    /**
     * 准备直播
     *
     * @param course
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R readyLive(Course course) {
        Long courseId = course.getId();
        List<CourseCataloguePO> courseCataloguePOS = courseCatalogueService.listByCourseId(courseId);
        if (CollUtil.isEmpty(courseCataloguePOS) && !EquipmentConstant.isNotNeedCatalogue(course.getEquipmentId())) {
            return R.fail("不满足直播条件");
        }

        courseCataloguePOS.forEach(courseCataloguePO -> courseCataloguePO.setCourseLinkPOS(courseLinkService.list(courseId, courseCataloguePO.getId())));
        String key = RedisKeyConstant.LIVE_PUSH_STREAM_URL_KEY.replace("${courseId}", courseId.toString());
        String pushStreamUrl = (String) redisTemplate.opsForValue().get(key);
        if (ObjectUtil.isNull(pushStreamUrl)) {
            pushStreamUrl = liveApi.getPushStreamUrl("", courseId.toString());
            if (pushStreamUrl == null) {
                return R.fail(ResponseConstant.COMMON_OPERATION_FAILED);
            }
            redisTemplate.opsForValue().set(key, pushStreamUrl, Duration.ofMinutes(25L));
        }

        Course coursePO = new Course();
        coursePO.setId(courseId);
        coursePO.setUpdateId(SessionUtil.getId());
        boolean update = this.updateById(coursePO);
        if (update) {
            Dict data = new Dict(6);
            data.put("name", course.getName());
            data.put("status", course.getStatus());
            data.put("raceStatus", course.getRaceStatus());
            data.put("pushStreamUrl", pushStreamUrl);
            data.put("courseCataloguePOS", courseCataloguePOS);
            String sign = WebsocketSignUtil.sign(courseId.toString());
            data.put("sign", sign);

            return R.ok(data);
        }

        return R.fail(ResponseConstant.COMMON_OPERATION_FAILED);
    }

    /**
     * 开启 / 关闭 直播
     *
     * @param course
     * @param operation
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R live(Course course, Integer operation) {
        Integer preStatus = course.getStatus();
        LocalDateTime actualTime = LocalDateTime.now();
        ActualTimeUpdateCmd actualTimeUpdateCmd = new ActualTimeUpdateCmd();
        actualTimeUpdateCmd.setCourseId(course.getId());
        if (operation.equals(BaseConstant.INT_TRUE)) {
            if (!preStatus.equals(CourseConstant.STATUS_35)) {
                return R.fail("不满足直播条件");
            }

            if (course.getPushStartTime() == null) {
                return R.fail("该课程直播还未进行推流操作，无法正式进入直播");
            }

            course.setStatus(CourseConstant.STATUS_40);
            course.setActualLiveTime(actualTime);
            actualTimeUpdateCmd.setActualStartTime(actualTime);
        } else {
            if (!preStatus.equals(CourseConstant.STATUS_40)) {
                return R.fail("不满足关闭直播条件");
            }

            if (course.getActualLiveTime() == null) {
                return R.fail("尚未开始直播，不满足关闭直播条件");
            }

            course.setStatus(CourseConstant.STATUS_50);
            course.setLiveCloseTime(actualTime);
            actualTimeUpdateCmd.setActualEndTime(actualTime);
        }

        Long sysUserId = SessionUtil.getId();
        course.setUpdateId(sysUserId);
        boolean update = this.updateById(course);
        if (update) {
            redisTemplate.delete(RedisKeyConstant.TOP3_LIVE_COURSE_KEY);

            CourseStatus courseStatus = new CourseStatus(course.getId(), preStatus, course.getStatus(), sysUserId);
            courseStatusService.save(courseStatus);
            asyncTaskService.courseLive(course);

            actualTimeUpdateCmd.setUserId(sysUserId);
            liveApi.updateActualTimeByCourseId(actualTimeUpdateCmd);

            return R.ok();
        }

        return R.fail(ResponseConstant.COMMON_OPERATION_FAILED);
    }

    /**
     * 上线 / 下线 课程
     *
     * @param course
     * @param operation
     * @param message
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R line(Course course, Integer operation, String message) {
        if (operation.equals(BaseConstant.INT_TRUE)) {
            // 上线课程
            course.setLineStatus(BaseConstant.INT_TRUE);
        } else {
            // 下线课程
            course.setLineStatus(BaseConstant.INT_FALSE);
            course.setLineId(SessionUtil.getId());
            course.setLineMsg(message);
            course.setLineTime(LocalDateTime.now());
        }

        boolean update = this.updateById(course);
        if (update) {
            saveLineStatus(course);
            return R.ok();
        }

        return R.fail(ResponseConstant.COMMON_OPERATION_FAILED);
    }

    /**
     * 查询今日直播课
     *
     * @param page
     * @param isTra
     * @param channel
     * @return
     */
    @Override
    public IPage<TodayLivePO> queryTodayLive(PageDTO<TodayLivePO> page, Integer isTra, Integer channel) {
        if (channel == null) {
            channel = CourseConstant.CHANNEL_1;
        }
        return baseMapper.queryTodayLive(page, isTra, channel);
    }

    /**
     * 更换教练
     *
     * @param course
     * @param coachName
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R coach(Course course, String coachName) {
        boolean update = this.updateById(course);
        if (update) {
            return R.ok();
        }
        return R.fail(ResponseConstant.COMMON_OPERATION_FAILED);
    }

    /**
     * 审核课程导出
     *
     * @param reviewQueryVO
     * @return
     */
    @Override
    public List<JSONObject> reviewExport(ReviewQueryVO reviewQueryVO) {
        return baseMapper.reviewExport(reviewQueryVO);
    }

    /**
     * 课程主题查询课程列表
     *
     * @param page
     * @param themeCourseQueryVO
     * @return
     */
    @Override
    public IPage<JSONObject> themeCourseQuery(PageDTO<Course> page, ThemeCourseQueryVO themeCourseQueryVO) {
        return baseMapper.themeCourseQuery(page, themeCourseQueryVO);
    }

    /**
     * 查询审核数量
     *
     * @param isTra
     * @param channel
     * @return
     */
    @Override
    public R reviewCount(Integer isTra, Integer channel) {
        BaseQuery<Course> baseQuery = new BaseQuery<>();
        baseQuery.le("is_tra", isTra).eq("channel", channel).eq("status", CourseConstant.STATUS_10);
        int reviewCount = this.count(baseQuery);
        int count = courseReviewService.count(isTra);

        Dict data = new Dict(2);
        data.put("reviewNum", reviewCount);
        data.put("finishNum", count);
        return R.ok(data);
    }

    /**
     * 直播课数量查询
     *
     * @param isTra
     * @param channel
     * @return
     */
    @Override
    public Map<String, Object> liveCount(Integer isTra, Integer channel) {
        return baseMapper.liveCount(isTra, channel);
    }

    /**
     * 导播修改课程
     *
     * @param coursePDVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R edit(CoursePDVO coursePDVO) {
        boolean isOk = false;
        Long sysUserId = SessionUtil.getId();

        if (StrUtil.isNotBlank(coursePDVO.getVideoId())) {
            Course course = new Course();
            course.setId(coursePDVO.getCourseId());
            course.setVideoId(coursePDVO.getVideoId());
            course.setUpdateId(sysUserId);
            isOk = this.updateById(course);
        }

        if (CollUtil.isNotEmpty(coursePDVO.getLinkPDVOS())) {
            List<LinkPDVO> linkPDVOS = coursePDVO.getLinkPDVOS();
            List<CourseCatalogue> catalogues = courseCatalogueService.list("course_id", ConditionEnum.EQ, coursePDVO.getCourseId(), ConditionEnum.ASC, "begin_time");

            int end = 0;
            CourseCatalogue courseCataloguePO = null;
            for (CourseCatalogue courseCatalogue : catalogues) {
                BaseQuery<CourseLink> baseQuery = new BaseQuery<>();
                baseQuery.eq("course_id", courseCatalogue.getCourseId()).eq("catalogue_id", courseCatalogue.getId());
                baseQuery.orderByAsc("begin_time", "sort");
                List<CourseLink> courseLinks = courseLinkService.list(baseQuery);

                CourseLink courseLinkPO = null;
                for (CourseLink courseLink : courseLinks) {
                    int begin;
                    if (courseLink.getSort() > 0 && courseLinkPO != null) {
                        begin = courseLinkPO.getEndTime();
                    } else {
                        begin = end;
                    }

                    Long courseLinkId = courseLink.getId();
                    for (LinkPDVO linkPDVO : linkPDVOS) {
                        if (!courseLinkId.equals(linkPDVO.getId())) {
                            continue;
                        }

                        courseLink.setSustainTime(linkPDVO.getSustainTime());
                        break;
                    }

                    int endTime = begin + courseLink.getSustainTime();
                    courseLink.setEndTime(endTime);

                    int minute = begin / 60;
                    int second = begin % 60;
                    String title = minute + ":" + second;
                    courseLink.setBeginDesc(title);
                    courseLink.setBeginTime(begin);
                    courseLinkPO = courseLink;
                }

                courseLinkService.updateBatchById(courseLinks);

                String beginStr;
                int sumSustainTime = courseLinks.stream().mapToInt(CourseLink::getSustainTime).sum();
                if (courseCatalogue.getSort() > 0 && courseCataloguePO != null) {
                    courseCatalogue.setBeginTime(courseCataloguePO.getEndTime());
                    courseCatalogue.setEndTime(courseCataloguePO.getEndTime() + sumSustainTime);
                    int minute = courseCataloguePO.getEndTime() / 60;
                    int second = courseCataloguePO.getEndTime() % 60;
                    beginStr = minute + ":" + second;
                } else {
                    beginStr = "0:0";
                    courseCatalogue.setBeginTime(0);
                    courseCatalogue.setEndTime(sumSustainTime);
                }

                int minute = courseCatalogue.getEndTime() / 60;
                int second = courseCatalogue.getEndTime() % 60;
                String title = beginStr + "-" + minute + ":" + second;
                courseCatalogue.setTitle(title);
                courseCatalogue.setUpdateId(sysUserId);

                end = courseCatalogue.getEndTime();
                courseCataloguePO = courseCatalogue;
            }

            isOk = courseCatalogueService.updateBatchById(catalogues);
        }

        if (isOk) {
            Long courseId = coursePDVO.getCourseId();
            BaseQuery<CourseVideo> baseQuery = new BaseQuery<>();
            baseQuery.eq("video_id", coursePDVO.getVideoId()).ne("course_id", courseId).select("id", "course_id");
            List<CourseVideo> videos = courseVideoService.list(baseQuery);
            if (CollUtil.isNotEmpty(videos)) {
                courseVideoService.remove("course_id", ConditionEnum.EQ, courseId);
                videos.forEach(courseVideo -> {
                    courseVideo.setType(CourseConstant.VIDEO_TYPE_1);
                    courseVideo.setCourseId(courseId);
                });
                courseVideoService.updateBatchById(videos, videos.size());
            }

            String key = RedisKeyConstant.COURSE_CATALOGUE_KEY.replace("${courseId}", courseId.toString());
            String videosKey = RedisKeyConstant.COURSE_VIDEOS_KEY.replace("${courseId}", courseId.toString());
            String linkKey = RedisKeyConstant.COURSE_LINK_KEY.replace("${courseId}", courseId.toString());
            List<String> keys = Arrays.asList(key, videosKey, linkKey);
            redisTemplate.delete(keys);
            return R.ok();
        }

        return R.fail(ResponseConstant.COMMON_OPERATION_FAILED);
    }

    /**
     * 查询课程反馈
     *
     * @param courseId
     * @param type
     * @return
     */
    @Override
    public R queryFeedback(Long courseId, Integer type) {
        List<JSONObject> list = courseFeedbackService.query(courseId, type);
        return R.ok(list);
    }

    @Override
    public IPage<JSONObject> queryBuCoachId(PageDTO<Map<String, Object>> page, Long coachId) {
        IPage<JSONObject> mapIPage = baseMapper.queryBuCoachId(page, coachId);

        List<JSONObject> records = mapIPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            records.forEach(dict -> {
                Integer status = MapUtil.getInt(dict, "status");
                int count = MapUtil.getInt(dict, "num", 0);
                if (status.equals(CourseConstant.STATUS_35) || status.equals(CourseConstant.STATUS_40)) {
                    Long id = MapUtil.getLong(dict, "id");
                    String realOnlineKey = RedisKeyConstant.REAL_ONLINE_STATUS_KEY + id;
                    Long realPlaySize = redisTemplate.opsForZSet().size(realOnlineKey);
                    if (realPlaySize == null || realPlaySize < 0) {
                        realPlaySize = 0L;
                    }
                    count = realPlaySize.intValue();
                }
                dict.put("num", count);
            });
        }

        return mapIPage;
    }

    /**
     * 根据课程ID获取标签
     *
     * @param courseId
     * @return
     */
    @Override
    public List<String> getTags(Long courseId) {
        return courseTagDetailService.getTags(courseId);
    }

    /**
     * 查询计划课程列表
     *
     * @param page
     * @param planCourseQueryVO
     * @return
     */
    @Override
    public R queryPlanCourse(PageDTO<PlanCoursePO> page, PlanCourseQueryVO planCourseQueryVO) {
        IPage<PlanCoursePO> planCoursePOIPage = baseMapper.queryPlanCourse(page, planCourseQueryVO);
        List<PlanCoursePO> records = planCoursePOIPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            records.forEach(planCoursePO -> {
                List<String> tags = this.getTags(planCoursePO.getCourseId());
                planCoursePO.setTags(tags);
            });
            planCoursePOIPage.setRecords(records);
        }

        return R.ok(planCoursePOIPage);
    }

    /**
     * 修改课程状态
     *
     * @param courseId
     * @param status
     * @param liveTime
     * @param preStatus
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R updateStatus(Long courseId, Integer status, LocalDateTime liveTime, Integer preStatus) {
        int i = baseMapper.updateCourseStatus(courseId, status, liveTime);
        if (i > 0) {
            CourseStatus courseStatus = new CourseStatus(courseId, preStatus, status, SessionUtil.getId());
            courseStatusService.save(courseStatus);

            String key = RedisKeyConstant.COURSE_SEI_KEY.replace("${courseId}", courseId.toString());
            redisTemplate.delete(key);

            return R.ok();
        }

        return R.fail(ResponseConstant.COMMON_OPERATION_FAILED);
    }

    /**
     * 查询课程统计数据
     *
     * @param page
     * @param courseQueryVO
     * @return
     */
    @Override
    public IPage<CoursePO> queryStatistics(PageDTO<CoursePO> page, CourseQueryVO courseQueryVO) {
        return baseMapper.queryStatistics(page, courseQueryVO);
    }

    /**
     * 查询课程统计数据导出
     *
     * @param courseQueryVO 查询参数
     * @return java.util.List<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @date 2022/3/1 13:11
     */
    @Override
    public List<JSONObject> queryStatisticsExport(CourseQueryVO courseQueryVO) {
        return baseMapper.queryStatisticsExport(courseQueryVO);
    }

    @Override
    public boolean updateActivityId(Set<Long> ids) {
        return baseMapper.updateActivityId(ids);
    }


    /**
     * 复制课程
     *
     * @param course
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R copyCourse(Course course) {
        Long courseId = course.getId();
        Long userId = SessionUtil.getId();
        Integer status = course.getStatus();
        boolean isLive = course.getChannel().equals(CourseConstant.CHANNEL_1);
        if (isLive) {
            // 直播渠道
            long days = course.getLiveTime().until(LocalDateTime.now(), ChronoUnit.DAYS);
            if (!status.equals(CourseConstant.STATUS_60) || days < 7) {
                return R.fail("不满足复制课程条件");
            }

            course.setReviewStartTime(null);
            course.setReviewId(null);
            course.setReviewRemark(null);
            course.setReviewEndTime(null);
        } else {
            if (status < CourseConstant.STATUS_50) {
                return R.fail("不满足复制课程条件");
            }
        }

        boolean isRecording = Objects.equals(course.getChannel(), CourseConstant.CHANNEL_2);

        // 课程重新赋值
        course.setId(null);
        course.setLiveTime(null);
        course.setPushStartTime(null);
        course.setPushEndTime(null);
        course.setActualLiveTime(null);
        course.setLiveCloseTime(null);
        if (!Objects.equals(course.getChannel(), CourseConstant.CHANNEL_2)) {
            course.setVideoId(null);
        }
        course.setRaceStatus(CourseConstant.RACE_STATUS_0);
        course.setRaceStartTime(null);
        course.setRaceEndTime(null);

        course.setTranscribeStartTime(null);
        course.setTranscribeEndTime(null);
        course.setClipStartTime(null);
        course.setClipEndTime(null);
        course.setEncodeTime(null);
        course.setShareNum(0);
        course.setPlayNum(0);
        course.setRobotNum(0);
        course.setRobotPlayNum(0);
        course.setLivePlayNum(0);
        course.setCreateId(userId);
        course.setCreateTime(LocalDateTime.now());
        course.setUpdateId(null);
        course.setUpdateTime(null);
        if (!isRecording) {
            course.setEquipmentId(null);
            course.setModelId(null);
            course.setCoachId(null);
            course.setVideoId(null);
            course.setStatus(CourseConstant.STATUS_0);
        } else {
            course.setLineStatus(BaseConstant.INT_FALSE);
            course.setVideoId(course.getVideoId() + "_" + LocalDateTime.now());
        }

        boolean save = this.save(course);
        if (save) {
            // 添加课程状态流水记录
            Long newCourseId = course.getId();
            CourseStatus courseStatus = new CourseStatus(newCourseId, null, course.getStatus(), userId);
            courseStatusService.save(courseStatus);

            // 复制课程教案
            List<CourseCatalogue> courseCatalogues = courseCatalogueService.list("course_id", ConditionEnum.EQ, courseId, ConditionEnum.ASC, "begin_time");
            if (CollUtil.isNotEmpty(courseCatalogues)) {
                courseCatalogues.forEach(courseCatalogue -> {
                    Long catalogueId = courseCatalogue.getId();
                    courseCatalogue.setId(null);
                    courseCatalogue.setCourseId(newCourseId);
                    courseCatalogue.setCreateId(userId);
                    courseCatalogue.setCreateTime(null);
                    courseCatalogueService.save(courseCatalogue);

                    Long newCatalogueId = courseCatalogue.getId();
                    BaseQuery<CourseLink> baseQuery = new BaseQuery<>();
                    baseQuery.eq("course_id", courseId).eq("catalogue_id", catalogueId).orderByAsc("begin_time", "sort");
                    List<CourseLink> courseLinks = courseLinkService.list(baseQuery);
                    courseLinks.forEach(courseLink -> {
                        courseLink.setId(null);
                        courseLink.setCourseId(newCourseId);
                        courseLink.setCatalogueId(newCatalogueId);
                        courseLink.setCreateId(userId);
                        courseLink.setCreateTime(null);
                    });
                    courseLinkService.saveBatch(courseLinks, courseLinks.size());

                    BaseQuery<CatalogueHotword> condition = new BaseQuery<>();
                    condition.eq("course_id", courseId).eq("catalogue_id", catalogueId);
                    List<CatalogueHotword> catalogueHotwords = catalogueHotwordService.list(condition);
                    if (CollUtil.isNotEmpty(catalogueHotwords)) {
                        catalogueHotwords.forEach(catalogueHotword -> {
                            catalogueHotword.setId(null);
                            catalogueHotword.setCourseId(newCourseId);
                            catalogueHotword.setCatalogueId(newCatalogueId);
                            catalogueHotword.setCreateTime(null);
                        });
                        catalogueHotwordService.saveBatch(catalogueHotwords, catalogueHotwords.size());
                    }
                });
            }

            // 复制课程互动词
            List<CourseInteract> courseInteracts = courseInteractService.list("course_id", ConditionEnum.EQ, courseId);
            if (CollUtil.isNotEmpty(courseInteracts)) {
                courseInteracts.forEach(courseInteract -> {
                    courseInteract.setId(null);
                    courseInteract.setCourseId(newCourseId);
                    courseInteract.setCreateTime(null);
                });
                courseInteractService.saveBatch(courseInteracts, courseInteracts.size());
            }

            // 复制课程标签
            List<CourseTagDetail> tagDetails = courseTagDetailService.list("course_id", ConditionEnum.EQ, courseId);
            if (CollUtil.isNotEmpty(tagDetails)) {
                tagDetails.forEach(courseTagDetail -> {
                    courseTagDetail.setId(null);
                    courseTagDetail.setCourseId(newCourseId);
                });
                courseTagDetailService.saveBatch(tagDetails, tagDetails.size());
            }

            // 赋值视频
            if (Objects.equals(course.getChannel(), CourseConstant.CHANNEL_2)) {
                List<CourseVideo> courseVideos = courseVideoService.list("course_id", ConditionEnum.EQ, courseId);
                if (CollUtil.isNotEmpty(courseVideos)) {
                    courseVideos.forEach(courseVideo -> {
                        courseVideo.setId(null);
                        courseVideo.setCourseId(newCourseId);
                    });

                    courseVideoService.saveBatch(courseVideos, courseVideos.size());
                }
            }
            return R.ok();
        }

        return R.fail("操作失败");
    }

    /**
     * 更新比赛状态
     *
     * @param course  要更新的课程信息
     * @param isStart 是否为开始比赛
     * @return ResDTO<Boolean>
     * <AUTHOR>
     * @date 2022/8/8 15:25
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResDTO<Boolean> race(Course course, boolean isStart) {
        Long courseId = course.getId();
        if (!isStart) {
            // 结束比赛
            LocalDateTime now = LocalDateTime.now();
            Course updateCourse = new Course().setId(courseId).setRaceStatus(CourseConstant.RACE_STATUS_20).setRaceEndTime(now);
            String raceInfoKey = RedisKeyConstant.COURSE_RACE_INFO_KEY.replace("${courseId}", courseId.toString());
            RaceInfoBO raceInfoBO = new RaceInfoBO(updateCourse.getRaceStatus(), course.getRaceTarget()).setRaceEndTime(now).setRaceStartTime(course.getRaceStartTime());
            redisTemplate.opsForValue().set(raceInfoKey, JSON.toJSONString(raceInfoBO), Duration.ofHours(1L));

            rabbitOutput.userCourseRaceOutput().send(MessageBuilder.withPayload(courseId).build());
            return ResDTO.ok(this.updateById(updateCourse));
        }

        // 开始比赛，设置倒计时
        String startKey = RedisKeyConstant.COURSE_RACE_START_KEY.replace("${courseId}", courseId.toString());
        redisTemplate.opsForValue().set(startKey, courseId.toString(), Duration.ofSeconds(CourseConstant.COUNTDOWN_SECONDS));
        // 计算自动关闭比赛时间段
        List<CourseCatalogue> courseCataloguePOS = courseCatalogueService.list("course_id", ConditionEnum.EQ, courseId, ConditionEnum.ASC, "begin_time");
        if (CollUtil.isEmpty(courseCataloguePOS)) {
            log.warn("=== courseId: {} courseCataloguePOS isEmpty. 无法自动检测关闭比赛 ===", courseId);
            return ResDTO.ok(true);
        }

        if (courseCataloguePOS.size() < CourseConstant.DEFAULT_COURSE_CATALOGUE_SIZE) {
            log.warn("=== courseId: {} 环节数量小于默认环节数量. 无法自动检测关闭比赛 ===", courseId);
            return ResDTO.ok(true);
        }

        LocalDateTime actualLiveTime = course.getActualLiveTime();
        if (actualLiveTime == null) {
            log.warn("=== courseId: {} actualLiveTime is null. 无法自动检测关闭比赛 ===", courseId);
            return ResDTO.ok(true);
        }

        // 获取倒数第二节环节信息
        CourseCatalogue courseCatalogue = courseCataloguePOS.get(CourseConstant.DEFAULT_COURSE_CATALOGUE_SIZE - 2);
        String key = RedisKeyConstant.COURSE_RACE_CLOSE_KEY.replace("${courseId}", courseId.toString());
        long seconds = courseCatalogue.getBeginTime().longValue() - actualLiveTime.until(LocalDateTime.now(), ChronoUnit.SECONDS);
        if (seconds > 0) {
            redisTemplate.opsForValue().set(key, courseId.toString(), Duration.ofSeconds(seconds));
        }
        return ResDTO.ok(true);
    }

    /**
     * @param pageForm
     * @return
     */
    @Override
    public PageDTO<CategoryVO> queryPage(CategoryFrom pageForm) {
        com.merach.sun.common.layer.web.PageDTO<CategoryDTO>
                page = categoryApi.pageCategories(pageForm);
        List<CategoryVO> list = new ArrayList<>();
        page.getRecords().forEach(v -> {
            CategoryVO response = new CategoryVO();
            BeanUtil.copyProperties(v, response);
            response.setCourseNum(getCoueseNumById(v.getId()));
            list.add(response);
        });
        // 更新创建人信息
        listUtil.fillUserName(list, CategoryVO::getCreateId, CategoryVO::setCreateName);
        //返回
        PageDTO pageDTO = new PageDTO();
        BeanUtil.copyProperties(page, pageDTO);
        pageDTO.setRecords(list);
        return pageDTO;
    }

    /**
     * 根据courseId获取人名字
     *
     * @param categoryId
     * @return
     */
    @Override
    public String getCategoryName(Long categoryId) {
        if (categoryId == null) {
            return "";
        }
        try {
            String key = String.format(RedisKeyConstant.CATEGORY_CATEGORY_ID, categoryId);
            Object object = redisTemplate.opsForValue().get(key);
            if (!ObjectUtil.isEmpty(object)) {
                // 先从redis中获取返回
                String str = String.valueOf(object);
                CategoryDTO categoryAllDTO = JSON.parseObject(str, CategoryDTO.class);
                if (ObjectUtil.isEmpty(categoryAllDTO)) {
                    return "";
                }
                return categoryAllDTO.getName();
            }
            // 数据库获取Name
            CategoryDTO category = categoryApi.getCategoryById(categoryId);
            if (ObjectUtil.isEmpty(category)) {
                return "";
            }
            //更新redis
            redisTemplate.opsForValue().set(key, JSON.toJSONString(category), Duration.ofDays(1L));
            return category.getName();
        } catch (Exception e) {
            log.error("获取课程名称失败:e " + e);
        }
        return "";
    }

    /**
     * 判断课程表中是否有课程id数据
     *
     * @param categoryId
     * @return
     */
    @Override
    public Boolean isExistByCategoryId(Long categoryId) {
        QueryWrapper<Course> wrapper = new QueryWrapper<>();
        wrapper.eq("category_id", categoryId);
        List<Course> courses = this.baseMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(courses)) {
            return false;
        }
        return true;
    }

    @Override
    public IPage<CourseOptionBO> pageCourseOptions(PageDTO<Course> page, CourseOptionPageQry qry) {
        return this.baseMapper.pageCourseOptions(page, qry);
    }

    /**
     * 根据id获取课程数量
     *
     * @param id
     * @return
     */
    private Long getCoueseNumById(Long id) {
        try {
            // 其中key=category_id（课程类型） value=数量
            List<CourseNumPO> courseNumPOS = getAllCourseNum();
            // 找id就返回
            for (CourseNumPO course : courseNumPOS) {
                if (course.getCourseId().equals(id)) {
                    return course.getCourseNum();
                }
            }
        } catch (Exception e) {
            log.error("获取课程数量失败:e " + e);
            System.out.println(e);
        }
        return 0L;
    }

    /**
     * 获取所有类型的课程数量
     *
     * @return
     */
    public List<CourseNumPO> getAllCourseNum() {
        List<CourseNumPO> list = new ArrayList<>();
        Object object = redisTemplate.opsForValue().get(RedisKeyConstant.COURSE_NUM);
        if (!ObjectUtil.isEmpty(object)) {
            // 先从redis中获取返回
            return JSON.parseArray(object.toString(), CourseNumPO.class);
        }
        // 数据库获取
        list = this.baseMapper.getAllCourseNum();
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        //更新redis
        redisTemplate.opsForValue().set(RedisKeyConstant.COURSE_NUM, JSON.toJSONString(list), Duration.ofMinutes(5L));
        return list;
    }

    private void saveLineStatus(Course course) {
        try {
            log.info("保存课程上下线状态:{}", course);
            LineStatusRecord createLineStatusRecordCmd = new LineStatusRecord();
            if (course.getLineStatus().equals(BaseConstant.INT_FALSE)) {
                createLineStatusRecordCmd.setLineStatus(LineStatusEnum.OFFLINE.getCode());
            } else {
                createLineStatusRecordCmd.setLineStatus(LineStatusEnum.ONLINE.getCode());
            }
            createLineStatusRecordCmd.setBusinessId(course.getId());
            createLineStatusRecordCmd.setBusinessType(LineStatusBusinessEnum.COURSE.getCode());
            lineStatusRecordService.createLineStatusRecord(createLineStatusRecordCmd);
        } catch (Exception e) {
            log.error("保存课程上下线状态异常:{}", e);
        }
    }
}

