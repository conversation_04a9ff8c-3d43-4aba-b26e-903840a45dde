package com.mrk.yudong.admin.infrastructure.user.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 兑换图片表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ExchangeImage extends Model<ExchangeImage> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 兑换配置ID
     */
    private Long exchangeId;

    /**
     * 图片地址
     */
    private String url;

    /**
     * 类型：1-订单，2-兑换
     */
    private Integer type;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public ExchangeImage(Long exchangeId, String url, Integer type, Long createId) {
        this.exchangeId = exchangeId;
        this.url = url;
        this.type = type;
        this.createId = createId;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
