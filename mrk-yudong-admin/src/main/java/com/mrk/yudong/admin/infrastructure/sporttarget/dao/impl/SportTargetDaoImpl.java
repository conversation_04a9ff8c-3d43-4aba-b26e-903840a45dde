package com.mrk.yudong.admin.infrastructure.sporttarget.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mrk.yudong.admin.infrastructure.sporttarget.dao.SportTargetDao;
import com.mrk.yudong.admin.infrastructure.sporttarget.mapper.SportTargetMapper;
import com.mrk.yudong.admin.infrastructure.sporttarget.model.SportTarget;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SportTargetDaoImpl implements SportTargetDao {
    private final SportTargetMapper sportTargetMapper;

    @Override
    public SportTarget getSportTargetByUserIdAndDay(Long userId, String day) {
        LambdaQueryWrapper<SportTarget> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SportTarget::getUserId, userId);
        queryWrapper.eq(SportTarget::getDay, day);
        queryWrapper.last("limit 1");
        return sportTargetMapper.selectOne(queryWrapper);
    }

    @Override
    public SportTarget updateSportTarget(SportTarget sportTarget) {
        sportTargetMapper.updateById(sportTarget);
        return sportTarget;
    }

}
