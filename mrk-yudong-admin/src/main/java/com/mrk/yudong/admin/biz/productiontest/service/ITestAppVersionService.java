package com.mrk.yudong.admin.biz.productiontest.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.infrastructure.producttest.model.TestAppVersion;
import com.mrk.yudong.admin.infrastructure.producttest.vo.AppVersionQueryVO;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.service.BaseService;

import java.util.List;

/**
 * <p>
 * app版本控制表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
public interface ITestAppVersionService extends BaseService<TestAppVersion> {
    /**
     * 根据终端获取最新版本信息
     *
     * @param terminal
     * @return
     */
    TestAppVersion getTop(Integer terminal);
    /**
     * 查询版本列表
     *
     * @param page
     * @param appVersionQueryVO
     * @return
     */
    IPage<TestAppVersion> query(PageDTO<TestAppVersion> page, AppVersionQueryVO appVersionQueryVO);

    /**
     * 获取选择数据字典
     *
     * @return
     */
    List<JSONObject> option();
}
