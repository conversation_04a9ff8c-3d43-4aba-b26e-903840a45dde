package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 课程标签表(中台)
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TagCourse extends Model<TagCourse> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模块code
     */
    private String moduleCode;

    /**
     * tag分类id,来自表t_tag_category主键
     */
    private Long tagCategoryId;

    /**
     * tag编码
     */
    private String tagCode;

    /**
     * tag名称
     */
    private String tagName;

    /**
     * 状态 0:下架 1:上架
     */
    private Integer tagStatus;

    /**
     * 排序，从小到大，默认999
     */
    private Integer seq;

    /**
     * remark
     */
    private String remark;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除 0:未删除  1:已删除
     */
    private Integer isDelete;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
