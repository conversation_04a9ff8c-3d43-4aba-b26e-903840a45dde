package com.mrk.yudong.admin.biz.equipment.service.impl;

import com.mrk.yudong.admin.biz.equipment.service.IEquipmentTypeService;
import com.mrk.yudong.admin.infrastructure.equipment.mapper.EquipmentTypeMapper;
import com.mrk.yudong.admin.infrastructure.equipment.model.EquipmentType;
import com.mrk.yudong.core.service.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-18
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EquipmentTypeServiceImpl extends BaseServiceImpl<EquipmentTypeMapper, EquipmentType> implements IEquipmentTypeService {


}
