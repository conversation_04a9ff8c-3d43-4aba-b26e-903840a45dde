package com.mrk.yudong.admin.biz.coursepackage.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.merach.sun.common.layer.web.PageForm;
import com.mrk.yudong.admin.api.coursepackage.dto.series.cmd.CreateCourseSeriesCmd;
import com.mrk.yudong.admin.api.coursepackage.dto.series.cmd.UpdateCourseSeriesCmd;
import com.mrk.yudong.admin.api.coursepackage.dto.series.resp.CourseSeriesDTO;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CourseSeries;
import com.mrk.yudong.core.service.BaseService;

/**
 * <p>
 * 系列课程 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
public interface ICourseSeriesService extends BaseService<CourseSeries> {
    Boolean createCourseSeries(CreateCourseSeriesCmd courseSeries);
    CourseSeriesDTO getCourseSeries(Long id);
    Boolean updateCourseSeries(UpdateCourseSeriesCmd courseSeries);
    Boolean updateCourseSeriesStatus(Long id, Integer status);
    Boolean deleteCourseSeries(Long id);
    Page<CourseSeriesDTO> pageCourseSeries(PageForm pageForm);
}
