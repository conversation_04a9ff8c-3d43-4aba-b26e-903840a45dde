package com.mrk.yudong.admin.infrastructure.tag.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class UserTag {

    private Long id;

    private String code;

    private String name;

    private Integer needCache;

    private Integer enable;

    private LocalDateTime createTime;

    private Long createId;

    private LocalDateTime updateTime;

    private Long updateId;



}
