package com.mrk.yudong.admin.infrastructure.content.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.content.model.LiveVideo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 实景视频 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-05
 */
@Mapper
public interface LiveVideoMapper extends BaseMapper<LiveVideo> {
    /**
     * 修改banner上下线状态
     * @param id
     * @param oper 1上线，2下线
     * @return
     */
    boolean updateOnlineStatus(@Param("id") Long id, @Param("oper") Integer oper);

}
