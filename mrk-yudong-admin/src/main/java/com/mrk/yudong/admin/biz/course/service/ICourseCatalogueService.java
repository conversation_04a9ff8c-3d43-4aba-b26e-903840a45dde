package com.mrk.yudong.admin.biz.course.service;

import com.mrk.yudong.admin.infrastructure.course.model.CourseCatalogue;
import com.mrk.yudong.core.service.BaseService;
import com.mrk.yudong.share.po.CourseCataloguePO;

import java.util.List;

/**
 * <p>
 * 课程目录（课程环节）表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
public interface ICourseCatalogueService extends BaseService<CourseCatalogue> {

    /**
     * 根据课程ID获取课程教案环节信息列表
     *
     * @param courseId
     * @return
     */
    List<CourseCataloguePO> listByCourseId(Long courseId);

}
