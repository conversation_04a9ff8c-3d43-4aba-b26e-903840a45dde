package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseTag;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 课程标签表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
public interface CourseTagMapper extends BaseMapper<CourseTag> {

    /**
     * 根据父级ID查询标签
     *
     * @param parentId 父级ID
     * @param isTra    是否为预发环境
     * @return java.util.List<com.mrk.yudong.admin.course.model.CourseTag>
     * <AUTHOR>
     * @date 2022/2/8 16:58
     */
    List<CourseTag> query(@Param("parentId") Long parentId, @Param("isTra") Integer isTra);

}
