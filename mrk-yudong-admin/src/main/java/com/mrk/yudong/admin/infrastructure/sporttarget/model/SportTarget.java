package com.mrk.yudong.admin.infrastructure.sporttarget.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_sport_target")
public class SportTarget extends Model<SportTarget> {
    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 目标消耗
     */
    private BigDecimal targetCalorie;

    /**
     * 实际消耗
     */
    private BigDecimal calorie;


    /**
     * 目标时间:秒
     */
    private Long targetTime;

    /**
     * 实际时间:秒
     */
    private Long time;

    /**
     * 完成状态
     */
    private Integer status;

    /**
     * 是否弹窗
     */
    private Integer isPop;

    private String day;

    /**
     * 身高，单位：CM
     */
    private BigDecimal height;

    /**
     * 体重，单位：kg
     */
    private BigDecimal weight;

    /**
     * BMI
     */
    private BigDecimal bmi;

    private Integer totalExerciseDay;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
