package com.mrk.yudong.admin.infrastructure.coursepackage.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CourseThemeDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 课程 - 课程主题 中间关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
public interface CourseThemeDetailMapper extends BaseMapper<CourseThemeDetail> {

    List<JSONObject> queryCourse(@Param("themeId") Long themeId);

}
