package com.mrk.yudong.admin.infrastructure.motion.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
public class MotionCategoryRel extends Model<MotionCategoryRel> {
    /**
     * 开发主键
     */
    private Long id;

    /**
     * 动作ID
     */
    private Long motionId;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 子分类ID
     */
    private Long subCategoryId;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    private Integer isDelete;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
