package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.ResponseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 训练计划
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CourseTrainingPlan extends Model<CourseTrainingPlan> {


    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 设备类型id
     */
    @NotNull(message = ResponseConstant.EQUIP_SELECT_DEVICE_TYPE)
    private Long equipTypeId;

    /**
     * 计划标题
     */
    @NotBlank(message = "请填写计划标题")
    @Length(min = 2, max = 64, message = "计划标题,请输入2-64个字符，支持中英文、数字组成")
    private String title;

    /**
     * 计划副标题
     */
    @NotBlank(message = "请填写计划副标")
    @Length(min = 2, max = 40, message = "计划副标,请输入2-40个字符，支持中英文、数字组成")
    private String subtitle;

    /**
     * 计划标签id，以,分割，最多选择3个
     */
    @NotBlank(message = "请选择计划标签")
    private String tagId;
    /**
     * 使用类型1.计划2.活动
     */
//    @NotNull(message = "请选择使用类型")
    private Integer useType;
    /**
     * 难度id
     */
    @NotNull(message = "请选择计划难度")
    private Integer difficultyId;
    /**
     * 难度
     */
    @TableField(exist = false)
    private String difficultyName;
    /**
     * 是否在app显示 1是0否
     */
//    @NotNull(message = "请选择是否在app显示")
    private Integer isShow;

    /**
     * 主图地址
     */
    @NotBlank(message = "请上传计划主图")
    private String mainFigure;

    /**
     * 训练简介
     */
    @NotBlank(message = "请填写计划训练简介")
    @Length(min = 10, max = 200, message = "计划训练简介,请输入10-200个字符，支持中英文、数字组成")
    private String introduction;

    /**
     * 训练建议
     */
    @NotBlank(message = "请填写计划训练建议")
    @Length(min = 10, max = 100, message = "计划训练建议,请输入10-100个字符，支持中英文、数字组成")
    private String advice;

    /**
     * 图文详情
     */
//    @NotBlank(message = "请上传图文详情")
    private String graphicDetails;

    /**
     * 上线时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime upTime;

    /**
     * 下线时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime downTime;

    /**
     * 在线状态1未上线，2进行中，3已下线
     */
    private Integer onlineStatus;

    /**
     * 是否长时间展示1是0否
     */
    private Integer isLongTime;

    /**
     * 是否需要vip 1是0否
     */
    @NotNull(message = "请填写排序信息")
    private Integer isVip;

    /**
     * 品牌类型：1-Merit，2-绝影
     */
    @NotNull(message = "请选择品牌类型")
    private Integer brandType;

    /**
     * 会员类型：10-VIP、20-SVIP、30-绝影会员
     */
    private Integer vipType;

    /**
     * 排序
     */
    @NotNull(message = "请填写排序信息")
    private Integer sort;

    /**
     * 计划周期
     */
    @NotNull(message = "请选择计划周期")
    private Integer planningCycle;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime operationTime;

    /**
     * 修改人id
     */
    private Long updateBy;

    /**
     * 课程关联信息
     */
    @TableField(exist = false)
    private List<CoursePlanAssociated> coursePlanAssociatedList;
    /**
     * 设备名称
     */
    @TableField(exist = false)
    private String equipmentTypeName;
    /**
     * 操作人名称
     */
    @TableField(exist = false)
    private String operationByName;
    /**
     * 课程数量
     */
    @TableField(exist = false)
    private Integer count;
    /**
     * 课程标签集合
     */
    @TableField(exist = false)
    private List<CourseTag> courseTagList;
    /**
     * 课程标签
     */
    @TableField(exist = false)
    private List<String> tagName;
    /**
     * 预发
     */
    private Integer isTra;
}
