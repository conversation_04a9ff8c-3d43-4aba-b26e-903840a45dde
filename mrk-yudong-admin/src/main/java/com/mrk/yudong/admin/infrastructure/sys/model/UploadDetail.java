package com.mrk.yudong.admin.infrastructure.sys.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 上传文件明细
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-25
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class UploadDetail extends Model<UploadDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 上传人ID
     */
    private Long uploadId;

    /**
     * 用户类型：1-APP用户，2-后台用户
     */
    private Integer userType;

    /**
     * 存储桶
     */
    private String bucket;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件原始名称
     */
    private String originalName;

    /**
     * 文件大小
     */
    private Long size;

    /**
     * 文件格式
     */
    private String type;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public UploadDetail(Long uploadId, Integer userType, String bucket, String fileName, String originalName, Long size, String type, String fileUrl) {
        this.uploadId = uploadId;
        this.userType = userType;
        this.bucket = bucket;
        this.fileName = fileName;
        this.originalName = originalName;
        this.size = size;
        this.type = type;
        this.fileUrl = fileUrl;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
