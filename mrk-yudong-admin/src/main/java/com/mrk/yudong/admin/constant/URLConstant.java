package com.mrk.yudong.admin.constant;

/**
 * 钉钉url地址
 * <AUTHOR>
 * @create 2021−05-27 1:41 下午
 */
public class URLConstant {
    /**
     * 获取企业内部应用的access_token
     */
    public static final String URL_GET_TOKKEN = "https://oapi.dingtalk.com/gettoken";

    /**
     * 通过临时授权码获取授权用户的个人信息
     */
    public static final String URL_GET_USER_INFO_BYCODE = "https://oapi.dingtalk.com/sns/getuserinfo_bycode";

    /**
     * 根据unionid获取userid
     */
    public static final String GET_BY_UNIONID ="https://oapi.dingtalk.com/topapi/user/getbyunionid";
    /**
     * 获取用户姓名的接口url
     */
    public static final String URL_USER_GET = "https://oapi.dingtalk.com/topapi/v2/user/get";

    /**
     * 机器人webhook
     */
    public static final String ROBOT = "https://oapi.dingtalk.com/robot/send?access_token=33273d33efafc9511942ca9af5d63664d0c5118705d5acf2c8c48b2f442765b0";

    /**
     * 获取部门列表
     */
    public static final String DEPARTMENT_LIST = "https://oapi.dingtalk.com/topapi/v2/department/listsub";

    /**
     *获取部门详情
     */
    public static final String DEPARTMENT_INFO = "https://oapi.dingtalk.com/topapi/v2/department/get";


}