package com.mrk.yudong.admin.biz.exclusive.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.merach.sun.misc.enums.ContentSkipEnum;
import com.merach.sun.user.api.ExclusiveApi;
import com.merach.sun.user.api.ExclusiveGroupApi;
import com.merach.sun.user.dto.exclusive.ExclusiveDTO;
import com.merach.sun.user.dto.exclusive.cmd.ExclusiveCmd;
import com.merach.sun.user.dto.exclusive.qry.ExclusiveGroupQry;
import com.merach.sun.user.dto.exclusive.qry.ExclusiveQry;
import com.merach.sun.user.dto.level.ExclusiveGroupDTO;
import com.merach.sun.user.enums.vip.ExclusiveTypeEnum;
import com.mrk.yudong.admin.api.exclusive.dto.ExclusiveDataDTO;
import com.mrk.yudong.admin.api.exclusive.dto.ExclusiveOptionDTO;
import com.mrk.yudong.admin.api.exclusive.query.ExclusiveQuery;
import com.mrk.yudong.admin.api.exclusive.vo.ExclusiveVo;
import com.mrk.yudong.admin.biz.exclusive.service.IExclusiveService;
import com.mrk.yudong.admin.biz.sys.service.ISysUserService;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.vo.common.OptionVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/10/17 16:37
 */
@RequiredArgsConstructor
@Service
public class ExclusiveServiceImpl implements IExclusiveService {

    private final ExclusiveGroupApi exclusiveGroupApi;

    private final ExclusiveApi exclusiveApi;

    private final ISysUserService sysUserService;

    @Override
    public PageDTO<ExclusiveDataDTO> page(ExclusiveQuery query) {
        ExclusiveQry exclusiveQry = BeanUtil.copyProperties(query, ExclusiveQry.class);
        exclusiveQry.setId(query.getExclusiveId());
        com.merach.sun.common.layer.web.PageDTO<ExclusiveDTO> page = exclusiveApi.page(exclusiveQry);
        List<ExclusiveDTO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageDTO.of(query.getCurrent(), query.getSize(), page.getTotal());
        }

        Set<Long> userIds = records.stream().flatMap(v -> Stream.of(v.getCreateId(), v.getUpdateId())).collect(Collectors.toSet());
        Map<Long, String> userNameData = sysUserService.getOperationUserNameData(userIds);

        Set<Long> groupIds = records.stream().map(ExclusiveDTO::getGroupId).collect(Collectors.toSet());
        Map<Long, String> nameDataMap = exclusiveGroupApi.getNameDataMap(groupIds);

        List<ExclusiveDataDTO> list = records.stream().map(v -> {
            ExclusiveDataDTO dataDTO = BeanUtil.copyProperties(v, ExclusiveDataDTO.class);
            Optional.ofNullable(userNameData.get(v.getCreateId())).ifPresent(dataDTO::setCreateName);
            Optional.ofNullable(userNameData.get(v.getUpdateId())).ifPresent(dataDTO::setUpdateName);
            Optional.ofNullable(nameDataMap.get(v.getGroupId())).ifPresent(dataDTO::setGroupName);

            Optional.ofNullable(ExclusiveTypeEnum.get(v.getType())).ifPresent(e -> dataDTO.setTypeName(e.getDesc()));
            Optional.ofNullable(ContentSkipEnum.get(v.getContentType())).ifPresent(e -> dataDTO.setContentTypeName(e.getName()));
            return dataDTO;
        }).collect(Collectors.toList());

        return PageDTO.of(query.getCurrent(), query.getSize(), page.getTotal(), list);
    }

    @Override
    public ExclusiveOptionDTO option(Integer type) {
        ExclusiveOptionDTO optionDTO = new ExclusiveOptionDTO();
        optionDTO.setTypes(Stream.of(ExclusiveTypeEnum.values()).map(v -> new OptionVo(v.getDesc(), v.getType().toString())).collect(Collectors.toList()));
        optionDTO.setContentTypes(Stream.of(ContentSkipEnum.values()).map(v -> new OptionVo(v.getName(), v.getCode())).collect(Collectors.toList()));

        ExclusiveGroupQry qry = new ExclusiveGroupQry();
        qry.setStatus(BaseConstant.INT_TRUE).setType(type);
        List<ExclusiveGroupDTO> list = exclusiveGroupApi.list(qry);
        optionDTO.setGroupItems(list.stream().map(v -> new OptionVo(v.getName(), v.getGroupId().toString())).collect(Collectors.toList()));
        return optionDTO;
    }

    @Override
    public ResDTO<Boolean> save(ExclusiveVo exclusiveVo) {
        ResDTO<ExclusiveGroupDTO> resDTO = this.checkParam(exclusiveVo);
        if (ResDTO.isFail(resDTO)) {
            return ResDTO.fail(resDTO.getStatus(), resDTO.getMessage());
        }

        ExclusiveCmd exclusiveCmd = this.buildExclusiveCmd(exclusiveVo, resDTO.getData());
        exclusiveCmd.setCreateId(SessionUtil.getId());
        return exclusiveApi.save(exclusiveCmd) ? ResDTO.ok(true) : ResDTO.fail();
    }

    private ResDTO<ExclusiveGroupDTO> checkParam(ExclusiveVo exclusiveVo) {
        ExclusiveTypeEnum typeEnum = ExclusiveTypeEnum.get(exclusiveVo.getType());
        if (typeEnum == null) {
            return ResDTO.paramFail("无效的分组类型");
        }

//        if (typeEnum == ExclusiveTypeEnum.VIP_BENEFIT) {
//            if (StrUtil.isBlank(exclusiveVo.getSubTitle())) {
//                return ResDTO.paramFail("副标题不能为空");
//            }
//
//            if (StrUtil.isBlank(exclusiveVo.getIcon())) {
//                return ResDTO.paramFail("权益icon不能为空");
//            }
//        }

        String contentType = exclusiveVo.getContentType();
        String content = exclusiveVo.getContent();
        if (StrUtil.isNotBlank(contentType)) {
            if (ContentSkipEnum.notHas(contentType)) {
                return ResDTO.paramFail("无效的链接跳转方式");
            }

            if (StrUtil.isBlank(content)) {
                return ResDTO.paramFail("请配置链接跳转内容");
            }
        }

        if (StrUtil.isNotBlank(content) && StrUtil.isBlank(contentType)) {
            return ResDTO.paramFail("请选择链接跳转方式");
        }

        Long groupId = exclusiveVo.getGroupId();
        ExclusiveGroupDTO groupDTO = exclusiveGroupApi.getById(groupId);
        if (groupDTO.getGroupId() == null) {
            return ResDTO.fail("无效的分组信息");
        }

        if (!Objects.equals(groupDTO.getType(), typeEnum.getType())) {
            return ResDTO.paramFail("不匹配的分组类型");
        }

        if (!Objects.equals(groupDTO.getStatus(), BaseConstant.INT_TRUE)) {
            return ResDTO.paramFail("仅支持选择启用状态的分组");
        }

        return ResDTO.ok(groupDTO);
    }

    private ExclusiveCmd buildExclusiveCmd(ExclusiveVo exclusiveVo, ExclusiveGroupDTO groupDTO) {
        ExclusiveCmd exclusiveCmd = BeanUtil.copyProperties(exclusiveVo, ExclusiveCmd.class);
        exclusiveCmd.setCode(groupDTO.getCode());
        exclusiveCmd.setUpdateId(SessionUtil.getId());
        return exclusiveCmd;
    }

    @Override
    public ResDTO<Boolean> update(Long exclusiveId, ExclusiveVo exclusiveVo) {
        ResDTO<ExclusiveGroupDTO> resDTO = this.checkParam(exclusiveVo);
        if (ResDTO.isFail(resDTO)) {
            return ResDTO.fail(resDTO.getStatus(), resDTO.getMessage());
        }

        if (!exclusiveApi.hasData(exclusiveId)) {
            return ResDTO.fail("无效的权益信息");
        }

        ExclusiveCmd exclusiveCmd = this.buildExclusiveCmd(exclusiveVo, resDTO.getData());
        return exclusiveApi.update(exclusiveId, exclusiveCmd) ? ResDTO.ok(true) : ResDTO.fail();
    }

    @Override
    public ResDTO<Boolean> remove(Long exclusiveId) {
        if (!exclusiveApi.hasData(exclusiveId)) {
            return ResDTO.fail("无效的权益信息");
        }

        if (exclusiveApi.hasUse(exclusiveId)) {
            return ResDTO.fail("该特权已被使用，无法删除");
        }

        return exclusiveApi.remove(exclusiveId, SessionUtil.getId()) ? ResDTO.ok(true) : ResDTO.fail();
    }

}
