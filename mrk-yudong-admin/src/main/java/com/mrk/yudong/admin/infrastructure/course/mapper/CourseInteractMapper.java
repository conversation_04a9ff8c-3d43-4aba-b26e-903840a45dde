package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseInteract;
import com.mrk.yudong.admin.infrastructure.course.model.InteractInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 课程互动词表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
public interface CourseInteractMapper extends BaseMapper<CourseInteract> {

    List<InteractInfo> getListByCourseId(@Param("courseId") Long courseId);

}
