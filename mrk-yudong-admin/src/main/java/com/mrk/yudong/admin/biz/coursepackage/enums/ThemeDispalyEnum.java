package com.mrk.yudong.admin.biz.coursepackage.enums;

import com.mrk.yudong.share.vo.common.OptionVo;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum ThemeDispalyEnum {
    HOMEPAGE_RECOMMEND(2, "设备板块"),
    ULTRA_BURNING(3, "超燃脂自由练板块"),
    ULTRA_BURNING_SPECIAL(4, "超燃脂自由练专项板块"),
    NO_LOCATION(5, "无固定位置展示");
    private final Integer type;
    private final String desc;

    public static List<OptionVo> findAllDispalys() {
        ThemeDispalyEnum[] enumValues = ThemeDispalyEnum.values();
        List<OptionVo> enumItemList = new ArrayList<>();

        for (ThemeDispalyEnum enumValue : enumValues) {
            enumItemList.add(new OptionVo(enumValue.getDesc(), enumValue.getType().toString()));
        }
        return enumItemList;
    }

    public static String getLocationDescByType(Integer type) {
        for (ThemeDispalyEnum enumValue : ThemeDispalyEnum.values()) {
            if (enumValue.type.equals(type)) {
                return enumValue.desc;
            }
        }
        return null;
    }
}
