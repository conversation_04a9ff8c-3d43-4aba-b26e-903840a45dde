package com.mrk.yudong.admin.biz.course.service;

import com.mrk.yudong.admin.infrastructure.course.model.CourseInteract;
import com.mrk.yudong.admin.infrastructure.course.model.InteractInfo;
import com.mrk.yudong.core.service.BaseService;

import java.util.List;

/**
 * <p>
 * 课程互动词表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
public interface ICourseInteractService extends BaseService<CourseInteract> {

    List<InteractInfo> getListByCourseId(Long courseId);

}
