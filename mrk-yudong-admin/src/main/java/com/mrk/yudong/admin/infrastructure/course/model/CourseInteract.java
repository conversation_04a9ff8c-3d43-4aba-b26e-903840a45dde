package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 课程互动词表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CourseInteract extends Model<CourseInteract> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 互动词ID
     */
    private Long interactId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public CourseInteract(Long courseId, Long interactId) {
        this.courseId = courseId;
        this.interactId = interactId;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
