package com.mrk.yudong.admin.biz.coursepackage.enums;

import com.merach.sun.device.enums.ProductEnum;
import com.mrk.yudong.share.vo.common.OptionVo;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum CourseThemeProductEnum {
    INDOOR_BIKE(ProductEnum.INDOOR_BIKE.getId(), ProductEnum.INDOOR_BIKE.getDesc()),
    TREADMILL(ProductEnum.TREADMILL.getId(), ProductEnum.TREADMILL.getDesc()),
    ROWER(ProductEnum.ROWER.getId(), ProductEnum.ROWER.getDesc()),
    CROSS_TRAINER(ProductEnum.CROSS_TRAINER.getId(), ProductEnum.CROSS_TRAINER.getDesc()),
    OTHER(3L, "其他"),

    ;
    private final Long id;
    private final String desc;

    public static List<OptionVo> findAllProducts() {
        CourseThemeProductEnum[] enumValues = CourseThemeProductEnum.values();
        List<OptionVo> enumItemList = new ArrayList<>();

        for (CourseThemeProductEnum enumValue : enumValues) {
            enumItemList.add(new OptionVo(enumValue.getDesc(), enumValue.getId().toString()));
        }
        return enumItemList;
    }
}
