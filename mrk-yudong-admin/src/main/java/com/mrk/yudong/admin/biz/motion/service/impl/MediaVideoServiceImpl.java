package com.mrk.yudong.admin.biz.motion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.api.motion.dto.qry.MediaPageQry;
import com.mrk.yudong.admin.biz.motion.bo.UpdateVideoBO;
import com.mrk.yudong.admin.biz.motion.bo.UploadVideoBO;
import com.mrk.yudong.admin.biz.motion.enums.MediaVideoTypeEnum;
import com.mrk.yudong.admin.biz.motion.enums.MediaVieoSubTypeEnum;
import com.mrk.yudong.admin.biz.motion.service.MediaVideoService;
import com.mrk.yudong.admin.infrastructure.motion.dao.MediaVideoDao;
import com.mrk.yudong.admin.infrastructure.motion.dao.MotionDao;
import com.mrk.yudong.admin.infrastructure.motion.model.MediaVideo;
import com.mrk.yudong.core.exception.MyException;
import com.mrk.yudong.core.model.PageDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MediaVideoServiceImpl implements MediaVideoService {

    private final MediaVideoDao mediaVideoDao;

    private final MotionDao motionDao;

    @Override
    public MediaVideo uploadVideo(UploadVideoBO uploadVideo) {
        if (Boolean.FALSE.equals(validateCreateVideo(uploadVideo.getVideoId()))) {
            throw new MyException(400, "视频已存在");
        }

        return mediaVideoDao.createVideo(BeanUtil.copyProperties(uploadVideo, MediaVideo.class));
    }

    @Override
    public MediaVideo updateVideo(UpdateVideoBO updated) {
        if (ObjectUtil.isNull(mediaVideoDao.getById(updated.getId()))) {
            throw new MyException(400, "视频不存在");
        }
        return mediaVideoDao.updateVideo(BeanUtil.copyProperties(updated, MediaVideo.class));
    }

    @Override
    public Boolean deleteVideo(Long id) {
        MediaVideo existVideo = mediaVideoDao.getById(id);
        if (ObjectUtil.isNull(existVideo)) {
            return false;
        }

        if (Boolean.TRUE.equals(isVideoInUse(existVideo))) {
            throw new MyException(400, "该视频已被占用，不可删除!");
        }
        return mediaVideoDao.deleteVideo(id);
    }

    @Override
    public PageDTO<MediaVideo> pageVideos(MediaPageQry pageQry) {
        IPage<MediaVideo> results = mediaVideoDao.pageVideos(pageQry);
        PageDTO<MediaVideo> medias = new PageDTO<>();
        medias.setCurrent(pageQry.getCurrent());
        medias.setSize(pageQry.getSize());
        if (ObjectUtil.isNull(results) || CollectionUtil.isEmpty(results.getRecords())) {
            return medias;
        }

        medias.setRecords(results.getRecords());
        medias.setTotal(results.getTotal());

        return medias;
    }

    private Boolean validateCreateVideo(String videoId) {
        return ObjectUtil.isNull(mediaVideoDao.getByVideoId(videoId));
    }

    /**
     * TODO
     * 先使用粗暴的字段查询实现需求,本期只满足动作库需求，后期扩展服务时重构相关代码
     */
    private Boolean isVideoInUse(MediaVideo mediaVideo) {
        if (Boolean.FALSE.equals(mediaVideo.getType().equals(MediaVideoTypeEnum.MOTION.getType()))) {
            return Boolean.FALSE;
        }

        if (ObjectUtil.equals(mediaVideo.getSubType(), MediaVieoSubTypeEnum.FOLLOW_VIDEO.getType())) {
            return ObjectUtil.isNotNull(motionDao.getByFollowVideoId(mediaVideo.getId()));
        } else if (ObjectUtil.equals(mediaVideo.getSubType(), MediaVieoSubTypeEnum.INSTRUCTION_VIDEO.getType())) {
            return ObjectUtil.isNotNull(motionDao.getByIntroduceVideoId(mediaVideo.getId()));
        }
        return Boolean.FALSE;
    }
}
