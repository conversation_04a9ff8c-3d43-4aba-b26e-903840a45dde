package com.mrk.yudong.admin.biz.productiontest.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.infrastructure.producttest.model.TestEquFirmwareVersion;
import com.mrk.yudong.core.service.BaseService;
import java.util.List;

/**
 * <p>
 * 固件版本管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
public interface ITestEquFirmwareVersionService extends BaseService<TestEquFirmwareVersion> {
    /**
     * 分页信息
     * @param page 分页数据
     * @param firmwareVersion 查询信息
     * @return
     */
    IPage<TestEquFirmwareVersion> getPage(IPage<TestEquFirmwareVersion> page , TestEquFirmwareVersion firmwareVersion);

    /**
     * 获取所有历史版本
     * @param modelId 设备型号id
     * @return
     */
    List<TestEquFirmwareVersion> getHistoryFirmwareVersion(String modelId);

    /**
     * 获取所有历史版本
     * @param modelId 设备型号id
     * @param code 设备编号
     * @return
     */
    TestEquFirmwareVersion getNewFirmwareVersion(String modelId,String code);


}
