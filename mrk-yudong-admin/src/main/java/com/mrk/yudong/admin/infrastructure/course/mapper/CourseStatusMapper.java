package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseStatus;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 课程状态表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
public interface CourseStatusMapper extends BaseMapper<CourseStatus> {

    /**
     * 获取最新课程状态信息
     *
     * @param courseId
     * @return
     */
    CourseStatus getTop(@Param("courseId") Long courseId);

}
