package com.mrk.yudong.admin.security.config;

import com.mrk.yudong.admin.security.filter.JwtAuthenticationFilter;
import com.mrk.yudong.admin.security.handler.AuthAccessDeniedHandler;
import com.mrk.yudong.admin.security.handler.AuthenticationEntryPointHandler;
import com.mrk.yudong.admin.security.handler.UserDetailsHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@RequiredArgsConstructor
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    private final JwtAuthenticationFilter jwtAuthenticationFilter;

    private final AuthAccessDeniedHandler authAccessDeniedHandler;

    private final AuthenticationEntryPointHandler authenticationEntryPointHandler;

    private final UserDetailsHandler userDetailsHandler;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.csrf().disable().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);

        http.authorizeRequests().antMatchers(HttpMethod.OPTIONS).permitAll()
                .antMatchers(HttpMethod.GET, "/sendSms", "/sys-user/name", "/login", "/actuator/health").permitAll()
                .antMatchers(HttpMethod.POST, "/signIn", "/sys-user/names", "/dingTlakController/callBack", "/item/initStock", "/activity/sync", "/activity/sync/userenroll").permitAll()
                .antMatchers(HttpMethod.PUT, "/password").permitAll()
                .antMatchers(HttpMethod.POST, "/index", "/password", "/signOut", "/upload", "/feedback/save").authenticated()
                .antMatchers(HttpMethod.GET, "/vodSignature", "/feedback/noPermissions", "/filename").authenticated()
                .antMatchers("/course/**").authenticated()
                .anyRequest().access("@userDetailsHandler.hasPermission(request, authentication)");

        http.headers().cacheControl();

        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        http.exceptionHandling().authenticationEntryPoint(authenticationEntryPointHandler)
                .accessDeniedHandler(authAccessDeniedHandler);
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.userDetailsService(userDetailsHandler).passwordEncoder(new BCryptPasswordEncoder());
    }

    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

}
