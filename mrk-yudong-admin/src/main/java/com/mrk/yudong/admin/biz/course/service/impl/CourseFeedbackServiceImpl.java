package com.mrk.yudong.admin.biz.course.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.admin.biz.course.service.ICourseFeedbackService;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseFeedbackMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseFeedback;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 课程反馈表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
@Service
public class CourseFeedbackServiceImpl extends BaseServiceImpl<CourseFeedbackMapper, CourseFeedback> implements ICourseFeedbackService {

    /**
     * 查询课程反馈列表
     *
     * @param courseId
     * @param type
     * @return
     */
    @Override
    public List<JSONObject> query(Long courseId, Integer type) {
        return baseMapper.query(courseId, type);
    }
}
