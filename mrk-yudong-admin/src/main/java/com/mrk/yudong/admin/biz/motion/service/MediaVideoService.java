package com.mrk.yudong.admin.biz.motion.service;

import com.mrk.yudong.admin.api.motion.dto.qry.MediaPageQry;
import com.mrk.yudong.admin.biz.motion.bo.UpdateVideoBO;
import com.mrk.yudong.admin.biz.motion.bo.UploadVideoBO;
import com.mrk.yudong.admin.infrastructure.motion.model.MediaVideo;
import com.mrk.yudong.core.model.PageDTO;

public interface MediaVideoService {
    MediaVideo uploadVideo(UploadVideoBO upload);

    MediaVideo updateVideo(UpdateVideoBO updated);

    Boolean deleteVideo(Long id);

    PageDTO<MediaVideo> pageVideos(MediaPageQry pageQry);
}
