package com.mrk.yudong.admin.infrastructure.user.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 兑换订单配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ExchangeOrderConfig extends Model<ExchangeOrderConfig> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 会员分类：1-麦瑞克，2-绝影
     */
    private Integer vipClassify;

    @TableField(exist = false)
    private String vipClassifyName;

    /**
     * VIP类型：10-VIP，20-SVIP，30-XVIP
     */
    private Integer vipType;

    @TableField(exist = false)
    private String vipTypeName;

    /**
     * 用户名称
     */
    @NotBlank(message = "用户名称不能为空")
    @Length(max = 64, message = "用户名称长度不能超过64位")
    private String username;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    @Length(min = 11, max = 64, message = "订单号长度必须大于10位且不能超过64位")
    private String orderNo;

    /**
     * 店铺编码
     */
    @NotBlank(message = "店铺编码不能为空")
    private String shopCode;

    /**
     * 店铺名称
     */
    @NotBlank(message = "店铺名称不能为空")
    private String shopName;

    /**
     * 支付金额
     */
    @NotNull(message = "支付金额不能为空")
    @DecimalMin(value = "0.01", message = "支付金额必须大于0")
    @Digits(integer = 10, fraction = 2, message = "支付金额格式不合法")
    private BigDecimal payPrice;

    /**
     * 支付时间
     */
    @NotNull(message = "支付时间不能为空")
    @Past(message = "支付时间必须是过去的时间")
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime payTime;

    /**
     * 规格代码
     */
    @NotBlank(message = "商品代码不能为空")
    @Length(max = 64, message = "商品代码长度不能超过64位长度")
    private String skuCode;

    /**
     * 兑换时间
     */
    @JsonIgnore
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime exchangeTime;

    /**
     * 有效时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_FORMAT)
    private LocalDate effectiveTime;

    /**
     * 截图凭证图
     */
    @TableField(exist = false)
    @Size(max = 6, message = "最多上次6张截图凭证")
    private List<String> images;

    /**
     * 兑换状态：0-未兑换，1-已兑换
     */
    private Integer status;

    /**
     * 是否为预发数据：0-否，1-是
     */
    private Integer isTra;

    /**
     * 兑换状态文字显示
     */
    @TableField(exist = false)
    private String statusDesc;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建人名称
     */
    @TableField(exist = false)
    private String createName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
