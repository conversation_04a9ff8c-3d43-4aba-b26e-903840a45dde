package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 课程状态表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CourseStatus extends Model<CourseStatus> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 上一个状态
     */
    private Integer preStatus;

    /**
     * 当前状态
     */
    private Integer status;

    /**
     * 操作用户ID
     */
    private Long sysUserId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public CourseStatus(Long courseId, Integer preStatus, Integer status, Long sysUserId) {
        this.courseId = courseId;
        this.preStatus = preStatus;
        this.status = status;
        this.sysUserId = sysUserId;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
