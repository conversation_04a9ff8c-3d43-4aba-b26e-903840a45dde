package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseTag;
import com.mrk.yudong.admin.infrastructure.course.model.CourseTagDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 课程-课程标签关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
public interface CourseTagDetailMapper extends BaseMapper<CourseTagDetail> {

    /**
     * 根据课程ID获取标签信息
     *
     * @param courseId
     * @return
     */
    List<CourseTag> getTagList(@Param("courseId") Long courseId);

}
