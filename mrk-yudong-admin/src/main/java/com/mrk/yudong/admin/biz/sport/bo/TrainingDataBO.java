package com.mrk.yudong.admin.biz.sport.bo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TrainingDataBO {

    /**
     * 训练id
     */
    private Long trainingRecordId;

    /**
     * 训练类型：1-课程训练，2-自由训练，6-实景视频，20-游戏
     * @see com.mrk.yudong.share.enums.training.TrainingTypeEnum
     */
    private Integer trainingType;

    /**
     * 训练模式：0-无任何模式，1-定距练，2-定时练
     * @see com.mrk.yudong.share.enums.training.TrainingModeEnum
     */
    private Integer trainingMode;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 产品大类id
     */
    private Long productId;

    /**
     * 型号id
     */
    private Long productModelId;

    /**
     * 设备信息id
     */
    private Long equipmentInfoId;

    /**
     * 消耗
     */
    private Double kcal;

    /**
     * 距离
     */
    private Double distance;

    /**
     * 设备时长
     */
    private Integer deviceTime;

    /**
     * 总踏数/总桨数/总个数/总圈数
     */
    private Double num;

    /**
     * 结束时间
     */
    private LocalDateTime createTime;

    /**
     * 课程播放时长
     */
    private Integer playTime;

    /**
     * 用户ID
     */
    private Long userId;

    private String cloudKey;

    private  Integer dataScene;
}
