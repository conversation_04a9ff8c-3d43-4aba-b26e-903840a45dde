package com.mrk.yudong.admin.infrastructure.user.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.user.model.MemberOrder;
import com.mrk.yudong.admin.api.user.po.MemberOrderPO;
import com.mrk.yudong.admin.api.user.vo.MemberOrderQueryVO;
import com.mrk.yudong.core.model.PageDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-10
 */
public interface MemberOrderMapper extends BaseMapper<MemberOrder> {

    /**
     * 分页查询用户会员开通记录
     *
     * @param page               分页参数
     * @param memberOrderQueryVO 查询参数
     * @return PageDTO<MemberOrderPO>
     * <AUTHOR>
     * @date 2023/1/4 14:31
     */
    PageDTO<MemberOrderPO> pageRecord(PageDTO<MemberOrderPO> page, @Param("memberOrderQueryVO") MemberOrderQueryVO memberOrderQueryVO);

    /**
     * 导出订单列表
     *
     * @param memberOrderQueryVO
     * @return
     */
    List<JSONObject> queryMap(@Param("memberOrderQueryVO") MemberOrderQueryVO memberOrderQueryVO);

}
