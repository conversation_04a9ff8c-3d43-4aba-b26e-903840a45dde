package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 训练计划记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LogPlan extends Model<LogPlan> {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 用户id
     */
    private Long userBy;

    /**
     * 计划id
     */
    private Long planId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
