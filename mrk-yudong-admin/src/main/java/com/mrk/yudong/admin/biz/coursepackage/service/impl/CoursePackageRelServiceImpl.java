package com.mrk.yudong.admin.biz.coursepackage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mrk.yudong.admin.biz.coursepackage.service.ICoursePackageRelService;
import com.mrk.yudong.admin.infrastructure.coursepackage.mapper.CoursePackageRelMapper;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CoursePackageRel;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 课包课程关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
@Service
public class CoursePackageRelServiceImpl extends BaseServiceImpl<CoursePackageRelMapper, CoursePackageRel> implements ICoursePackageRelService {

    @Override
    public List<CoursePackageRel> findByCoursePackageId(Long coursePackageId) {
        return this.list(new LambdaQueryWrapper<CoursePackageRel>()
                .eq(CoursePackageRel::getPackageId, coursePackageId));
    }

    @Override
    public Boolean deletePackageCourses(Long packageId, List<Long> courseIds) {
        if (ObjectUtil.isNull(packageId) || CollUtil.isEmpty(courseIds)) {
            return Boolean.FALSE;
        }

        LambdaQueryWrapper<CoursePackageRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CoursePackageRel::getPackageId, packageId);
        queryWrapper.in(CoursePackageRel::getCourseId, courseIds);
        return this.remove(queryWrapper);
    }
}
