package com.mrk.yudong.admin.biz.comment.service.enums;


import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;

@Getter
@AllArgsConstructor
public enum AppCommnetTypeEnum {

    /**
     * app评论类型
     */
    APP_USER(1,"app体验", "您觉得App使用体验如何？"),
    APP_COURSE(2,"课程内容", "您觉得课程内容如何？");

    private final Integer id;

    private final String value;

    private final String title;

    public static String getStrAppCommetType(Integer id){
       HashMap<Integer, String>  map = new HashMap<>();
        AppCommnetTypeEnum[] values = AppCommnetTypeEnum.values();
        for (AppCommnetTypeEnum level: values) {
            map.put(level.getId(), level.getValue());
        }
        String s = map.get(id);
        if (StrUtil.isEmpty(s)){
            return "";
        }
        return s;
    }
}
