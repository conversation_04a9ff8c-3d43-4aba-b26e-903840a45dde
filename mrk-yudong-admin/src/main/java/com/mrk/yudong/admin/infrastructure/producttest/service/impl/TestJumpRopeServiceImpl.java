package com.mrk.yudong.admin.infrastructure.producttest.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.api.jumprope.dto.TestJumpRopeForm;
import com.mrk.yudong.admin.infrastructure.producttest.mapper.TestJumpRopeMapper;
import com.mrk.yudong.admin.infrastructure.producttest.model.TestJumpRope;
import com.mrk.yudong.admin.infrastructure.producttest.service.ITestJumpRopeService;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.WebUtil;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.util.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 *  产测跳绳 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */

@Slf4j
@Service
public class TestJumpRopeServiceImpl extends BaseServiceImpl<TestJumpRopeMapper, TestJumpRope> implements ITestJumpRopeService {


    /**
     * 创建
     *
     * @param form
     */
    @Override
    public Page<TestJumpRope> findAllByPage(TestJumpRopeForm form) {
        Page<TestJumpRope> page = baseMapper.selectPage(new Page<>(form.getCurrent(), form.getSize()), buildQueryWrapper(form));
        List<TestJumpRope> records = page.getRecords();

        if (CollectionUtils.isEmpty(records)) {
            return new Page<>(form.getCurrent(), form.getSize());
        }

        return page;
    }

    /**
     * 导出
     *
     * @param form 形式
     */
    @Override
    public void export(TestJumpRopeForm form) {
        List<TestJumpRope> jumpRopes = baseMapper.selectList(buildQueryWrapper(form));
        ExcelUtils.download("跳绳列表", TestJumpRope.class, jumpRopes, WebUtil.getResponse());
    }

    /**
     * 建立查询包装
     *
     * @param form 形式
     * @return {@link LambdaQueryWrapper}<{@link TestJumpRope}>
     */
    private LambdaQueryWrapper<TestJumpRope> buildQueryWrapper(TestJumpRopeForm form) {
        log.info("[TestJumpRopeServiceImpl#buildQueryWrapper] testJumpRopeForm={}", JSON.toJSONString(form));

        LambdaQueryWrapper<TestJumpRope> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TestJumpRope::getIsDelete, BaseConstant.INT_FALSE)
                .ge(StrUtil.isNotBlank(form.getBeginDate()), TestJumpRope::getCreateTime, form.getBeginDate() + " 00:00:00")
                .le(StrUtil.isNotBlank(form.getEndDate()), TestJumpRope::getCreateTime, form.getEndDate() + " 23:59:59")
                .orderByDesc(TestJumpRope::getCreateTime);

        return queryWrapper;
    }
}
