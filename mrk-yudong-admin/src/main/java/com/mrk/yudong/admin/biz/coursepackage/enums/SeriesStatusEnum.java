package com.mrk.yudong.admin.biz.coursepackage.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SeriesStatusEnum {
    DRAFT(0, "未上线"),
    PUBLISH(1, "上线"),
    CLOSE(2, "下线"),
    ;

    private final Integer status;
    private final String desc;

    public static SeriesStatusEnum fromCode(Integer status) {
        for (SeriesStatusEnum seriesStatus : values()) {
            if (seriesStatus.getStatus().equals(status)) {
                return seriesStatus;
            }
        }
        throw new IllegalArgumentException("Invalid SeriesStatusEnum code: " + status);
    }
}
