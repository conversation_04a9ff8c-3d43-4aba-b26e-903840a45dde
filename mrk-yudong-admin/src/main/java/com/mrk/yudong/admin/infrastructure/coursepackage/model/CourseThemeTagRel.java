package com.mrk.yudong.admin.infrastructure.coursepackage.model;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CourseThemeTagRel extends Model<CourseThemeDetail> {
    private Long id;

    private Long themeId;

    private Long tagId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    public CourseThemeTagRel(Long themeId, Long tagId) {
        this.themeId = themeId;
        this.tagId = tagId;
    }
}
