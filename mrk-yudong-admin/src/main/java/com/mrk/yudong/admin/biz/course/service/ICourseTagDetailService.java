package com.mrk.yudong.admin.biz.course.service;

import com.mrk.yudong.admin.infrastructure.course.model.CourseTag;
import com.mrk.yudong.admin.infrastructure.course.model.CourseTagDetail;
import com.mrk.yudong.core.service.BaseService;

import java.util.List;

/**
 * <p>
 * 课程-课程标签关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
public interface ICourseTagDetailService extends BaseService<CourseTagDetail> {

    /**
     * 根据课程ID获取标签信息
     *
     * @param courseId
     * @return
     */
    List<CourseTag> getTagList(Long courseId);

    /**
     * 根据课程ID获取标签
     *
     * @param courseId
     * @return
     */
    List<String> getTags(Long courseId);

}
