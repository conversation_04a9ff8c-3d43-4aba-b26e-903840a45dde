package com.mrk.yudong.admin.infrastructure.marketing.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mrk.yudong.admin.infrastructure.marketing.dao.MarketingActivityUserEnrollDao;
import com.mrk.yudong.admin.infrastructure.marketing.mapper.MarketingActivityUserEnrollMapper;
import com.mrk.yudong.admin.infrastructure.marketing.model.MarketingActivityUserEnroll;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MarketingActivityUserEnrollDaoImpl implements MarketingActivityUserEnrollDao {
    private final MarketingActivityUserEnrollMapper marketingActivityUserEnrollMapper;

    @Override
    public MarketingActivityUserEnroll getByUserIdAndActivityId(long userId, long activityId) {
        LambdaQueryWrapper<MarketingActivityUserEnroll> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MarketingActivityUserEnroll::getUserId, userId)
                .eq(MarketingActivityUserEnroll::getActivityId, activityId);

        return marketingActivityUserEnrollMapper.selectOne(queryWrapper);
    }

    @Override
    public Boolean updateActivityUserEnrollById(MarketingActivityUserEnroll marketingActivityUserEnroll) {
        return marketingActivityUserEnrollMapper.updateById(marketingActivityUserEnroll) > 0;
    }


}
