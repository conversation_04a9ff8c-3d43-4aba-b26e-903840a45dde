package com.mrk.yudong.admin.biz.device.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.mrk.yudong.admin.infrastructure.device.mapper.EquEquipmentTypeMapper;
import com.mrk.yudong.admin.infrastructure.device.model.EquEquipmentType;
import com.mrk.yudong.admin.biz.device.service.IEquEquipmentTypeService;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 设备类型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-14
 */
@Service
public class EquEquipmentTypeServiceImpl extends BaseServiceImpl<EquEquipmentTypeMapper, EquEquipmentType> implements IEquEquipmentTypeService {

    @Override
    public Map<Long, String> getEquipmentNameData(Set<Long> equipmentIds) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return new HashMap<>(0);
        }

        LambdaQueryChainWrapper<EquEquipmentType> wrapper = new LambdaQueryChainWrapper<>(this.baseMapper);
        return wrapper.in(EquEquipmentType::getId, equipmentIds).list().stream()
                .collect(Collectors.toMap(EquEquipmentType::getId, EquEquipmentType::getTypeName, (k1, k2) -> k2));
    }

}
