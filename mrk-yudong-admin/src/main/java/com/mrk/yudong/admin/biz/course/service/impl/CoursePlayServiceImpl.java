package com.mrk.yudong.admin.biz.course.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.admin.api.course.vo.PlayerQueryVO;
import com.mrk.yudong.admin.biz.course.service.ICourseCollectService;
import com.mrk.yudong.admin.biz.course.service.ICourseMakeService;
import com.mrk.yudong.admin.biz.course.service.ICoursePlayService;
import com.mrk.yudong.admin.biz.course.service.ILogCourseDetailService;
import com.mrk.yudong.admin.biz.sys.service.ISysMetaService;
import com.mrk.yudong.admin.feign.EquipmentFeign;
import com.mrk.yudong.admin.infrastructure.course.mapper.CoursePlayMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CoursePlay;
import com.mrk.yudong.admin.infrastructure.course.model.CoursePlayStatistic;
import com.mrk.yudong.core.service.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 课程播放明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-07
 */
@RequiredArgsConstructor
@Service
public class CoursePlayServiceImpl extends BaseServiceImpl<CoursePlayMapper, CoursePlay> implements ICoursePlayService {

    private final ICourseMakeService courseMakeService;

    private final ICourseCollectService courseCollectService;

    private final ILogCourseDetailService logCourseDetailService;

    private final EquipmentFeign equipmentFeign;

    private final ISysMetaService sysMetaService;

    /**
     * 根据日期统计总数量
     *
     * @param playerQueryVO
     * @return
     */
    @Override
    public CoursePlayStatistic sumByDate(PlayerQueryVO playerQueryVO) {
        List<JSONObject> list = baseMapper.sumByDate(playerQueryVO);
        if (CollUtil.isEmpty(list)) {
            return null;
        }

        JSONObject data = sysMetaService.buildTrainData(list);
        CoursePlayStatistic coursePlayStatistic = new CoursePlayStatistic();
        BeanUtil.fillBeanWithMap(data, coursePlayStatistic, true);
        return coursePlayStatistic;
    }

}
