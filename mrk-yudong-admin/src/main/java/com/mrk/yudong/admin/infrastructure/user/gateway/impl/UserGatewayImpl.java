package com.mrk.yudong.admin.infrastructure.user.gateway.impl;

import com.merach.sun.user.api.AccountApi;
import com.merach.sun.user.dto.cmd.account.AccountsStatusUpdateCmd;
import com.mrk.yudong.admin.infrastructure.user.gateway.UserGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserGatewayImpl implements UserGateway {
    private final AccountApi accountApi;

    @Override
    public Boolean updateAccountStatus(Long userId, Integer status) {
        AccountsStatusUpdateCmd accountsStatusUpdateCmd = new AccountsStatusUpdateCmd();
        accountsStatusUpdateCmd.setAccountId(userId);
        accountsStatusUpdateCmd.setStatus(status);
        return accountApi.updateAccountStatus(accountsStatusUpdateCmd);
    }
}
