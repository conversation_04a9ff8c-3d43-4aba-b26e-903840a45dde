package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.mrk.yudong.admin.infrastructure.course.model.LogCourseDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 课程详情记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-05
 */
public interface LogCourseDetailMapper extends BaseMapper<LogCourseDetail> {

    /**
     * 获取去重用户数量
     *
     * @param courseId
     * @return
     */
    int getGroupUserNum(@Param("courseId") Long courseId);

}
