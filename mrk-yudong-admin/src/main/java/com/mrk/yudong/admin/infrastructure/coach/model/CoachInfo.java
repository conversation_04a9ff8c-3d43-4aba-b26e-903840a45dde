package com.mrk.yudong.admin.infrastructure.coach.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.admin.infrastructure.course.model.InteractInfo;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 教练信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-24
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CoachInfo extends Model<CoachInfo> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 昵称
     */
    private String name;

    /**
     * 头衔
     */
    private String title;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 后台用户ID
     */
    private Long sysUserId;

    /**
     * 背景封面图
     */
    private String cover;
    /**
     * 头像
     */
    private String avatar;

    /**
     * 找教练头像
     */
    private String selectAvatar;

    /**
     * 彩屏端教练头像
     */
    private String colorScreenAvatar;
    /**
     * 个人介绍
     */
    private String introduce;

    /**
     * 课程数量
     */
    @TableField(exist = false)
    private Integer num;

    /**
     * 生活中图片数量
     */
    @TableField(exist = false)
    private Integer imageNum;

    /**
     * 被关注数量
     */
    @TableField(exist = false)
    private Integer followNum;

    /**
     * 性别：0-未知，1-男，2-女
     */
    private Integer sex;

    /**
     * 排序号
     */
    private Integer sort;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 状态文字显示
     */
    @TableField(exist = false)
    private String statusDesc;

    /**
     * 是否为预发数据：0-否，1-是
     */
    private Integer isTra;

    /**
     * 互动词
     */
    @TableField(exist = false)
    List<InteractInfo> interactInfos;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    public CoachInfo(String name, String mobile, Long sysUserId, String avatar, Integer status, Integer isTra, Long createId, String selectAvatar, String colorScreenAvatar) {
        this.name = name;
        this.mobile = mobile;
        this.sysUserId = sysUserId;
        this.avatar = avatar;
        this.status = status;
        this.isTra = isTra;
        this.createId = createId;
        this.selectAvatar = selectAvatar;
        this.colorScreenAvatar = colorScreenAvatar;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
