package com.mrk.yudong.admin.infrastructure.user.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UserMeta extends Model<UserMeta> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 显示名称
     */
    private String name;

    /**
     * 字典编码
     */
    private String code;

    /**
     * 值
     */
    private String val;

    /**
     * 排序
     */
    private Integer sort;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
