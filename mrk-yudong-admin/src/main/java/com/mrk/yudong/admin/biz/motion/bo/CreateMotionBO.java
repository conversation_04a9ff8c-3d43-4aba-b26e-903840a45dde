package com.mrk.yudong.admin.biz.motion.bo;

import lombok.Data;

import java.util.List;

@Data
public class CreateMotionBO {
    private String name;

    private String part;

    private Long coachId;

    private Integer type;

    private String nameAudio;

    private Long productId;

    private String cover;

    private Long introduceVideoId;

    private String introduceVideo;

    private Long followAloneVideoId;

    private String followAloneVideo;

    private Integer motionCount;

    private Integer oneMotionTime;

    private Integer secondPerTime;

    private Double kcalPerTime;

    private Integer broadcastRequired;

    private Integer broadcastMode;

    private Integer broadcastSound;

    private String introduce;

    private List<MotionCategoryBO> categories;

    private Long createId;

    private Integer duration;

    private Integer grade;
}
