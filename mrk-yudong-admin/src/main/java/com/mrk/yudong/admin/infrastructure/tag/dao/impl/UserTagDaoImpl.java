package com.mrk.yudong.admin.infrastructure.tag.dao.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.infrastructure.tag.dao.UserTagDao;
import com.mrk.yudong.admin.infrastructure.tag.mapper.UserTagComputeConfigMapper;
import com.mrk.yudong.admin.infrastructure.tag.mapper.UserTagMapper;
import com.mrk.yudong.admin.infrastructure.tag.mapper.UserTagValueMapper;
import com.mrk.yudong.admin.infrastructure.tag.model.UserTag;
import com.mrk.yudong.admin.infrastructure.tag.model.UserTagComputeConfig;
import com.mrk.yudong.admin.infrastructure.tag.model.UserTagValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

@Component
@Slf4j
public class UserTagDaoImpl implements UserTagDao {

    @Resource
    private UserTagMapper userTagMapper;

    @Resource
    private UserTagComputeConfigMapper userTagComputeConfigMapper;

    @Resource
    private UserTagValueMapper userTagValueMapper;

    @Override
    public void addUserTagConfig(UserTagComputeConfig config) {
        userTagComputeConfigMapper.insert(config);
    }

    // TODO 可能需要删除不存在的值
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addUserTagValues(Collection<UserTagValue> userTagValues) {
        userTagValues.forEach(u -> userTagValueMapper.insert(u));
    }

    @Override
    public void addUserTag(UserTag userTag) {
        userTagMapper.insert(userTag);
    }

    @Override
    public UserTag getUserTag(String code) {
        return userTagMapper.selectOne(Wrappers.<UserTag>lambdaQuery()
                .eq(UserTag::getCode, code));
    }

    @Override
    public UserTag getUserTag(Long tagId) {
        return userTagMapper.selectOne(Wrappers.<UserTag>lambdaQuery()
                .eq(UserTag::getId, tagId));
    }

    @Override
    public Page<UserTag> pageUserTags(Integer current, Integer size, String code, String name) {
        Page<UserTag> page = new Page<>(current, size);
        return userTagMapper.selectPage(page, Wrappers.lambdaQuery(UserTag.class)
                .eq(StringUtils.isNotBlank(code), UserTag::getCode, code)
                .like(StringUtils.isNotBlank(name), UserTag::getName, "%" + name + "%")
                .orderByDesc(UserTag::getCreateTime));
    }

    @Override
    public List<UserTagComputeConfig> getComputeConfigsByTagIds(List<Long> tagIds) {
        return userTagComputeConfigMapper.selectList(Wrappers.lambdaQuery(UserTagComputeConfig.class)
                .in(UserTagComputeConfig::getTagId, tagIds));
    }

    @Override
    public List<UserTagValue> getTagValuesByTagIds(List<Long> tagIds) {
        return userTagValueMapper.selectList(Wrappers.lambdaQuery(UserTagValue.class)
                .in(UserTagValue::getTagId, tagIds));
    }
}
