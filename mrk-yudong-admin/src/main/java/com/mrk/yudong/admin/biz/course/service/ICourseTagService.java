package com.mrk.yudong.admin.biz.course.service;

import com.mrk.yudong.admin.infrastructure.course.model.CourseTag;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.service.BaseService;

import java.util.List;

/**
 * <p>
 * 课程标签表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
public interface ICourseTagService extends BaseService<CourseTag> {

    /**
     * 根据父级ID查询标签
     *
     * @param parentId
     * @return
     */
    List<CourseTag> query(Long parentId);

    /**
     * 删除标签
     *
     * @param tagId
     * @param courseTag
     * @return
     */
    R removeCourseTag(Long tagId, CourseTag courseTag);

}
