package com.mrk.yudong.admin.biz.course.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.biz.course.constant.CourseConstant;
import com.mrk.yudong.admin.biz.course.service.ICourseKeywordsService;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseKeywordsMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseKeywords;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.share.constant.BaseConstant;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>
 * 关键词信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-09
 */
@Service
public class CourseKeywordsServiceImpl extends BaseServiceImpl<CourseKeywordsMapper, CourseKeywords> implements ICourseKeywordsService {

    @Override
    public IPage<CourseKeywords> query(Page<CourseKeywords> page, Map<String, Object> param) {
        return baseMapper.query(page, param);
    }

    /**
     * 删除 维度/关键字
     *
     * @return
     */
    @Override
    public Boolean deleteCourseKeywords(CourseKeywords courseKeywords) {
        //如果删除类型为‘维度’， 则清空关联的‘关键词’信息
        if (courseKeywords.getType().equals(CourseConstant.DIMENSION)) {
            LambdaUpdateWrapper<CourseKeywords> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(CourseKeywords::getIsDelete, BaseConstant.INT_TRUE)
                    .eq(CourseKeywords::getTheDimension, courseKeywords.getId())
                    .eq(CourseKeywords::getIsDelete, BaseConstant.INT_FALSE);
            update(wrapper);
        }

        LambdaUpdateWrapper<CourseKeywords> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(CourseKeywords::getIsDelete, BaseConstant.INT_TRUE)
                .eq(CourseKeywords::getId, courseKeywords.getId());
        return update(wrapper);
    }

}
