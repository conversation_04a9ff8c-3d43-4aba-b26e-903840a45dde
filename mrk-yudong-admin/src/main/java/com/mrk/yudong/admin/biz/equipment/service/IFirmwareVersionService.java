package com.mrk.yudong.admin.biz.equipment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.infrastructure.equipment.model.FirmwareVersion;
import com.mrk.yudong.core.service.BaseService;

/**
 * <p>
 * 固件版本管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
public interface IFirmwareVersionService extends BaseService<FirmwareVersion> {

    /**
     * 分页信息
     * @param page 分页数据
     * @param firmwareVersion 查询信息
     * @return
     */
    IPage<FirmwareVersion> getPage(IPage<FirmwareVersion> page , FirmwareVersion firmwareVersion) ;

}
