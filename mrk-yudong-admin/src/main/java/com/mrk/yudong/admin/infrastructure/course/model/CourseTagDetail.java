package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 课程-课程标签关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CourseTagDetail extends Model<CourseTagDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 一级标签ID
     */
    private Long topId;

    /**
     * 标签ID
     */
    private Long tagId;

    public CourseTagDetail(Long courseId, Long topId, Long tagId) {
        this.courseId = courseId;
        this.topId = topId;
        this.tagId = tagId;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
