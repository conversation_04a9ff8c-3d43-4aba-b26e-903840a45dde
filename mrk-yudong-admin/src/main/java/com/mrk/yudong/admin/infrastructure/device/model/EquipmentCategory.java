package com.mrk.yudong.admin.infrastructure.device.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mrk.yudong.share.constant.ResponseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 设备分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@JsonIgnoreProperties(value = {"status", "createId", "createTime", "updateId", "updateTime"})
@Data
@EqualsAndHashCode(callSuper = false)
public class EquipmentCategory extends Model<EquipmentCategory> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 类型：100-心率带
     */
    @NotNull(message = ResponseConstant.EQUIP_SELECT_DEVICE_TYPE)
    private Integer type;

    /**
     * 设备类型名称
     */
    @TableField(exist = false)
    private String typeDesc;

    /**
     * 设备类型绑定设备信息数量
     */
    @TableField(exist = false)
    private Integer num;

    /**
     * 封面
     */
    @NotBlank(message = "请上传封面")
    @Length(max = 128, message = "封面地址不合法")
    private String cover;

    /**
     * 图标
     */
    @NotBlank(message = "请上传图标")
    @Length(max = 128, message = "图标地址不合法")
    private String icon;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
