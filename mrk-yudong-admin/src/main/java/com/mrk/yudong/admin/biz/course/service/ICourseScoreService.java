package com.mrk.yudong.admin.biz.course.service;

import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.admin.infrastructure.course.model.CourseScore;
import com.mrk.yudong.core.service.BaseService;

import java.util.List;

/**
 * <p>
 * 课程评分表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
public interface ICourseScoreService extends BaseService<CourseScore> {

    /**
     * 获取课程评分统计
     *
     * @param courseId
     * @param column
     * @return
     */
    List<JSONObject> getScoreList(Long courseId, String column);

}
