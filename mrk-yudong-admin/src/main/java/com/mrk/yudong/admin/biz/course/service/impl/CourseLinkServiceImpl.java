package com.mrk.yudong.admin.biz.course.service.impl;

import com.mrk.yudong.admin.biz.course.service.ICourseLinkService;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseLinkMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseLink;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.share.po.CourseLinkPO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 课程小节表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
@Service
public class CourseLinkServiceImpl extends BaseServiceImpl<CourseLinkMapper, CourseLink> implements ICourseLinkService {

    /**
     * 根据课程ID和环节ID获取课程小节信息列表
     *
     * @param courseId
     * @param catalogueId
     * @return
     */
    @Override
    public List<CourseLinkPO> list(Long courseId, Long catalogueId) {
        return baseMapper.list(courseId, catalogueId);
    }

}
