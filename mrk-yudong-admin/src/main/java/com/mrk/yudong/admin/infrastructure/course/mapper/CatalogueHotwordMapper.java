package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CatalogueHotword;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 环节热词表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
public interface CatalogueHotwordMapper extends BaseMapper<CatalogueHotword> {

    List<JSONObject> getHotWordList(@Param("courseCatalogueId") Long courseCatalogueId);

}
