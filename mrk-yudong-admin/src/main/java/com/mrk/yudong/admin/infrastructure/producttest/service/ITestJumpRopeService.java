package com.mrk.yudong.admin.infrastructure.producttest.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.api.jumprope.dto.TestJumpRopeForm;
import com.mrk.yudong.admin.infrastructure.producttest.model.TestJumpRope;
import com.mrk.yudong.core.service.BaseService;

/**
 * <p>
 *  产测跳绳 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
public interface ITestJumpRopeService extends BaseService<TestJumpRope> {


    /**
     * 创建
     *
     * @param form
     */
    Page<TestJumpRope> findAllByPage(TestJumpRopeForm form);

    /**
     * 导出
     *
     * @param form 形式
     */
    void export(TestJumpRopeForm form);
}
