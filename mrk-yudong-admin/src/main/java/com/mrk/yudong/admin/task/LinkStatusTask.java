package com.mrk.yudong.admin.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.mrk.yudong.admin.biz.link.service.ILinkService;
import com.mrk.yudong.admin.infrastructure.link.model.Link;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 *
 * 链路状态Job
 *
 * <AUTHOR>
 * @date 2023/11/15 10:50:15
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LinkStatusTask extends JavaProcessor {

    private final ILinkService linkService;



    /**
     * @param context
     * @return {@link ProcessResult}
     */
    @Override
    public ProcessResult process(JobContext context) {
        try {

            List<Link> dbLinks = linkService.findAllByStatus(Arrays.asList(1, 2));
            if (CollectionUtils.isEmpty(dbLinks)) {
                return new ProcessResult(true);
            }

            List<Link> links = new ArrayList<>();
            dbLinks.forEach(link -> {
                int status = linkService.calculateStatus(link.getBeginTime(), link.getEndTime());
                Link updateLink = new Link();
                updateLink.setId(link.getId());
                updateLink.setStatus(status);
                updateLink.setUpdateTime(LocalDateTime.now());
                links.add(updateLink);
            });

            linkService.updateBatchById(links);

            log.info("[LinkStatusTask#process] links={}", JSON.toJSONString(links));

            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("[LinkStatusTask#process] Exception=", e);
            return new ProcessResult(false);
        }
    }
}
