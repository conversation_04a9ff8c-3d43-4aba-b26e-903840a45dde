package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 课程目录（课程环节）表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CourseCatalogue extends Model<CourseCatalogue> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 环节标题
     */
    private String title;

    /**
     * 环节名称
     */
    private String name;

    /**
     * 环节消耗
     */
    private Double kcal;

    /**
     * 开始时间：秒
     */
    private Integer beginTime;

    /**
     * 结束时间：秒
     */
    private Integer endTime;

    /**
     * 排序号
     */
    private Integer sort;

    /**
     * 环节描述
     */
    private String describeInfo;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
