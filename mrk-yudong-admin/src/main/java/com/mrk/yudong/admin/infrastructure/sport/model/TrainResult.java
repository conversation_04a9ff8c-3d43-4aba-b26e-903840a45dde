package com.mrk.yudong.admin.infrastructure.sport.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 训练结算结果数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Getter
@Setter
@Accessors(chain = true)
public class TrainResult extends Model<TrainResult> {

    private static final long serialVersionUID = 1L;

    /**
     * 结果ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 训练ID
     */
    private Long trainId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 返回结果JSON
     */
    private String result;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
