package com.mrk.yudong.admin.biz.course.service;

import com.mrk.yudong.admin.infrastructure.course.model.CoursePlay;
import com.mrk.yudong.admin.infrastructure.course.model.CoursePlayStatistic;
import com.mrk.yudong.admin.api.course.vo.PlayerQueryVO;
import com.mrk.yudong.core.service.BaseService;

/**
 * <p>
 * 课程播放明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-07
 */
public interface ICoursePlayService extends BaseService<CoursePlay> {

    /**
     * 根据日期统计总数量
     *
     * @param playerQueryVO
     * @return
     */
    CoursePlayStatistic sumByDate(PlayerQueryVO playerQueryVO);

}
