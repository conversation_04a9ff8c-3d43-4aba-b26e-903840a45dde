package com.mrk.yudong.admin.infrastructure.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.mrk.yudong.admin.infrastructure.equipment.model.FirmwareVersion;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 固件版本管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
public interface FirmwareVersionMapper extends BaseMapper<FirmwareVersion> {
    /**
     * 分页
     * @param page
     * @param equipTypeId
     * @param agreement
     * @return
     */
    Page<FirmwareVersion> getPage(IPage<FirmwareVersion> page, @Param("equipTypeId") Long equipTypeId,@Param("agreement") Integer agreement,@Param("isTra") Integer isTra) ;

}
