package com.mrk.yudong.admin.infrastructure.motion.dao.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.api.motion.dto.qry.MotionPageQry;
import com.mrk.yudong.admin.infrastructure.motion.dao.MotionDao;
import com.mrk.yudong.admin.infrastructure.motion.mapper.MotionMapper;
import com.mrk.yudong.admin.infrastructure.motion.model.Motion;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Component;

@Component
public class MotionDaoImpl extends BaseServiceImpl<MotionMapper, Motion> implements MotionDao {

    @Override
    public Motion getById(Long id) {
        if (ObjectUtil.isNull(id)) {
            return null;
        }
        return this.baseMapper.selectById(id);
    }

    @Override
    public Motion getByFollowVideoId(Long followVideoId) {
        if (ObjectUtil.isNull(followVideoId)) {
            return null;
        }

        LambdaQueryWrapper<Motion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Motion::getFollowAloneVideoId, followVideoId);
        queryWrapper.last("limit 1");
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public Motion getByIntroduceVideoId(Long introduceId) {
        if (ObjectUtil.isNull(introduceId)) {
            return null;
        }
        LambdaQueryWrapper<Motion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Motion::getIntroduceVideoId, introduceId);
        queryWrapper.last("limit 1");
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public IPage<Motion> pageMotions(MotionPageQry pageQry) {
        LambdaQueryWrapper<Motion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotNull(pageQry.getId()), Motion::getId, pageQry.getId());
        queryWrapper.eq(ObjectUtil.isNotNull(pageQry.getProductId()), Motion::getProductId, pageQry.getProductId());
        queryWrapper.eq(ObjectUtil.isNotNull(pageQry.getStatus()), Motion::getStatus, pageQry.getStatus());
        queryWrapper.in(ObjectUtil.isNotNull(pageQry.getIds()), Motion::getId, pageQry.getIds());
        queryWrapper.likeRight(ObjectUtil.isNotNull(pageQry.getName()), Motion::getName, pageQry.getName());
        queryWrapper.eq(ObjectUtil.isNotNull(pageQry.getGrade()), Motion::getGrade, pageQry.getGrade());
        queryWrapper.orderByDesc(Motion::getUpdateTime);

        return baseMapper.selectPage(new Page<>(pageQry.getCurrent(), pageQry.getSize()), queryWrapper);
    }

    @Override
    public Motion update(Motion motion) {
        this.updateById(motion);
        return getById(motion.getId());
    }

    @Override
    public Motion create(Motion motion) {
        this.save(motion);
        return motion;
    }

    @Override
    public Motion getByName(String name) {
        LambdaQueryWrapper<Motion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Motion::getName, name);
        queryWrapper.last("limit 1");

        return baseMapper.selectOne(queryWrapper);
    }
}
