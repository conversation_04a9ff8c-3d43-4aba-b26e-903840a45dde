package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseScore;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 课程评分表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
public interface CourseScoreMapper extends BaseMapper<CourseScore> {

    /**
     * 获取课程评分统计
     *
     * @param courseId
     * @param column
     * @return
     */
    List<JSONObject> getScoreList(@Param("courseId") Long courseId, @Param("column") String column);

}
