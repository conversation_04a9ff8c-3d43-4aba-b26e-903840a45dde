package com.mrk.yudong.admin.infrastructure.sys.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 后台用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUser extends Model<SysUser> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 账号
     */
    @NotBlank(message = "账号不能为空")
    @Length(max = 16, message = "账号长度不能超过16位")
    private String username;

    /**
     * 密码
     */
    @JsonIgnore
    private String password;

    /**
     * 昵称
     */
    @NotBlank(message = "昵称不能为空")
    @Length(max = 16, message = "昵称长度不能超过16位")
    private String nickName;

    /**
     * 钉钉用户id
     */
    private String dingTalkUserId;
    /**
     * 角色名称
     */
    @TableField(exist = false)
    private String roleName;

    /**
     * 账号类型：1-系统账号，2-教练账号
     */
    @NotNull(message = "请选择账号类型")
    private Integer type;

    /**
     * 账号类型文字显示
     */
    @TableField(exist = false)
    private String typeDesc;

    /**
     * 状态：0-禁用，1-正常
     */
    private Integer status;

    /**
     * 状态文字显示
     */
    @TableField(exist = false)
    private String statusDesc;

    /**
     * 找教练头像
     */
    @TableField(exist = false)
    private String selectAvatar;
    /**
     * 彩屏端教练头像
     */
    @TableField(exist = false)
    private String colorScreenAvatar;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateBy;

    /**
     * 修改人
     */
    @TableField(exist = false)
    private String updateName;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否预发环境数据：0-否，1-是
     */
    @TableField(exist = false)
    private Integer isTra = BaseConstant.INT_FALSE;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
