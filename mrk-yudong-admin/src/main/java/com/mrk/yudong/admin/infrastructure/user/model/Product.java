package com.mrk.yudong.admin.infrastructure.user.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员商品表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class Product extends Model<Product> {


    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * VIP类型：10-VIP，20-SVIP，30-XVIP
     */
    private Integer vipType;

    /**
     * VIP类别：1-麦瑞克，2-绝影
     */
    private Integer brandType;

    /**
     * 购买的会员包类型
     * @see com.merach.sun.user.enums.vip.VipPackageTypeEnum
     */
    private Integer vipPackageType;

    /**
     * 名称
     */
    private String name;

    /**
     * 实际价格
     */
    private BigDecimal price;

    /**
     * 显示价格
     */
    private BigDecimal showPrice;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 类型：1-月，2-季，3-年
     */
    private Integer type;

    /**
     * 是否开启自动续费功能：0-否，1-是
     */
    private Integer isContinuity;

    /**
     * 特惠价格
     */
    private BigDecimal discountPrice;

    /**
     * 日计费起价
     */
    private BigDecimal dailyStartPrice;

    /**
     * 排序号
     */
    private Integer sort;

    /**
     * 终端：1-Android，2-IOS
     */
    private Integer terminal;

    /**
     * 商品编码
     */
    private String code;

    /**
     * 商品类型：1-售卖商品，2-奖项商品，3.vip促销活动
     */
    private Integer productType;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 是否隐藏：0-否，1-是
     */
    private Integer isHide;

    /**
     * 是否为预发数据：0-否，1-是
     */
    private Integer isTra;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 上线时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime onlineTime;

    /**
     * 下线时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime offlineTime;

    /**
     * 标签图
     */
    private String tagImage;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改人名称
     */
    @TableField(exist = false)
    private String updateName;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
