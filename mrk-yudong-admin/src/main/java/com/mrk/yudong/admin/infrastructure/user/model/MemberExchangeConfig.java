package com.mrk.yudong.admin.infrastructure.user.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.LinkedHashSet;

/**
 * <p>
 * 会员兑换规则配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MemberExchangeConfig extends Model<MemberExchangeConfig> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 显示名称
     */
    @NotBlank(message = "显示名称不能为空")
    @Length(max = 32, message = "显示名称长度不能超过32位")
    private String name;

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 店铺编码
     */
    @Size(min = 1, message = "请选择店铺")
    @TableField(exist = false)
    private LinkedHashSet<String> shopCodes;

    /**
     * 是否设置时间：0-否，1-是
     */
    @NotNull(message = "请选择是否设置时间")
    @Range(min = 0, max = 1, message = "是否设置时间数据不合法")
    private Integer isSetTime;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime endTime;

    /**
     * 是否设置SKU：0-否，1-是
     */
    @NotNull(message = "请选择是否设置SKU")
    @Range(min = 0, max = 1, message = "是否设置SKU数据不合法")
    private Integer isSetSku;

    /**
     * 商品编码，JSON数组
     */
    private String skuCode;

    /**
     * 商品编码
     */
    @TableField(exist = false)
    private LinkedHashSet<String> skuCodes;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量必须大于0")
    private Integer num;

    /**
     * 类型：1-月，2-季，3-年，4-日
     */
    @NotNull(message = "类型不能为空")
    private Integer type;

    /**
     * 类型文字显示
     */
    @TableField(exist = false)
    private String typeDesc;

    /**
     * 备注
     */
    @Length(max = 120, message = "备注长度不能超过120位")
    private String remark;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 状态文字显示
     */
    @TableField(exist = false)
    private String statusDesc;

    /**
     * 是否可修改
     */
    @TableField(exist = false)
    private Integer isEdit;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 最近一次操作人
     */
    @TableField(exist = false)
    private String updateName;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
