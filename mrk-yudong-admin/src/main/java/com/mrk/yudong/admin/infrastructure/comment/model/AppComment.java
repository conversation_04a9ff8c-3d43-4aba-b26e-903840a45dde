package com.mrk.yudong.admin.infrastructure.comment.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * app评论表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AppComment extends Model<AppComment> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 评价类型：1-app体验，2-课程内容
     */
    private Integer type;

    /**
     * 评价等级：1-非常差、2-差、3-一般、4-好、5-非常好
     */
    private Integer level;

    /**
     * 评价星级：1-1星、2-2星、3-3星、4-4星、5-5星
     */
    private Integer star;

    /**
     * 评价标签
     */
    private String label;

    /**
     * 其他意见
     */
    private String otherOpinion;

    /**
     * app版本号
     */
    private String appVersion;

    /**
     * 评价时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
