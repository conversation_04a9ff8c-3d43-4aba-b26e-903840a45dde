package com.mrk.yudong.admin.biz.sport.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.merach.sun.snowflake.core.SnowflakeTool;
import com.mrk.yudong.admin.biz.content.service.ILiveVideoService;
import com.mrk.yudong.admin.biz.course.service.ICourseService;
import com.mrk.yudong.admin.biz.sport.bo.TrainingDataBO;
import com.mrk.yudong.admin.biz.sport.service.SportService;
import com.mrk.yudong.admin.biz.sporttarget.enums.SportTargetSatatusEnum;
import com.mrk.yudong.admin.biz.user.service.ITrainDetailService;
import com.mrk.yudong.admin.infrastructure.content.model.LiveVideo;
import com.mrk.yudong.admin.infrastructure.course.model.Course;
import com.mrk.yudong.admin.infrastructure.sport.dao.TrainResultDao;
import com.mrk.yudong.admin.infrastructure.sporttarget.dao.SportTargetDao;
import com.mrk.yudong.admin.infrastructure.sporttarget.model.SportTarget;
import com.mrk.yudong.admin.infrastructure.user.model.TrainDetail;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.share.constant.training.TrainingTitleConstant;
import com.mrk.yudong.share.constant.user.TrainConstant;
import com.mrk.yudong.share.enums.DataSceneEnum;
import com.mrk.yudong.share.enums.training.TrainingModeEnum;
import com.mrk.yudong.share.enums.training.TrainingRecordStatusEnum;
import com.mrk.yudong.share.enums.training.TrainingTypeEnum;
import com.mrk.yudong.share.po.equip.EquipmentType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class SportServiceImpl implements SportService {

    private final ITrainDetailService trainDetailService;

    private final SportTargetDao sportTargetDao;

    private final TrainResultDao trainResultDao;

    private final ICourseService courseService;

    private final ILiveVideoService liveVideoService;

    private final StringRedisTemplate redisTemplate;

    /**
     * 补数据逻辑改为功能接口
     * 传入常见参数后块数据操作：
     * 1、直接增加/修改train_detail
     * 2、修改当天sport_target
     * 3、如果是修改，则删除对应train_result
     */
    @Override
    public Boolean repairTrainingData(TrainingDataBO trainingDataBO) {
        TrainDetail trainDetail = new TrainDetail();
        buildTrainDetail(trainingDataBO, trainDetail);
        if (Objects.isNull(trainingDataBO.getTrainingRecordId())) {
            //新增逻辑
            trainDetail.setId(SnowflakeTool.getId());
            trainDetailService.save(trainDetail);
        } else {
            //修改逻辑
            trainDetail.setId(trainingDataBO.getTrainingRecordId());
            trainDetailService.updateById(trainDetail);
            trainResultDao.removeByTrainId(trainingDataBO.getTrainingRecordId());
        }


        LocalDate createTime = trainingDataBO.getCreateTime().toLocalDate();
        //更新sport_target
        SportTarget sportTarget = sportTargetDao.getSportTargetByUserIdAndDay(trainingDataBO.getUserId(), createTime.toString());
        if (Objects.nonNull(sportTarget)) {
            List<TrainDetail> trainDetails = trainDetailService.getTrainDetailsByUserIdAndDate(trainingDataBO.getUserId(), createTime.atStartOfDay(), createTime.atTime(23, 59, 59));
            BigDecimal totalKcal = BigDecimal.valueOf(trainDetails.stream().mapToDouble(TrainDetail::getKcal).sum());
            Long totalTakeTime = (long) trainDetails.stream().mapToDouble(TrainDetail::getTakeTime).sum();
            sportTarget.setCalorie(totalKcal);
            sportTarget.setTime(totalTakeTime);
            if (sportTarget.getTargetCalorie().compareTo(totalKcal) >= 0 && sportTarget.getTargetTime().compareTo(totalTakeTime) >= 0) {
                sportTarget.setStatus(SportTargetSatatusEnum.FINISHED.getStatus());
            } else {
                sportTarget.setStatus(SportTargetSatatusEnum.IN_PROGRESS.getStatus());
            }
            sportTargetDao.updateSportTarget(sportTarget);
        }

        return true;
    }

    private void buildTrainDetail(TrainingDataBO trainingDataBO, TrainDetail trainDetail) {
        trainDetail.setType(trainingDataBO.getTrainingType());
        trainDetail.setTrainType(trainingDataBO.getTrainingMode());
        trainDetail.setCourseId(trainingDataBO.getCourseId());
        trainDetail.setUserId(trainingDataBO.getUserId());
        trainDetail.setEquipmentId(trainingDataBO.getProductId());
        trainDetail.setEquipmentInfoId(trainingDataBO.getEquipmentInfoId());
        trainDetail.setModelId(trainingDataBO.getProductModelId());
        trainDetail.setTakeTime(Long.valueOf(trainingDataBO.getDeviceTime()));
        trainDetail.setKcal(trainingDataBO.getKcal());
        trainDetail.setDistance(trainingDataBO.getDistance());
        trainDetail.setNum(trainingDataBO.getNum());
        trainDetail.setPlayTime(Long.valueOf(trainingDataBO.getPlayTime()));
        trainDetail.setStatus(TrainingRecordStatusEnum.TRAINING_COMPLETED.getCode());
        trainDetail.setTitle(setTrainDetailTitle(trainingDataBO));
        trainDetail.setCreateTime(trainingDataBO.getCreateTime());
        trainDetail.setStartTime(trainingDataBO.getCreateTime().minusSeconds(trainingDataBO.getDeviceTime()));
    }

    private String setTrainDetailTitle(TrainingDataBO trainDetail) {
        // 游戏数据单独处理，写死名称
        if (ObjectUtil.isNotNull(trainDetail.getDataScene()) && trainDetail.getDataScene().equals(DataSceneEnum.GAME.getCode())) {
            return TrainingTitleConstant.GAME_TRAINING_TITLE;
        }
        // iot物联网单独处理，写死名称
        if (Objects.equals(trainDetail.getTrainingType(), TrainConstant.TYPE_IOT_OFFLINE)) {
            return TrainingTitleConstant.IOT_OFFLINE_TRAINING_TITLE;
        }

        if (Objects.equals(trainDetail.getTrainingType(), TrainingTypeEnum.ROUTE_TRAINING.getCode())) {
            EquipmentType equipmentType = this.getEquipmentType(trainDetail.getProductId());
            String title = TrainingTitleConstant.FREE_TRAINING_TITLE_PREFIX;
            if (Objects.nonNull(equipmentType)) {
                title = title.concat(equipmentType.getTypeName());
            }
            return title.concat(TrainingTitleConstant.ROUTE_TRAINING_SUFFIX);
        }

        // 自由训练 + 顽鹿 + 自由训练活动
        if (Arrays.asList(TrainingTypeEnum.FREE_TRAINING.getCode(), TrainingTypeEnum.ONELAP_TRAINING.getCode()).contains(trainDetail.getTrainingType())
                || (Objects.equals(trainDetail.getTrainingType(), TrainConstant.TYPE_ACTIVITY) && (trainDetail.getCourseId() == null || trainDetail.getCourseId() <= 0))) {
            EquipmentType equipmentType = this.getEquipmentType(trainDetail.getProductId());
            //自由练前缀
            String title = TrainingTitleConstant.FREE_TRAINING_TITLE_PREFIX;
            if (Objects.nonNull(equipmentType)) {
                //设备类型名称
                title = title.concat(equipmentType.getTypeName());
            }
            //心率练
            if (Objects.nonNull(trainDetail.getCloudKey()) && StrUtil.isNotBlank(trainDetail.getCloudKey())) {
                title = title + TrainingTitleConstant.RATE_TRAINING_SUFFIX;
            }
            //定距练
            else if (Objects.equals(trainDetail.getTrainingMode(), TrainingModeEnum.DISTANCE_MODE.getCode())) {
                title = title + TrainingTitleConstant.DISTANCE_TRAINING_SUFFIX;
            }
            //定时练
            else if (Objects.equals(trainDetail.getTrainingMode(), TrainingModeEnum.TIME_MODE.getCode())) {
                title = title + TrainingTitleConstant.TIMING_TRAINING_SUFFIX;
            } else {
                title = title + TrainingTitleConstant.FREE_TRAINING_TITLE_SUFFIX;
            }
            //自由练
            return title;
        }
        // 实景视频
        if (Objects.equals(trainDetail.getTrainingType(), TrainingTypeEnum.LIVE_VIDEO_TRAINING.getCode())) {
            LiveVideo liveVideo = liveVideoService.getById(trainDetail.getCourseId());
            if (Objects.nonNull(liveVideo)) {
                return liveVideo.getTitle();
            }
        }
        // 课程训练 + 计划 + 课程活动
        if (Arrays.asList(TrainingTypeEnum.COURSE_TRAINING.getCode(), TrainConstant.TYPE_ACTIVITY, TrainConstant.TYPE_PLAN).contains(trainDetail.getTrainingType())) {
            Course course = courseService.getById(trainDetail.getCourseId());
            if (course != null) {
                return course.getName();
            }
        }
        log.warn("failed to get title. trainingRecordId: {}", trainDetail.getTrainingRecordId());
        return TrainingTitleConstant.MERIT_TRAINING_BASIC_TITLE;
    }

    private EquipmentType getEquipmentType(Long productId) {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(RedisKeyConstant.ALL_EQUIPMENT_TYPE_KEY);
        EquipmentType equipmentType = null;
        for (Map.Entry<Object, Object> entry : entries.entrySet()) {
            Long key = Long.valueOf(entry.getKey().toString());
            if (!key.equals(productId)) {
                continue;
            }
            equipmentType = JSON.parseObject(entry.getValue().toString(), EquipmentType.class);
            break;
        }
        return equipmentType;
    }

}

