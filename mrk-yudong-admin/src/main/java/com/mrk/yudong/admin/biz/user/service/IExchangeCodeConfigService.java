package com.mrk.yudong.admin.biz.user.service;

import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.api.user.dto.ExchangeCodeConfigDTO;
import com.mrk.yudong.admin.api.user.vo.ExchangeCodeConfigQueryVO;
import com.mrk.yudong.admin.infrastructure.user.model.ExchangeCodeConfig;
import com.mrk.yudong.core.service.BaseService;

import java.util.List;

/**
 * <p>
 * 会员兑换码配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
public interface IExchangeCodeConfigService extends BaseService<ExchangeCodeConfig> {

    /**
     * 新增兑换码
     * @param exchangeCodeConfigDTO
     * @return
     */
    List<ExchangeCodeConfigDTO> addExchangeCode(ExchangeCodeConfigDTO exchangeCodeConfigDTO);

    /**
     * 查询兑换会员码配置列表
     * @param exchangeCodeConfigQueryVO
     * @return
     */
    IPage<ExchangeCodeConfig> queryByPagination(ExchangeCodeConfigQueryVO exchangeCodeConfigQueryVO);

    /**
     * 根据id查询详情
     * @param id
     * @return
     */
    ExchangeCodeConfig queryByCode(Long id);

    /**
     * 根据兑换码查询详情
     * @param exchangeCode
     * @return
     */
    ExchangeCodeConfig queryByCode(String exchangeCode);

    /**
     * 修改兑换码配置
     * @param exchangeCodeConfigDTO
     * @return
     */
    boolean updateExchangeCodeConfig(ExchangeCodeConfigDTO exchangeCodeConfigDTO);

    /**
     * 根据id删除
     * @param id
     * @return
     */
    boolean deleteById(Long id);

    Boolean updateStatus(long id, Integer status);

    /**
     * 获取配置选择项
     * @return
     */
    Dict getOption();

    /**
     * 查询导出数据
     * @param exchangeCodeConfigQueryVO
     * @return
     */
    List<JSONObject> queryExportData(ExchangeCodeConfigQueryVO exchangeCodeConfigQueryVO);



}
