package com.mrk.yudong.admin.infrastructure.app.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 设备百科
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DeviceEncyclopedia extends Model<DeviceEncyclopedia> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分类百科ID
     */
    private Long categoryEncyclopediaId;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备图片
     */
    private String imgUrl;

    /**
     * 新手引导_设备类型ID
     */
    private Long guidanceEquipTypeId;

    /**
     * 常见问题_设备类型ID
     */
    private Long issueEquipTypeId;

    /**
     * 安装教程
     */
    private String installTutorialUrl;

    /**
     * 使用教程
     */
    private String useTutorialUrl;

    /**
     * 设备保养
     */
    private String maintenanceUrl;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
