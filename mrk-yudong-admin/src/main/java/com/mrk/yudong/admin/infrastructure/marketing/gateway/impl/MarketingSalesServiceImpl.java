package com.mrk.yudong.admin.infrastructure.marketing.gateway.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.api.marketing.dto.cmd.CreateMarketingSalesCmd;
import com.mrk.yudong.admin.api.marketing.dto.cmd.SetMarketingSalesGoodsRelCmd;
import com.mrk.yudong.admin.api.marketing.dto.cmd.UpdateMarketingSalesCmd;
import com.mrk.yudong.admin.api.marketing.dto.qry.MarketingSalesSearchQry;
import com.mrk.yudong.admin.api.marketing.dto.resp.MarketingSalesInfoDTO;
import com.mrk.yudong.admin.biz.sys.service.ISysUserService;
import com.mrk.yudong.admin.feign.AppFeign;
import com.mrk.yudong.admin.infrastructure.marketing.enums.MarketingSalesStatusEnum;
import com.mrk.yudong.admin.infrastructure.marketing.gateway.IMarketingSalesGoodsRelService;
import com.mrk.yudong.admin.infrastructure.marketing.gateway.IMarketingSalesService;
import com.mrk.yudong.admin.infrastructure.marketing.mapper.MarketingSalesMapper;
import com.mrk.yudong.admin.infrastructure.marketing.model.MarketingSales;
import com.mrk.yudong.admin.infrastructure.marketing.model.MarketingSalesGoodsRel;
import com.mrk.yudong.admin.infrastructure.sys.model.SysUser;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.share.dto.admin.marketing.goods.MarketingGoodsDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MarketingSalesServiceImpl extends BaseServiceImpl<MarketingSalesMapper, MarketingSales> implements IMarketingSalesService {
    private final ISysUserService sysUserService;

    private final IMarketingSalesGoodsRelService marketingSalesGoodsRelService;

    private final AppFeign appFeign;
    @Override
    public Boolean create(CreateMarketingSalesCmd cmd) {
        MarketingSales marketingSales = BeanUtil.copyProperties(cmd, MarketingSales.class);
        marketingSales.setCreateId(SessionUtil.getId());
        marketingSales.setUpdateId(SessionUtil.getId());
        marketingSales.setUpdateTime(LocalDateTime.now());
        marketingSales.setStatus(MarketingSalesStatusEnum.NOT_STARTED.getCode());
        marketingSales.setCountDownHour(cmd.getCountDownHour() * 24);
        boolean saveMarketingSales = save(marketingSales);
        if (!saveMarketingSales) {
            log.warn("保存营销活动失败");
            return Boolean.FALSE;
        }

        return marketingSalesGoodsRelService.saveBatch(cmd.getSetMarketingSalesGoods().stream().map(
                setMarketingSalesGoodsRelCmd -> {
                    MarketingSalesGoodsRel marketingSalesGoodsRel = BeanUtil.copyProperties(setMarketingSalesGoodsRelCmd, MarketingSalesGoodsRel.class);
                    marketingSalesGoodsRel.setCreateId(SessionUtil.getId());
                    marketingSalesGoodsRel.setSalesId(marketingSales.getId());
                    return marketingSalesGoodsRel;
                }
        ).collect(Collectors.toList()));
    }

    @Override
    public Boolean update(UpdateMarketingSalesCmd cmd) {
        MarketingSales marketingSales = BeanUtil.copyProperties(cmd, MarketingSales.class);
        marketingSales.setUpdateId(SessionUtil.getId());
        marketingSales.setCountDownHour(cmd.getCountDownHour() * 24);
        boolean updateMarketingSales = updateById(marketingSales);
        if (!updateMarketingSales) {
            log.warn("修改营销活动失败");
            return Boolean.FALSE;
        }
        marketingSalesGoodsRelService.remove(
                new QueryWrapper<MarketingSalesGoodsRel>().lambda()
                        .eq(MarketingSalesGoodsRel::getSalesId, marketingSales.getId()));

        return marketingSalesGoodsRelService.saveBatch(cmd.getSetMarketingSalesGoods().stream().map(
                setMarketingSalesGoodsRelCmd -> {
                    MarketingSalesGoodsRel marketingSalesGoodsRel = BeanUtil.copyProperties(setMarketingSalesGoodsRelCmd, MarketingSalesGoodsRel.class);
                    marketingSalesGoodsRel.setCreateId(SessionUtil.getId());
                    marketingSalesGoodsRel.setSalesId(marketingSales.getId());
                    return marketingSalesGoodsRel;
                }
        ).collect(Collectors.toList()));
    }

    @Override
    public Boolean hasTimeConflict(LocalDateTime beginTime, LocalDateTime endTime, Long excludeId) {
        QueryWrapper<MarketingSales> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .lt(MarketingSales::getBeginTime, endTime)
                .gt(MarketingSales::getEndTime, beginTime)
                .ne(excludeId != null, MarketingSales::getId, excludeId);
        return count(queryWrapper) > 0;
    }

    @Override
    public boolean changeSalesStatus(Long id, Integer status) {
        MarketingSales marketingSales = getById(id);
        if (marketingSales == null) {
            return false;
        }

        marketingSales.setStatus(status);
        return updateById(marketingSales);
    }

    @Override
    public Page<MarketingSalesInfoDTO> getSalesByConditions(MarketingSalesSearchQry searchDTO) {
        QueryWrapper<MarketingSales> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(searchDTO.getSalesId() != null, MarketingSales::getId, searchDTO.getSalesId())
                .like(StrUtil.isNotBlank(searchDTO.getName()), MarketingSales::getName, searchDTO.getName())
                .eq(searchDTO.getStatus() != null, MarketingSales::getStatus, searchDTO.getStatus())
                .ge(searchDTO.getBeginTime() != null, MarketingSales::getBeginTime, searchDTO.getBeginTime())
                .le(searchDTO.getEndTime() != null, MarketingSales::getEndTime, searchDTO.getEndTime());
        Page<MarketingSales> page = page(new Page<>(searchDTO.getCurrent(), searchDTO.getSize()), queryWrapper);
        if (CollUtil.isEmpty(page.getRecords())) {
            return new Page<>(searchDTO.getCurrent(), searchDTO.getSize());
        }


        Page<MarketingSalesInfoDTO> result = new Page<>(searchDTO.getCurrent(), searchDTO.getSize(), page.getTotal());
        Map<Long, String> userMap = sysUserService.listByIds(page.getRecords().stream().map(MarketingSales::getUpdateId).collect(Collectors.toList())).stream().collect(Collectors.toMap(SysUser::getId, SysUser::getNickName));

        result.setRecords(page.getRecords().stream().map(sales -> {
            MarketingSalesInfoDTO marketingSalesInfoDTO = BeanUtil.copyProperties(sales, MarketingSalesInfoDTO.class);
            if (MapUtil.isNotEmpty(userMap) && sales.getUpdateId() != null) {
                String name = MapUtil.getStr(userMap, sales.getUpdateId());
                if (StrUtil.isNotBlank(name)) {
                    marketingSalesInfoDTO.setOperatorName(name);
                }
            }
            List<MarketingSalesGoodsRel> list = marketingSalesGoodsRelService.list(new QueryWrapper<MarketingSalesGoodsRel>().lambda().eq(MarketingSalesGoodsRel::getSalesId, sales.getId()));
            ResDTO<List<MarketingGoodsDTO>> goodsInfosDto = appFeign.findGoodsByIds(list.stream().map(MarketingSalesGoodsRel::getGoodsId).collect(Collectors.toList()));
            List<MarketingGoodsDTO> goodInfos = CollectionUtil.isEmpty(goodsInfosDto.getData()) ? new ArrayList<>() : goodsInfosDto.getData();
            Map<Long, MarketingGoodsDTO> goodsMap = goodInfos.stream().collect(Collectors.toMap(MarketingGoodsDTO::getId, v -> v));
            if (CollUtil.isNotEmpty(list)) {
                marketingSalesInfoDTO.setGoodsCount(list.size());
                List<SetMarketingSalesGoodsRelCmd> goods = BeanUtil.copyToList(list, SetMarketingSalesGoodsRelCmd.class);
                for (SetMarketingSalesGoodsRelCmd good : goods) {
                    if (goodsMap.containsKey(good.getGoodsId())) {
                        good.setStatus(goodsMap.get(good.getGoodsId()).getStatus());
                        good.setName(goodsMap.get(good.getGoodsId()).getName());
                        good.setImage(goodsMap.get(good.getGoodsId()).getImage());
                        good.setPrice(goodsMap.get(good.getGoodsId()).getPrice());
                        good.setMarkedPrice(goodsMap.get(good.getGoodsId()).getMarkedPrice());
                    }

                }
                marketingSalesInfoDTO.setSetMarketingSalesGoods(goods);
            }
            // 获取状态的描述
            marketingSalesInfoDTO.setStatusName(MarketingSalesStatusEnum.fromCode(sales.getStatus()).getDescription());
            marketingSalesInfoDTO.setOperationTime(sales.getUpdateTime());
            marketingSalesInfoDTO.setCountDownHour(sales.getCountDownHour() / 24);
            return marketingSalesInfoDTO;
        }).collect(Collectors.toList()));
        return result;
    }

}
