package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.admin.api.course.po.PlanCourseInfoPO;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 课程训练计划关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CoursePlanAssociated extends Model<CoursePlanAssociated> {

    private static final long serialVersionUID = 1L;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 课程id
     */
    private Long courseId;
    /**
     * 计划id
     */
    private Long planId;

    /**
     * 类型1上课2休息日
     */
    private Integer type;

    /**
     * 天数
     */
    private Integer days;

    /**
     * 所属周
     */
    private Integer byWeek;
    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    /**
     * 课程信息
     */
    @TableField(exist = false)
    private List<PlanCourseInfoPO> courseInfoList;

    /**
     * 课程数
     */
    @TableField(exist = false)
    private Integer count;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
