package com.mrk.yudong.admin.biz.marketing.service.impl;

import com.mrk.yudong.admin.biz.marketing.bo.ActivityReapplyBO;
import com.mrk.yudong.admin.biz.marketing.service.ActivityService;
import com.mrk.yudong.admin.infrastructure.marketing.dao.MarketingActivityUserEnrollDao;
import com.mrk.yudong.admin.infrastructure.marketing.model.MarketingActivityUserEnroll;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityServiceImpl implements ActivityService {
    private final MarketingActivityUserEnrollDao marketingActivityUserEnrollDao;


    @Override
    public Boolean reapply(ActivityReapplyBO reapplyBO) {
        MarketingActivityUserEnroll enroll = marketingActivityUserEnrollDao.getByUserIdAndActivityId(reapplyBO.getUserId(), reapplyBO.getActivityId());
        if (enroll == null) {
            log.info("reapply -> 用户未报名活动, userId: {}, activityId: {}", reapplyBO.getUserId(), reapplyBO.getActivityId());
            return false;
        }
        MarketingActivityUserEnroll enrollToUpdate = new MarketingActivityUserEnroll();
        enrollToUpdate.setId(enroll.getId());
        enrollToUpdate.setGroupId(reapplyBO.getGroupId());
        enrollToUpdate.setProductId(reapplyBO.getProductId());
        return marketingActivityUserEnrollDao.updateActivityUserEnrollById(enrollToUpdate);
    }
}
