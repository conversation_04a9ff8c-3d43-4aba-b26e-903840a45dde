package com.mrk.yudong.admin.infrastructure.search.gateway.impl;

import com.merach.sun.common.layer.web.PageDTO;
import com.merach.sun.data.api.SearchApi;
import com.merach.sun.data.dto.qry.user.UserPageQry;
import com.merach.sun.data.dto.resp.user.UserInfoDTO;
import com.mrk.yudong.admin.infrastructure.search.gateway.SearchGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class SearchGatewayImpl implements SearchGateway {
    private final SearchApi searchApi;

    @Override
    public PageDTO<UserInfoDTO> pageQueryUsers(UserPageQry userPageQry) {
        log.info("SearchGatewayImpl->pageQueryUsers->qry: {}", userPageQry);
        try {
            return searchApi.pageUsers(userPageQry);
        } catch (Throwable e) {
            log.error("SearchGatewayImpl->pageQueryUsers->error, message:{}", e.getMessage(), e);
            throw e;
        }
    }
}
