package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 课程关联标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CourseTagRel extends Model<CourseTagRel> {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 标签id
     */
    private Long tagId;

    /**
     * 状态0正常
     */
    private Integer status;

    /**
     * 逻辑删除
     */
    private Integer isDelete;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Long updateId;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
