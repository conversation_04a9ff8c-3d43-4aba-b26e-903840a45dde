package com.mrk.yudong.admin.biz.course.bo;

import lombok.Data;

@Data
public class CourseOptionBO {
    /**
     * 课程id
     */
    private Long id;

    /**
     * 课程名称
     */
    private String name;

    /**
     * 课程封面
     */
    private String cover;

    /**
     * 教练id
     */
    private Long coachId;

    /**
     * 教练名称
     */
    private String coachName;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 难度
     */
    private Integer grade;

    /**
     * 难度描述
     */
    private String gradeDesc;

    /**
     * 分类
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 课程时长
     */
    private Integer courseTime;
}
