package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 课程反馈表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CourseFeedback extends Model<CourseFeedback> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 类型：1-中途退出，2-完成训练
     */
    private Integer type;

    /**
     * 播放课程状态：1-直播，2-录播
     */
    private Integer playStatus;

    /**
     * 训练感受
     */
    private String content;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
