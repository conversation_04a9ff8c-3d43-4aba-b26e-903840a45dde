package com.mrk.yudong.admin.infrastructure.device.gateway;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.merach.sun.device.dto.qry.ProductModelFirmwareQry;
import com.mrk.yudong.admin.api.device.dto.cmd.firmware.CreateModelFirmwareCmd;
import com.mrk.yudong.admin.api.device.dto.cmd.firmware.UpdateModelFirmwareCmd;
import com.mrk.yudong.admin.api.device.dto.resp.firmware.BackstageModelFirmwareDetailDTO;
import com.mrk.yudong.admin.api.device.dto.resp.firmware.HistoryModelFirmwareDetailDTO;
import com.mrk.yudong.admin.api.device.dto.resp.firmware.PageModelFirmwareDTO;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.RequestBody;

/** 固件更新
 * <AUTHOR>
 * @create 2023−02-10 13:43
 */
public interface ProductModelFirmwareGateway {
    BackstageModelFirmwareDetailDTO createProductModelFirmware(@RequestBody CreateModelFirmwareCmd cmd);


    BackstageModelFirmwareDetailDTO updateProductModelFirmware(@RequestBody UpdateModelFirmwareCmd cmd);

    Boolean deleteProductModelFirmware(Long id);


    BackstageModelFirmwareDetailDTO getProductModelFirmware(@SpringQueryMap ProductModelFirmwareQry productModelFirmwareQry);


    Page<HistoryModelFirmwareDetailDTO> pageHistoryModelFirmwares(@SpringQueryMap ProductModelFirmwareQry productModelFirmwareQry);


    Page<PageModelFirmwareDTO> pageProductModelFirmwares(@SpringQueryMap ProductModelFirmwareQry productModelFirmwareQry);

}
