package com.mrk.yudong.admin.biz.exclusive.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.merach.sun.misc.enums.ContentSkipEnum;
import com.merach.sun.user.api.ExclusiveApi;
import com.merach.sun.user.api.ExclusiveGroupApi;
import com.merach.sun.user.dto.exclusive.cmd.ExclusiveGroupCmd;
import com.merach.sun.user.dto.exclusive.qry.ExclusiveGroupQry;
import com.merach.sun.user.dto.level.ExclusiveGroupDTO;
import com.merach.sun.user.enums.vip.ExclusiveTypeEnum;
import com.mrk.yudong.admin.api.exclusive.dto.ExclusiveGroupDataDTO;
import com.mrk.yudong.admin.api.exclusive.dto.ExclusiveGroupOptionDTO;
import com.mrk.yudong.admin.api.exclusive.dto.LinkValDTO;
import com.mrk.yudong.admin.api.exclusive.query.ExclusiveGroupQuery;
import com.mrk.yudong.admin.api.exclusive.vo.ExclusiveGroupVo;
import com.mrk.yudong.admin.api.exclusive.vo.LinkValVo;
import com.mrk.yudong.admin.biz.exclusive.service.IExclusiveGroupService;
import com.mrk.yudong.admin.biz.sys.service.ISysUserService;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.share.vo.common.OptionVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/10/17 14:46
 */
@RequiredArgsConstructor
@Service
public class ExclusiveGroupServiceImpl implements IExclusiveGroupService {

    private final ExclusiveGroupApi exclusiveGroupApi;

    private final ExclusiveApi exclusiveApi;

    private final ISysUserService sysUserService;

    @Override
    public PageDTO<ExclusiveGroupDataDTO> page(ExclusiveGroupQuery query) {
        ExclusiveGroupQry qry = BeanUtil.copyProperties(query, ExclusiveGroupQry.class);
        qry.setId(query.getGroupId());
        com.merach.sun.common.layer.web.PageDTO<ExclusiveGroupDTO> page = exclusiveGroupApi.page(qry);
        List<ExclusiveGroupDTO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageDTO.of(query.getCurrent(), query.getSize(), page.getTotal());
        }

        Set<Long> userIds = records.stream().flatMap(v -> Stream.of(v.getCreateId(), v.getUpdateId())).collect(Collectors.toSet());
        Map<Long, String> userNameData = sysUserService.getOperationUserNameData(userIds);
        List<ExclusiveGroupDataDTO> list = records.stream().map(v -> {
            ExclusiveGroupDataDTO groupDataDTO = BeanUtil.copyProperties(v, ExclusiveGroupDataDTO.class);
            String linkVal = v.getLinkVal();
            if (StrUtil.isNotBlank(linkVal)) {
                LinkValDTO linkValData = JSON.parseObject(linkVal, LinkValDTO.class);
                Optional.ofNullable(ContentSkipEnum.get(linkValData.getContentType())).ifPresent(e -> linkValData.setContentTypeName(e.getName()));
                groupDataDTO.setLinkValData(linkValData);
            }

            Optional.ofNullable(userNameData.get(v.getCreateId())).ifPresent(groupDataDTO::setCreateName);
            Optional.ofNullable(userNameData.get(v.getUpdateId())).ifPresent(groupDataDTO::setUpdateName);
            Optional.ofNullable(ExclusiveTypeEnum.get(v.getType())).ifPresent(e -> groupDataDTO.setTypeName(e.getDesc()));
            Optional.ofNullable(ContentSkipEnum.get(v.getContentType())).ifPresent(e -> groupDataDTO.setContentTypeName(e.getName()));

            return groupDataDTO;
        }).collect(Collectors.toList());

        return PageDTO.of(query.getCurrent(), query.getSize(), page.getTotal(), list);
    }

    @Override
    public ExclusiveGroupOptionDTO option() {
        ExclusiveGroupOptionDTO optionDTO = new ExclusiveGroupOptionDTO();
        optionDTO.setTypes(Stream.of(ExclusiveTypeEnum.values()).map(v -> new OptionVo(v.getDesc(), v.getType().toString())).collect(Collectors.toList()));
        optionDTO.setContentTypes(Stream.of(ContentSkipEnum.values()).map(v -> new OptionVo(v.getName(), v.getCode())).collect(Collectors.toList()));
        return optionDTO;
    }

    @Override
    public ResDTO<Boolean> save(ExclusiveGroupVo exclusiveGroupVo) {
        ResDTO<Boolean> resDTO = this.checkParam(exclusiveGroupVo);
        if (ResDTO.isFail(resDTO)) {
            return resDTO;
        }

        if (exclusiveGroupApi.hasCode(exclusiveGroupVo.getCode())) {
            return ResDTO.paramFail("编码已被使用");
        }

        ExclusiveGroupCmd exclusiveGroupCmd = this.buildExclusiveGroupCmd(exclusiveGroupVo);
        exclusiveGroupCmd.setCreateId(SessionUtil.getId());
        return exclusiveGroupApi.save(exclusiveGroupCmd) ? ResDTO.ok(true) : ResDTO.fail();
    }

    private ResDTO<Boolean> checkParam(ExclusiveGroupVo exclusiveGroupVo) {
        ExclusiveTypeEnum typeEnum = ExclusiveTypeEnum.get(exclusiveGroupVo.getType());
        if (typeEnum == null) {
            return ResDTO.paramFail("无效的分组类型");
        }

        // 等级权益分组
        if (typeEnum == ExclusiveTypeEnum.USER_LEVEL) {
            if (StrUtil.isBlank(exclusiveGroupVo.getLockCover())) {
                return ResDTO.paramFail("未解锁封面不能为空");
            }

            if (exclusiveGroupVo.getLevel() == null) {
                return ResDTO.paramFail("解锁等级不能为空");
            }

            if (StrUtil.isBlank(exclusiveGroupVo.getLevelColorIcon())) {
                return ResDTO.paramFail("等级颜色icon不能为空");
            }

            LinkValVo linkValData = exclusiveGroupVo.getLinkValData();
            if (StrUtil.isNotBlank(exclusiveGroupVo.getButtonText()) && linkValData == null) {
                return ResDTO.paramFail("跳转配置不能为空");
            }

            if (linkValData != null && ContentSkipEnum.notHas(linkValData.getContentType())) {
                return ResDTO.paramFail("无效的链接跳转方式");
            }
        }

        // 会员权益分组
        if (typeEnum == ExclusiveTypeEnum.VIP_BENEFIT) {
            if (StrUtil.isBlank(exclusiveGroupVo.getSubTitle())) {
                return ResDTO.paramFail("副标题不能为空");
            }

            String contentType = exclusiveGroupVo.getContentType();
            String content = exclusiveGroupVo.getContent();
            if (StrUtil.isNotBlank(contentType)) {
                if (ContentSkipEnum.notHas(contentType)) {
                    return ResDTO.paramFail("无效的链接跳转方式");
                }

                if (StrUtil.isBlank(content)) {
                    return ResDTO.paramFail("请配置链接跳转内容");
                }
            }

            if (StrUtil.isNotBlank(content) && StrUtil.isBlank(contentType)) {
                return ResDTO.paramFail("请选择链接跳转方式");
            }

            if (StrUtil.isBlank(exclusiveGroupVo.getIcon())) {
                return ResDTO.paramFail("权益icon不能为空");
            }
        }

        return ResDTO.ok(true);
    }

    private ExclusiveGroupCmd buildExclusiveGroupCmd(ExclusiveGroupVo exclusiveGroupVo) {
        ExclusiveGroupCmd exclusiveGroupCmd = BeanUtil.copyProperties(exclusiveGroupVo, ExclusiveGroupCmd.class);
        LinkValVo linkValData = exclusiveGroupVo.getLinkValData();
        if (linkValData != null) {
            exclusiveGroupCmd.setLinkVal(JSON.toJSONString(linkValData));
        } else {
            exclusiveGroupCmd.setLinkVal(StrUtil.EMPTY);
        }

        exclusiveGroupCmd.setUpdateId(SessionUtil.getId());
        return exclusiveGroupCmd;
    }

    @Override
    public ResDTO<Boolean> update(Long groupId, ExclusiveGroupVo exclusiveGroupVo) {
        ResDTO<Boolean> resDTO = this.checkParam(exclusiveGroupVo);
        if (ResDTO.isFail(resDTO)) {
            return resDTO;
        }

        if (!exclusiveGroupApi.hasData(groupId)) {
            return ResDTO.fail("无效的分组信息");
        }

        // 编码不支持修改
        exclusiveGroupVo.setCode(null);
        ExclusiveGroupCmd exclusiveGroupCmd = this.buildExclusiveGroupCmd(exclusiveGroupVo);
        return exclusiveGroupApi.update(groupId, exclusiveGroupCmd) ? ResDTO.ok(true) : ResDTO.fail();
    }

    @Override
    public ResDTO<Boolean> remove(Long groupId) {
        if (!exclusiveGroupApi.hasData(groupId)) {
            return ResDTO.fail("无效的分组信息");
        }

        if (exclusiveApi.hasUseGroupData(groupId)) {
            return ResDTO.fail("该分组下存在权益数据，无法删除");
        }

        return exclusiveGroupApi.remove(groupId, SessionUtil.getId()) ? ResDTO.ok(true) : ResDTO.fail();
    }

}
