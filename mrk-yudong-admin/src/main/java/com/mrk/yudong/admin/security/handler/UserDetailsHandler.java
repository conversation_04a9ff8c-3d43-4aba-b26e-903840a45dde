package com.mrk.yudong.admin.security.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.admin.biz.coach.service.ICoachInfoService;
import com.mrk.yudong.admin.biz.sys.service.ISysResourceService;
import com.mrk.yudong.admin.biz.sys.service.ISysRoleService;
import com.mrk.yudong.admin.biz.sys.service.ISysUserService;
import com.mrk.yudong.admin.constant.AdminConstant;
import com.mrk.yudong.admin.infrastructure.coach.model.CoachInfo;
import com.mrk.yudong.admin.infrastructure.sys.model.SysResource;
import com.mrk.yudong.admin.infrastructure.sys.model.SysUser;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.utils.JwtUtil;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.share.vo.SysMessageVO;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;

import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class UserDetailsHandler implements UserDetailsService {

    private static final AntPathMatcher ANT_PATH_MATCHER = new AntPathMatcher();

    private final ISysUserService sysUserService;

    private final ISysRoleService sysRoleService;

    private final ISysResourceService sysResourceService;

    private final StringRedisTemplate redisTemplate;

    private final ICoachInfoService coachInfoService;

    private final JwtUtil jwtUtil;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        SysUser sysUser = sysUserService.getOne("username", ConditionEnum.EQ, username);
        if (sysUser == null) {
            return null;
        }

        Long coachId = null;
        if (sysUser.getType().equals(AdminConstant.USER_COACH)) {
            CoachInfo coachInfo = coachInfoService.getOne("sys_user_id", ConditionEnum.EQ, sysUser.getId(), "id");
            if (coachInfo != null) {
                coachId = coachInfo.getId();
            }
        }
        return new User(username, sysUser.getPassword(), this.authorities(sysUser.getId(), coachId));
    }

    private Set<GrantedAuthority> authorities(Long sysUserId, Long coachId) {
        Set<GrantedAuthority> authorities;
        List<JSONObject> list = this.getResourceList(sysUserId, coachId);

        if (CollUtil.isEmpty(list)) {
            authorities = new HashSet<>(1);
            GrantedAuthority grantedAuthority = new SimpleGrantedAuthority("GUST");
            authorities.add(grantedAuthority);
            return authorities;
        }

        authorities = list.stream().map(obj -> {
            String roleCode = obj.getString("code") + "@" + obj.getString("method");
            return new SimpleGrantedAuthority(roleCode);
        }).collect(Collectors.toSet());

        return authorities;
    }

    public List<JSONObject> getResourceList(Long sysUserId, Long coachId) {
        String key = RedisKeyConstant.SYS_USER_RESOURCE.replace("${sysUserId}", sysUserId.toString());
        List<String> list = redisTemplate.opsForList().range(key, 0, -1);
        if (CollUtil.isNotEmpty(list)) {
            return list.stream().map(JSONObject::parseObject).collect(Collectors.toList());
        }

        boolean isAdmin = sysRoleService.isAdmin(sysUserId);
        List<SysResource> resources;

        if (isAdmin) {
            resources = sysResourceService.list("status", ConditionEnum.EQ, BaseConstant.INT_TRUE);
        } else {
            Set<Long> sysRoleIds = sysRoleService.getSysRoleIdsBySysUserId(sysUserId);
            if (CollUtil.isEmpty(sysRoleIds)) {
                sysRoleIds = new HashSet<>();
            }

            resources = sysResourceService.listBySysRoleIds(sysRoleIds);
        }

        if (CollUtil.isEmpty(resources)) {
            return null;
        }

        List<JSONObject> collect = resources.stream().map(sysResource -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", sysResource.getId());
            jsonObject.put("name", sysResource.getName());
            jsonObject.put("code", sysResource.getCode());
            jsonObject.put("url", sysResource.getUrl());
            jsonObject.put("method", sysResource.getMethod());
            redisTemplate.opsForList().leftPush(key, JSON.toJSONString(jsonObject));
            return jsonObject;
        }).collect(Collectors.toList());
        redisTemplate.expire(key, Duration.ofHours(AdminConstant.TOKEN_TIME));

        Boolean hasKey = redisTemplate.opsForHash().hasKey(RedisKeyConstant.SYS_RESOURCE, sysUserId.toString());
        if (!hasKey) {
            redisTemplate.opsForHash().put(RedisKeyConstant.SYS_RESOURCE, sysUserId.toString(), key);
        }

        this.buildSysMessageVO(collect, sysUserId, coachId);

        return collect;
    }

    public SysMessageVO buildSysMessageVO(List<JSONObject> resources, Long sysUserId, Long coachId) {
        if (CollUtil.isEmpty(resources)) {
            SysMessageVO sysMessageVO = new SysMessageVO();
            sysMessageVO.setCoachType(0);
            sysMessageVO.setIsMsg(0);
            sysMessageVO.setFeedbackType(0);
            return sysMessageVO;
        }
        int isMsg = 0;
        // 教练审核身份类型：0-无推送，1-教练，2-主管，3-同为教练和主管
        int coachType = 0;
        if (coachId != null) {
            coachType = 1;
            isMsg = 1;
        }

        boolean anyMatch = resources.stream().anyMatch(dict -> StrUtil.equals(dict.getString("code"), AdminConstant.DEFAULT_COURSE_COACH_MANAGE_CODE));
        if (anyMatch) {
            coachType = 2;
            isMsg = 1;
        }

        if (coachId != null && anyMatch) {
            coachType = 3;
            isMsg = 1;
        }

        // 意见反馈身份类型：0-无推送，1-处理人身份
        int feedbackType = 0;
        anyMatch = resources.stream().anyMatch(dict -> StrUtil.equals(dict.getString("code"), AdminConstant.DEFAULT_FEEDBACK_MANAGE_CODE));
        if (anyMatch) {
            feedbackType = 1;
            isMsg = 1;
        }

        int pdType = 0;
        anyMatch = resources.stream().anyMatch(dict -> StrUtil.equals(dict.getString("code"), AdminConstant.DEFAULT_COURSE_PD_CODE));
        if (anyMatch) {
            pdType = 1;
            isMsg = 1;
        }

        SysMessageVO sysMessageVO = new SysMessageVO(isMsg, coachType, feedbackType, pdType);
        String sysMsgKey = RedisKeyConstant.SYS_MESSAGE_KEY.replace("${sysUserId}", sysUserId.toString());
        Boolean hasKey = redisTemplate.hasKey(sysMsgKey);
        if (hasKey != null && !hasKey) {
            redisTemplate.opsForValue().set(sysMsgKey, JSON.toJSONString(sysMessageVO), Duration.ofHours(AdminConstant.TOKEN_TIME));
        }

        return sysMessageVO;
    }

    public boolean hasPermission(HttpServletRequest request, Authentication authentication) {
        Object principal = authentication.getPrincipal();
        if (principal == null) {
            return false;
        }

        String authorization = request.getHeader(HttpHeaders.AUTHORIZATION);
        if (!jwtUtil.checkToken(authorization)) {
            return false;
        }

        List<JSONObject> list = this.getResourceList(jwtUtil.getId(authorization), jwtUtil.getCoachId(authorization));
        if (CollUtil.isEmpty(list)) {
            return false;
        }

        String requestURI = request.getRequestURI();
        String method = request.getMethod();

        return list.stream().filter(obj -> StrUtil.isNotBlank(obj.getString("url"))).anyMatch(obj -> ANT_PATH_MATCHER.match(obj.getString("url"), requestURI)
                && StrUtil.equals(method, obj.getString("method")));
    }


}
