package com.mrk.yudong.admin.infrastructure.motion.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mrk.yudong.admin.infrastructure.motion.dao.MotionCategoryRelDao;
import com.mrk.yudong.admin.infrastructure.motion.mapper.MotionCategoryRelMapper;
import com.mrk.yudong.admin.infrastructure.motion.model.MotionCategoryRel;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class MotionCategoryRelDaoImpl extends BaseServiceImpl<MotionCategoryRelMapper, MotionCategoryRel> implements MotionCategoryRelDao {
    @Override
    public List<MotionCategoryRel> findByMotionIds(Set<Long> motionIds) {
        LambdaQueryWrapper<MotionCategoryRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MotionCategoryRel::getMotionId, motionIds);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public Boolean deleteByMotionId(Long motionId) {
        LambdaQueryWrapper<MotionCategoryRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MotionCategoryRel::getMotionId, motionId);
        return baseMapper.delete(queryWrapper) > 0;
    }

    @Override
    public Boolean batchCreate(List<MotionCategoryRel> motionCategoryRelList) {
        return saveBatch(motionCategoryRelList);
    }

    @Override
    public Set<Long> findMotionIdsByCategoryId(Long categoryId) {
        QueryWrapper<MotionCategoryRel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category_id", categoryId);
        queryWrapper.select("DISTINCT motion_id");
        List<MotionCategoryRel> result = this.baseMapper.selectList(queryWrapper);
        return CollectionUtil.isNotEmpty(result) ? result.stream().map(MotionCategoryRel::getMotionId).collect(Collectors.toSet()) : Set.of(-1L);
    }

    @Override
    public Set<Long> findMotionIdsBySubCategoryId(Long subCategoryId) {
        QueryWrapper<MotionCategoryRel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sub_category_id", subCategoryId);
        queryWrapper.select("DISTINCT motion_id");
        List<MotionCategoryRel> result = this.baseMapper.selectList(queryWrapper);
        return CollectionUtil.isNotEmpty(result) ? result.stream().map(MotionCategoryRel::getMotionId).collect(Collectors.toSet()) : Set.of(-1L);
    }
}
