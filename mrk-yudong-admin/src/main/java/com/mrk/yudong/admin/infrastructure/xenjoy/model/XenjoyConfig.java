package com.mrk.yudong.admin.infrastructure.xenjoy.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 绝影会员卡配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
@Getter
@Setter
@Accessors(chain = true)
public class XenjoyConfig extends Model<XenjoyConfig> {

    private static final long serialVersionUID = 1L;

    /**
     * 配置ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 金额
     */
    private BigDecimal price;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 类型：1-月，2-季，3-年，4-日
     */
    private Integer skuType;

    /**
     * 状态：0-未发放，1-已发放
     */
    private Integer status;

    /**
     * 发放时间
     */
    private LocalDateTime assignedTime;

    /**
     * 凭证文件
     */
    private String fileUrl;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除：0-否，1-是
     */
    private Integer isDelete;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
