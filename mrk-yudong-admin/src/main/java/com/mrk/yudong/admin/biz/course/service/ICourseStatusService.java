package com.mrk.yudong.admin.biz.course.service;

import com.mrk.yudong.admin.infrastructure.course.model.CourseStatus;
import com.mrk.yudong.core.service.BaseService;

/**
 * <p>
 * 课程状态表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
public interface ICourseStatusService extends BaseService<CourseStatus> {

    /**
     * 获取最新课程状态信息
     *
     * @param courseId
     * @return
     */
    CourseStatus getTop(Long courseId);

}
