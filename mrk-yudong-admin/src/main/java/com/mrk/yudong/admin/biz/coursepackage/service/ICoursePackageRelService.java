package com.mrk.yudong.admin.biz.coursepackage.service;/**
 * <AUTHOR>
 * @create 2023−04-17 18:29
 */

import com.mrk.yudong.admin.infrastructure.coursepackage.model.CoursePackageRel;
import com.mrk.yudong.core.service.BaseService;

import java.util.List;

/**
 * @description:
 * @author: ljx
 * @create: 2023/4/17 18:29
 * @Version 1.0
 **/
public interface ICoursePackageRelService extends BaseService<CoursePackageRel> {
    List<CoursePackageRel> findByCoursePackageId(Long coursePackageId);

    Boolean deletePackageCourses(Long packageId, List<Long> courseIds);
}
