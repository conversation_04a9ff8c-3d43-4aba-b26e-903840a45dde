package com.mrk.yudong.admin.biz.coursepackage.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CourseThemeDetail;
import com.mrk.yudong.admin.infrastructure.coursepackage.mapper.CourseThemeDetailMapper;
import com.mrk.yudong.admin.biz.coursepackage.service.ICourseThemeDetailService;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 课程 - 课程主题 中间关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
@Service
public class CourseThemeDetailServiceImpl extends BaseServiceImpl<CourseThemeDetailMapper, CourseThemeDetail> implements ICourseThemeDetailService {

    @Override
    public List<JSONObject> queryCourse(Long themeId) {
        return baseMapper.queryCourse(themeId);
    }

}
