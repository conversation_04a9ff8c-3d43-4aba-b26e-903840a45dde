package com.mrk.yudong.admin.infrastructure.tag.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class UserTagComputeConfig {

    private Long id;

    private Long tagId;

    private String tagCode;

    private String computeLogic;

    private Integer enable;

    private LocalDateTime createTime;

    private Long createId;

    private LocalDateTime updateTime;

    private Long updateId;

}
