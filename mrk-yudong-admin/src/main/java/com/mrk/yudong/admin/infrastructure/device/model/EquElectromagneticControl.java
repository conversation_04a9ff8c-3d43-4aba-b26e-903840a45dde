package com.mrk.yudong.admin.infrastructure.device.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 电磁控与设备类型关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EquElectromagneticControl extends Model<EquElectromagneticControl> {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 电磁档位
     */
    private Integer electromagneticControl;

    /**
     * 实际电磁档位
     */
    private Integer actualElectromagneticControl;

    /**
     * 关联类型id
     */
    private Long typeId;

    /**
     * 电磁控类型 1阻力， 2坡度
     */
    private Integer type;

    /**
     * 删除状态0：未删除，1：已删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    private Long updateBy;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
