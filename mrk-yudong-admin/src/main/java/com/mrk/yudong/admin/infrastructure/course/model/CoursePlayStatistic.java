package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 课程播放数据统计
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-08
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CoursePlayStatistic extends Model<CoursePlayStatistic> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    @JsonIgnore
    private Long id;

    /**
     * 统计日期
     */
    @JsonIgnore
    private LocalDate statisticDate;

    /**
     * 跑步机数量
     */
    private Integer runNum;

    /**
     * 单车数量
     */
    private Integer bicycleNum;

    /**
     * 划船机数量
     */
    private Integer shipNum;

    /**
     * 椭圆机数量
     */
    private Integer roundNum;

    /**
     * 筋膜枪数量
     */
    private Integer gunNum;

    /**
     * 跳绳数量
     */
    private Integer jumpNum;

    /**
     * 总数量
     */
    private Integer sumNum;

    /**
     * 小件数量
     */
    @TableField(exist = false)
    private Integer otherNum;

    /**
     * 转化率
     */
    private Double prop;

    /**
     * 创建时间
     */
    @JsonIgnore
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
