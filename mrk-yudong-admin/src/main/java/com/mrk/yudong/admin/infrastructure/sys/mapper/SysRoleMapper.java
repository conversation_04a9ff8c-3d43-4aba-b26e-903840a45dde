package com.mrk.yudong.admin.infrastructure.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.infrastructure.sys.model.SysRole;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 后台角色表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-17
 */
public interface SysRoleMapper extends BaseMapper<SysRole> {

    IPage<SysRole> query(Page<SysRole> page, @Param("param") Map<String, Object> param);

}
