package com.mrk.yudong.admin.infrastructure.app.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 常见问题
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AppCommonProblem extends Model<AppCommonProblem> {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 设备id，以,分割
     */
    @NotBlank(message = "请选择设备")
    private String equipTypeId;

    /**
     * 标题
     */
    @NotBlank(message = "请填写标题")
    @Size(min = 2,max = 50,message="标题请输入2-50个字符")
    private String title;

    /**
     * 内容
     */
    @NotBlank(message = "请填写内容")
    private String content;

    /**
     * 排序
     */
    @NotNull(message = "请填写排序")
    private Integer sort;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Long updateId;
    /**
     * 修改人
     */
    @TableField(exist = false)
    private String updateByName;

    /**
     * 设备分类名称
     */
    @TableField(exist = false)
    private String equipTypeName;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 预发环境
     */
    private Integer isTra;
    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
