package com.mrk.yudong.admin.feign;

import com.mrk.yudong.core.model.R;
import com.mrk.yudong.share.dto.equip.BackstageEquipmentType;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2021−04-20 4:54 下午
 */
@FeignClient(name = "yudong-equipment")
public interface EquipmentFeign {

    /**
     * 通过id获取信息（提供给外部服务）
     *
     * @return R
     */
    @PostMapping("/equipment/equipmentTypeController/getTypeById")
    R getTypeById(@RequestBody List<String> id);

    /**
     * 获取所有的一级分类信息
     *
     * @return
     */
    @GetMapping("/equipment/equipmentTypeController/getOneTypeId")
    R getTopEquipment();

    /**
     * 获取小件ID
     *
     * @return
     */
    @GetMapping("/equipDictController/smallIdList")
    R smallIdList();

    @PostMapping("/equipment/equipmentTypeController/equipmentType")
    R saveEquipment(@RequestBody BackstageEquipmentType equipmentType);

    @PutMapping("/equipment/equipmentTypeController/equipmentType")
    R updateEquipment(@RequestBody @Valid BackstageEquipmentType equipmentType);
}
