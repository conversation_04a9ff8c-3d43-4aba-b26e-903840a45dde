package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 环节热词表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CatalogueHotword extends Model<CatalogueHotword> {

    private static final long serialVersionUID = 1L;

    /**
     * 开放主键
     */
    private Long id;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 环节ID
     */
    private Long catalogueId;

    /**
     * 热词ID
     */
    private Long hotwordId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public CatalogueHotword(Long courseId, Long catalogueId, Long hotwordId) {
        this.courseId = courseId;
        this.catalogueId = catalogueId;
        this.hotwordId = hotwordId;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
