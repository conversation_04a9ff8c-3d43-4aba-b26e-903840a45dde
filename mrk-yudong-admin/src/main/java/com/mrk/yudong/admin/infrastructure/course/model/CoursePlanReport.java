package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <p>
 * 计划训练报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CoursePlanReport extends Model<CoursePlanReport> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户计划ID
     */
    private Long planUserId;

    /**
     * 计划ID
     */
    private Long planId;

    /**
     * 加入计划时间
     */
    private LocalDateTime beginTime;

    /**
     * 计划训练时长：秒
     */
    private Long time;

    /**
     * 计划训练距离：千米
     */
    private BigDecimal distance;

    /**
     * 地址ID
     */
    private Long addressId;

    /**
     * 圈数
     */
    private BigDecimal turnNum;

    /**
     * 计划训练消耗卡路里
     */
    private BigDecimal kcal;

    /**
     * 食物ID
     */
    private Long foodId;

    /**
     * 数量
     */
    private BigDecimal num;

    /**
     * 比例
     */
    private BigDecimal prop;

    /**
     * 经常训练开始时间
     */
    private LocalTime trainBeginTime;

    /**
     * 经常训练结束时间
     */
    private LocalTime trainEndTime;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
