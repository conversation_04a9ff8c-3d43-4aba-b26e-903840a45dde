package com.mrk.yudong.admin.infrastructure.device.gateway.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.merach.sun.common.layer.web.PageDTO;
import com.merach.sun.device.api.FirmwareApi;
import com.merach.sun.device.api.ProductModelApi;
import com.merach.sun.device.dto.cmd.firmware.CreateProductModelFirmwareCmd;
import com.merach.sun.device.dto.cmd.firmware.DeleteProductModelFirmwareCmd;
import com.merach.sun.device.dto.cmd.firmware.UpdateProductModelFirmwareCmd;
import com.merach.sun.device.dto.qry.ProductModelFirmwareQry;
import com.merach.sun.device.dto.qry.ProductModelQry;
import com.merach.sun.device.dto.resp.firmware.ProductModelFirmwareDetailDTO;
import com.merach.sun.device.dto.resp.model.ProductModelDetailDTO;
import com.merach.sun.device.enums.FirmwareUpdateEnum;
import com.merach.sun.device.enums.OtaProtocolEnum;
import com.merach.sun.device.enums.ProductEnum;
import com.mrk.yudong.admin.api.device.dto.cmd.firmware.CreateModelFirmwareCmd;
import com.mrk.yudong.admin.api.device.dto.cmd.firmware.UpdateModelFirmwareCmd;
import com.mrk.yudong.admin.api.device.dto.resp.firmware.BackstageModelFirmwareDetailDTO;
import com.mrk.yudong.admin.api.device.dto.resp.firmware.HistoryModelFirmwareDetailDTO;
import com.mrk.yudong.admin.api.device.dto.resp.firmware.PageModelFirmwareDTO;
import com.mrk.yudong.admin.infrastructure.device.gateway.ProductModelFirmwareGateway;
import com.mrk.yudong.admin.biz.sys.service.ISysUserService;
import com.mrk.yudong.core.utils.SessionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class ProductModelFirmwareQryGatewayImpl implements ProductModelFirmwareGateway {
    private final FirmwareApi firmwareApi;

    private final ISysUserService systemUserService;

    private final ProductModelApi productModelApi;

    @Override
    public BackstageModelFirmwareDetailDTO createProductModelFirmware(CreateModelFirmwareCmd cmd) {
        ProductModelFirmwareDetailDTO productModelFirmware = firmwareApi.createProductModelFirmware(BeanUtil.copyProperties(cmd, CreateProductModelFirmwareCmd.class));
        BackstageModelFirmwareDetailDTO backstageModelFirmwareDetailDTO = BeanUtil.copyProperties(productModelFirmware, BackstageModelFirmwareDetailDTO.class);
        backstageModelFirmwareDetailDTO.setCreateId(SessionUtil.getId());
        return backstageModelFirmwareDetailDTO;
    }

    @Override
    public BackstageModelFirmwareDetailDTO updateProductModelFirmware(UpdateModelFirmwareCmd cmd) {
        ProductModelFirmwareDetailDTO productModelFirmware = firmwareApi.updateProductModelFirmware(BeanUtil.copyProperties(cmd, UpdateProductModelFirmwareCmd.class));
        BackstageModelFirmwareDetailDTO backstageModelFirmwareDetailDTO = BeanUtil.copyProperties(productModelFirmware, BackstageModelFirmwareDetailDTO.class);
        backstageModelFirmwareDetailDTO.setUpdateId(SessionUtil.getId());
        return backstageModelFirmwareDetailDTO;
    }

    @Override
    public Boolean deleteProductModelFirmware(Long id) {
        if (null == id) {
            return Boolean.FALSE;
        }
        DeleteProductModelFirmwareCmd deleteProductModelFirmwareCmd = new DeleteProductModelFirmwareCmd();
        deleteProductModelFirmwareCmd.setId(id);
        deleteProductModelFirmwareCmd.setCurrentUserId(SessionUtil.getId());
        return firmwareApi.deleteProductModelFirmware(deleteProductModelFirmwareCmd);
    }

    @Override
    public BackstageModelFirmwareDetailDTO getProductModelFirmware(ProductModelFirmwareQry productModelFirmwareQry) {
        ProductModelFirmwareDetailDTO productModelFirmware = firmwareApi.getProductModelFirmware(productModelFirmwareQry);
        return BeanUtil.copyProperties(productModelFirmware, BackstageModelFirmwareDetailDTO.class);
    }

    @Override
    public Page<HistoryModelFirmwareDetailDTO> pageHistoryModelFirmwares(ProductModelFirmwareQry productModelFirmwareQry) {
        PageDTO<ProductModelFirmwareDetailDTO> productModelFirmwareDetailDTOS = firmwareApi.pageProductModelFirmwares(productModelFirmwareQry);
        Page<HistoryModelFirmwareDetailDTO> resultPage = new Page<>(productModelFirmwareQry.getCurrent(), productModelFirmwareQry.getSize());
        if (CollUtil.isEmpty(productModelFirmwareDetailDTOS.getRecords())) {
            return resultPage;
        }

        List<ProductModelFirmwareDetailDTO> productModelFirmwares = productModelFirmwareDetailDTOS.getRecords();
        Map<Long, String> sysUserNames = systemUserService.listUserNameByIds(productModelFirmwares.stream().map(ProductModelFirmwareDetailDTO::getUpdateId).collect(Collectors.toSet()));
        resultPage.setTotal(productModelFirmwareDetailDTOS.getTotal());
        resultPage.setRecords(productModelFirmwares.stream().map(v -> {
            HistoryModelFirmwareDetailDTO historyModelFirmware = BeanUtil.copyProperties(v, HistoryModelFirmwareDetailDTO.class);
            String userName = MapUtil.getStr(sysUserNames, v.getUpdateId());
            if (StrUtil.isNotBlank(userName)) {
                historyModelFirmware.setOperationName(userName);
            }
            return historyModelFirmware;
        }).collect(Collectors.toList()));
        return resultPage;
    }

    @Override
    public Page<PageModelFirmwareDTO> pageProductModelFirmwares(ProductModelFirmwareQry productModelFirmwareQry) {
        Page<PageModelFirmwareDTO> resultPage = new Page<>();
        PageDTO<ProductModelFirmwareDetailDTO> productModelFirmwareDetailDTOPageDTO = firmwareApi.pageProductModelFirmwares(BeanUtil.copyProperties(productModelFirmwareQry, ProductModelFirmwareQry.class));
        if (null == productModelFirmwareDetailDTOPageDTO.getRecords()) {
            return resultPage;
        }

        resultPage.setRecords(convertFirmwarePage(productModelFirmwareDetailDTOPageDTO));
        resultPage.setTotal(productModelFirmwareDetailDTOPageDTO.getTotal());
        return resultPage;
    }

    private List<PageModelFirmwareDTO> convertFirmwarePage(PageDTO<ProductModelFirmwareDetailDTO> productModelFirmwareDetailDTOPageDTO) {
        Map<Long, String> sysUserNames = systemUserService.listUserNameByIds(productModelFirmwareDetailDTOPageDTO.getRecords().stream().map(ProductModelFirmwareDetailDTO::getUpdateId).collect(Collectors.toSet()));
        Map<Integer, String> otaProtocols = OtaProtocolEnum.getOtaProtocol();
        Map<Long, ProductModelDetailDTO> productModels = getProductModels(productModelFirmwareDetailDTOPageDTO);
        Map<Integer, String> firmwareUpdates = FirmwareUpdateEnum.getFirmwareUpdateEnum();
        Map<Long, String> product = ProductEnum.getProduct();
        return productModelFirmwareDetailDTOPageDTO.getRecords().stream().map(v -> {
            PageModelFirmwareDTO pageModelFirmwareDTO = BeanUtil.copyProperties(v, PageModelFirmwareDTO.class);
            pageModelFirmwareDTO.setUploadTime(v.getCreateTime());
            String sysUserName = MapUtil.getStr(sysUserNames, v.getUpdateId());
            if (StrUtil.isNotBlank(sysUserName)) {
                pageModelFirmwareDTO.setOperationName(sysUserName);
                pageModelFirmwareDTO.setOperationTime(v.getUpdateTime());
            }

            ProductModelDetailDTO modelDetailDTO = MapUtil.get(productModels, v.getProductModelId(), ProductModelDetailDTO.class);
            if (!Objects.isNull(modelDetailDTO)) {
                pageModelFirmwareDTO.setProudctModelId(modelDetailDTO.getId());
                if (otaProtocols.containsKey(modelDetailDTO.getOtaProtocol())) {
                    pageModelFirmwareDTO.setOtaProtocol(otaProtocols.get(modelDetailDTO.getOtaProtocol()));
                }
                if (product.containsKey(modelDetailDTO.getProductId())) {
                    pageModelFirmwareDTO.setProductName(product.get(modelDetailDTO.getProductId()));
                }
                pageModelFirmwareDTO.setProductModelName(modelDetailDTO.getModelName());
            }

            if (firmwareUpdates.containsKey(v.getUpdateType())) {
                pageModelFirmwareDTO.setUpdateTypeName(firmwareUpdates.get(v.getUpdateType()));
            }

            return pageModelFirmwareDTO;
        }).collect(Collectors.toList());
    }


    private Map<Long, ProductModelDetailDTO> getProductModels(PageDTO<ProductModelFirmwareDetailDTO> productModelFirmwareDetailDTOPageDTO) {
        ProductModelQry productModelQry = new ProductModelQry();
        productModelQry.setProductModelIds(productModelFirmwareDetailDTOPageDTO.getRecords().stream().map(ProductModelFirmwareDetailDTO::getProductModelId).collect(Collectors.toList()));
        List<ProductModelDetailDTO> productModelDetailDTOs = productModelApi.listProductModels(productModelQry);

        if (CollUtil.isEmpty(productModelDetailDTOs)) {
            return null;
        }
        return productModelDetailDTOs.stream().collect(Collectors.toMap(ProductModelDetailDTO::getId, v -> v));
    }

}