package com.mrk.yudong.admin.infrastructure.coursepackage.dao;

import com.mrk.yudong.admin.infrastructure.coursepackage.model.CourseThemeDetail;

import java.util.List;

public interface CourseThemeDetailDao {
    Boolean batchCreateCourseThemeDetail(List<CourseThemeDetail> courseThemeDetails);

    Boolean deleteByThemeId(Long themeId);

    List<CourseThemeDetail> findByThemeIds(List<Long> themeIds);
}
