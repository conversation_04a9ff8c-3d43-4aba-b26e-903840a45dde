package com.mrk.yudong.admin.infrastructure.tag.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.infrastructure.tag.model.UserTag;
import com.mrk.yudong.admin.infrastructure.tag.model.UserTagComputeConfig;
import com.mrk.yudong.admin.infrastructure.tag.model.UserTagValue;

import java.util.Collection;
import java.util.List;
import java.util.zip.ZipFile;

public interface UserTagDao {

    // 添加用户标签配置
    void addUserTagConfig(UserTagComputeConfig config);
    // 添加用户标签
    void addUserTag(UserTag userTag);
    // 添加用户标签纸集合
    void addUserTagValues(Collection<UserTagValue> userTagValues);
    // 查询用户标签
    UserTag getUserTag(String code);

    UserTag getUserTag(Long tagId);


    Page<UserTag> pageUserTags(Integer current,Integer size,String code,String name);

    List<UserTagComputeConfig> getComputeConfigsByTagIds(List<Long> tagIds);

    List<UserTagValue> getTagValuesByTagIds(List<Long> tagIds);
}
