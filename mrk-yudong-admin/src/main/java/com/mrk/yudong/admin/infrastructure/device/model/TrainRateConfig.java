package com.mrk.yudong.admin.infrastructure.device.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 训练心率配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TrainRateConfig extends Model<TrainRateConfig> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 显示名称
     */
    @NotBlank(message = "显示名称不能为空")
    @Length(max = 16, message = "显示名称长度不能超过16位")
    private String name;

    /**
     * 颜色
     */
    @NotBlank(message = "颜色不能为空")
    @Length(max = 16, message = "颜色配置长度不能超过16位")
    private String color;

    /**
     * 最小比例
     */
    @NotNull(message = "最小比例不能为空")
    @Digits(integer = 3, fraction = 0, message = "比例必须为整数位")
    @DecimalMin(value = "1", message = "比例必须大于0")
    @DecimalMax(value = "99", message = "最小比例必须小于100")
    private BigDecimal minProp;

    /**
     * 最大比例
     */
    @NotNull(message = "最大比例不能为空")
    @Digits(integer = 3, fraction = 0, message = "比例必须为整数位")
    @DecimalMin(value = "1", message = "比例必须大于0")
    @DecimalMax(value = "100", message = "最大比例必须不能超过100")
    private BigDecimal maxProp;

    /**
     * 排序号
     */
    @NotNull(message = "排序号不能为空")
    @Min(value = 0, message = "排序号不能小于0")
    private Integer sort;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
