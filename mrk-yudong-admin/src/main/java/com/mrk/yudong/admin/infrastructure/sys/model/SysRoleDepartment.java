package com.mrk.yudong.admin.infrastructure.sys.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 角色和部门关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysRoleDepartment extends Model<SysRoleDepartment> {

    private static final long serialVersionUID = 1L;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 部门id
     */
    private Long departmentId;

    /**
     * 角色id
     */
    private Long roleId;


    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
