package com.mrk.yudong.admin.infrastructure.link.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 链路表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Link extends Model<Link> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 链路编码
     */
    private String code;

    /**
     * 链路名称
     */
    private String name;

    /**
     * 状态：1-未开始，2-生效中，3-已结束
     */
    private Integer status;

    /**
     * 触达人群
     */
    private String crowd;

    /**
     * 触达终端
     */
    private Integer terminal;

    /**
     * 触达设备类型ID
     */
    private Long productId;

    /**
     * 起始版本
     */
    private Integer beginVersion;

    /**
     * 终止版本
     */
    private Integer endVersion;

    /**
     * 开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
