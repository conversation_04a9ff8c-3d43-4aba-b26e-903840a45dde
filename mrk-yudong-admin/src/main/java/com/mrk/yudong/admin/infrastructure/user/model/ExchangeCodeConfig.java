package com.mrk.yudong.admin.infrastructure.user.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 会员兑换码配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ExchangeCodeConfig extends Model<ExchangeCodeConfig> {


    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 会员分类：1-麦瑞克，2-绝影
     */
    private Integer vipClassify;

    @TableField(exist = false)
    private String vipClassifyName;

    /**
     * VIP类型：10-VIP，20-SVIP，30-XVIP
     */
    private Integer vipType;

    @TableField(exist = false)
    private String vipTypeName;

    /**
     * 用户名称
     */
    @NotBlank(message = "用户名称不能为空")
    @Length(max = 64, message = "用户名称长度不能超过64位长度")
    private String username;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    private String mobile;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    @Length(min = 11, max = 64, message = "订单号长度必须大于10位且不能超过64位")
    private String orderNo;

    /**
     * 店铺名称
     */
    private String shop;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空")
    @Length(max = 200, message = "备注长度不能超过200位长度")
    private String remark;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量必须大于等于1")
    private Integer num;

    /**
     * 类型：1-月，2-季，3-年，4-日
     */
    @NotNull(message = "请选择类型")
    private Integer type;

    /**
     * 类型文字显示
     */
    @TableField(exist = false)
    private String typeDesc;

    /**
     * 截图凭证图
     */
    @TableField(exist = false)
    @Size(max = 6, message = "最多上次6张截图凭证")
    private List<String> images;

    /**
     * 兑换码
     */
    private String exchangeCode;

    /**
     * 兑换时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime exchangeTime;

    /**
     * 兑换状态：0-未兑换，1-已兑换
     */
    private Integer exchangeStatus;

    /**
     * 兑换状态文字显示
     */
    @TableField(exist = false)
    private String exchangeStatusDesc;

    /**
     * 是否为预发数据：0-否，1-是
     */
    private Integer isTra;

    /**
     * 有效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime effectiveTime;

    /**
     * 状态：0-作废，1-正常
     */
    private Integer status;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建人名称
     */
    @TableField(exist = false)
    private String createName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
