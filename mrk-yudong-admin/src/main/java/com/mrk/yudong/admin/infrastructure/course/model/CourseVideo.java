package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 课程视频表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CourseVideo extends Model<CourseVideo> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 视频ID
     */
    private String videoId;

    /**
     * 视频流码率，单位：Kbps
     */
    private String bitrate;

    /**
     * 视频流清晰度定义：FD-流畅，LD-标清，SD-高清，HD-超清，OD-原画，2K-2K，4K-4K，AUTO-自适应码流
     */
    private String definition;

    /**
     * 视频流长度，单位：秒
     */
    private Float duration;

    /**
     * 视频流是否为加密流：0-否，1-是
     */
    private Integer encrypt;

    /**
     * 作业错误码，视频流转码出错时，会有该字段
     */
    private String errorCode;

    /**
     * 作业错误信息，视频流转码出错时，会有该字段
     */
    private String errorMessage;

    /**
     * 视频流的播放地址。不带鉴权的auth_key，如果开启了URL鉴权，则需要自己生成auth_key才能访问
     */
    private String fileUrl;

    /**
     * 视频流格式
     */
    private String format;

    /**
     * 视频流帧率，每秒多少帧
     */
    private String fps;

    /**
     * 视频流高度，单位：px
     */
    private Long height;

    /**
     * 视频流宽度，单位：px
     */
    private Long width;

    /**
     * 视频流大小，单位：Byte
     */
    private Long size;

    /**
     * 转码作业ID
     */
    private String jobId;

    /**
     * 视频类型：1-课程视频，2-实景视频
     */
    private Integer type;

    /**
     * 是否转码成功：0-否，1-是
     */
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
