package com.mrk.yudong.admin.biz.course.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseMetaMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseMeta;
import com.mrk.yudong.admin.biz.course.service.ICourseMetaService;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.model.SortModel;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 课程字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-18
 */
@Service
public class CourseMetaServiceImpl extends BaseServiceImpl<CourseMetaMapper, CourseMeta> implements ICourseMetaService {

    @Override
    public boolean isExist(String code, String val) {
        BaseQuery<CourseMeta> baseQuery = new BaseQuery<>();
        baseQuery.eq("code", code).eq("val", val);
        return this.isExist(baseQuery);
    }

    @Override
    public List<Dict> option(String code) {
        SortModel sortModel = new SortModel(ConditionEnum.ASC, "sort");
        List<CourseMeta> list = this.list("code", ConditionEnum.EQ, code, sortModel, "name", "val");
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>(0);
        }

        return list.stream().map(courseMeta -> {
            Dict dict = new Dict(2);
            dict.put("name", courseMeta.getName());
            dict.put("value", courseMeta.getVal());
            return dict;
        }).collect(Collectors.toList());
    }

    @Override
    public int count(String code, Set<String> cruxSet) {
        BaseQuery<CourseMeta> baseQuery = new BaseQuery<>();
        baseQuery.eq("code", code).in("val", cruxSet);
        return this.count(baseQuery);
    }

}
