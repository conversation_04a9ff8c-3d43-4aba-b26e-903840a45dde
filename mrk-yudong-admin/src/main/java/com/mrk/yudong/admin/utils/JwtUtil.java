package com.mrk.yudong.admin.utils;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.admin.constant.AdminConstant;
import com.mrk.yudong.share.constant.BaseConstant;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class JwtUtil {

    /**
     * 加密私钥
     */
    private static final String PRIVATE_KEY = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAI6W+V/UoJIR0F9hEqRoe/rUc7lb1n+jsa/WtTaIxmliFdFxknhyPsFGt1EOzs50jhHe1OtS0tXKUuuBvU4M/Qs4+qbI2ZiHdVUxvNMGhSLcwNCRnjCxiUh4WHIPZpyUXTwQpmamxia7gSo2fyzUZ1+h83FbQY2sUEYW8ipLen9XAgMBAAECgYAaiqG6o9KLvgMibehRcB170CYGX8Dqm2bMy1OJ5iXAogBbrx9LikCCK46d6Pq31VxImUja0Nzr7LBIpCq/p41GSzUrelivr6jqT0VORtbSB1j4s7TuumdGaeg7u6gSPo2DMVNYtioT/0Zff4vwn5l3gOSgXM2yrvrlnoytmffn4QJBAPR3v+fi7DdJ5348X2NUoYOZL07EjwunPYUA/VRBKIaBpfiwIgkfCX21DHU0DO/7ChuXwWm1fmze1EgU+6KhbjECQQCVUOviKAJIZHefkuLmC/OcHnVTGbYkwpxP5PJUnbjqNXNog2oges+ROisE+6Drc40GAljt6lYfPU3vTRKmzTwHAkBmDiO4CzxvbFPOmCd+EROIG4frSxUoWT3Oa3ZWGQlw9WizI9xhkGpm5xD3UK5h9JvonvaoSZMoofFe0humbdvRAkBRbsaWmrdT/+b1kkhIqi/77uiRn64ksjZJpFU9LJ+Sq//6+eDBQfk9/PlFSG5kuqoootMZPfomI2sDnYcUjU21AkAtw2nPTNjQNLiZNNg/G5OoFsI2e7oZMXFzfEQFNFMbDMRj8lQ/1GnBfIXusJZiNTDYplR+mDrWqKjbrqqy1838";

    /**
     * 加密公钥
     */
    private static final String PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCOlvlf1KCSEdBfYRKkaHv61HO5W9Z/o7Gv1rU2iMZpYhXRcZJ4cj7BRrdRDs7OdI4R3tTrUtLVylLrgb1ODP0LOPqmyNmYh3VVMbzTBoUi3MDQkZ4wsYlIeFhyD2aclF08EKZmpsYmu4EqNn8s1GdfofNxW0GNrFBGFvIqS3p/VwIDAQAB";

    private static final RSA rsa = new RSA(PRIVATE_KEY, PUBLIC_KEY);

    private static final String ID_KEY = "id";

    private static final String TIME_KEY = "data";

    private static final DateTimeFormatter DATE_TIME = DateTimeFormatter.ofPattern(BaseConstant.DATE_TIME_FORMAT);

    private JwtUtil() {
    }

    public static String sign(String id) {
        if (StrUtil.isBlank(id)) {
            return null;
        }

        Dict dict = new Dict(2);
        dict.put(ID_KEY, id);

        LocalDateTime time = LocalDateTime.now().plusHours(AdminConstant.TOKEN_TIME);
        dict.put(TIME_KEY, time.format(DATE_TIME));

        return rsa.encryptBcd(JSON.toJSONString(dict), KeyType.PublicKey);
    }

    public static String getId(String sign) {
        if (StrUtil.isBlank(sign)) {
            return null;
        }

        try {
            String s = rsa.decryptStrFromBcd(sign, KeyType.PrivateKey);
            JSONObject dict = JSONObject.parseObject(s);
            return dict.getString(ID_KEY);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static boolean valid(String sign) {
        if (StrUtil.isBlank(sign)) {
            return false;
        }

        try {
            String s = rsa.decryptStrFromBcd(sign, KeyType.PrivateKey);
            JSONObject dict = JSONObject.parseObject(s);
            LocalDateTime time = LocalDateTime.parse(dict.getString(TIME_KEY), DATE_TIME);
            return LocalDateTime.now().isBefore(time);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

}
