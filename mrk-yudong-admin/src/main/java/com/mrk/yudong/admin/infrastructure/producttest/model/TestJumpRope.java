package com.mrk.yudong.admin.infrastructure.producttest.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 产测跳绳
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TestJumpRope extends Model<TestJumpRope> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelIgnore
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * CMEI
     */
    @ExcelProperty("CMEI")
    private String cmei;

    /**
     * MAC
     */
    @ExcelProperty("MAC")
    private String mac;

    /**
     * SN
     */
    @ExcelProperty("SN")
    private String sn;

    /**
     * 是否删除
     */
    @ExcelIgnore
    private Integer isDelete;

    /**
     * 创建人
     */
    @ExcelIgnore
    private Long createId;

    /**
     * 创建时间
     */
    @ExcelProperty("生成时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @ExcelIgnore
    private Long updateId;

    /**
     * 修改时间
     */
    @ExcelIgnore
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
