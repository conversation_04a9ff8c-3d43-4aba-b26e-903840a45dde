package com.mrk.yudong.admin.biz.course.service.impl;

import com.mrk.yudong.admin.infrastructure.course.mapper.LogCourseDetailMapper;
import com.mrk.yudong.admin.infrastructure.course.model.LogCourseDetail;
import com.mrk.yudong.admin.biz.course.service.ILogCourseDetailService;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 课程详情记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-05
 */
@Service
public class LogCourseDetailServiceImpl extends BaseServiceImpl<LogCourseDetailMapper, LogCourseDetail> implements ILogCourseDetailService {

    /**
     * 获取去重用户数量
     *
     * @param courseId
     * @return
     */
    @Override
    public int getGroupUserNum(Long courseId) {
        return baseMapper.getGroupUserNum(courseId);
    }

}
