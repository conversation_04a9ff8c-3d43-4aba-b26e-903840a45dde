package com.mrk.yudong.admin.infrastructure.motion.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
public class MediaVideo extends Model<MediaVideo> {
    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 资源名称
     */
    private String name;
    
    /**
     * 封面
     */
    private String cover;

    /**
     * 视频类型：视频类型：1-课程，2-实景视频，3-动作
     */
    private Integer type;

    /**
     * 视频子类型：课程：；实景视频：；动作：31-跟练视频，32-精讲视频
     */
    private Integer subType;

    /**
     * 毫秒单位
     */
    private Integer duration;

    private String url;

    private String videoId;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    private Integer isDelete;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
