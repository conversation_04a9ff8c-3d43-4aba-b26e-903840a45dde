package com.mrk.yudong.admin.biz.coach.service.impl;

import com.mrk.yudong.admin.infrastructure.coach.mapper.CoachInteractMapper;
import com.mrk.yudong.admin.infrastructure.coach.model.CoachInteract;
import com.mrk.yudong.admin.biz.coach.service.ICoachInteractService;
import com.mrk.yudong.admin.infrastructure.course.model.InteractInfo;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 教练互动词表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
@Service
public class CoachInteractServiceImpl extends BaseServiceImpl<CoachInteractMapper, CoachInteract> implements ICoachInteractService {

    /**
     * 根据教练ID获取互动词
     *
     * @param coachId
     * @return
     */
    @Override
    public List<InteractInfo> getListByCoachId(Long coachId) {
        return baseMapper.getListByCoachId(coachId);
    }

}
