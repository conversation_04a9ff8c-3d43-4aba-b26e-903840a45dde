package com.mrk.yudong.admin.task.linestatus;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.mrk.yudong.admin.biz.linestatusrecord.service.LineStatusRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Slf4j
@RequiredArgsConstructor
@Component
public class CourseInitLineStatusJob extends JavaProcessor {

 private final LineStatusRecordService lineStatusRecordService;
    @Override
    public ProcessResult process(JobContext context) {
        try {
            log.info("CourseLineStatusJob start");
            lineStatusRecordService.initCourseLineStatusRecord();
            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("CourseLineStatusJob error: ", e);
            return new ProcessResult(false);
        }
    }

}
