package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.infrastructure.course.model.CourseKeywords;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 关键词信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-09
 */
public interface CourseKeywordsMapper extends BaseMapper<CourseKeywords> {

    IPage<CourseKeywords> query(Page<CourseKeywords> page, @Param("param") Map<String, Object> param);

}
