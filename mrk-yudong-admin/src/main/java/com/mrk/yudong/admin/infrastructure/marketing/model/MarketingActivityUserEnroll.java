package com.mrk.yudong.admin.infrastructure.marketing.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 活动报名表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Getter
@Setter
@Accessors(chain = true)
public class MarketingActivityUserEnroll extends Model<MarketingActivityUserEnroll> {

    private static final long serialVersionUID = 1L;

    /**
     * 报名ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 活动类型：1-里程挑战，2-打卡挑战，3-分享活动，4-返现活动
     */
    private Integer type;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 活动分组ID
     */
    private Long groupId;

    /**
     * 设备ID
     */
    private Long productId;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 可抽奖次数
     */
    private Integer lotteryNum;

    /**
     * 已抽奖次数
     */
    private Integer lotteryDrawnNum;

    /**
     * 地址信息
     */
    private String address;

    /**
     * 是否完成活动：0-否，1-是
     */
    private Integer isFinish;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 是否发放奖品：0-否，1-是
     */
    private Integer isGivePrize;

    /**
     * 活动报名订单ID
     */
    private Long enrollOrderId;

    /**
     * 是否删除：0-否，1-是
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
