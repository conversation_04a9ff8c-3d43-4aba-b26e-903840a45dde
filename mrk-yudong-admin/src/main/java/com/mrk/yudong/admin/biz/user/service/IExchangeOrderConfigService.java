package com.mrk.yudong.admin.biz.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.infrastructure.user.model.ExchangeOrderConfig;
import com.mrk.yudong.admin.api.user.vo.ExchangeCodeConfigQueryVO;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.service.BaseService;

/**
 * <p>
 * 兑换订单配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-30
 */
public interface IExchangeOrderConfigService extends BaseService<ExchangeOrderConfig> {

    /**
     * 查询配置列表
     *
     * @param page
     * @param exchangeCodeConfigQueryVO
     * @return
     */
    IPage<ExchangeOrderConfig> query(Page<ExchangeOrderConfig> page, ExchangeCodeConfigQueryVO exchangeCodeConfigQueryVO);

    /**
     * 新增订单配置
     *
     * @param exchangeOrderConfig
     * @return
     */
    R saveExchangeOrderConfig(ExchangeOrderConfig exchangeOrderConfig);

    /**
     * 修改订单配置
     *
     * @param exchangeOrderConfig
     * @return
     */
    R updateExchangeOrderConfig(ExchangeOrderConfig exchangeOrderConfig);

    /**
     * 课程详情
     *
     * @param id
     * @return
     */
    R detail(Long id);

}
