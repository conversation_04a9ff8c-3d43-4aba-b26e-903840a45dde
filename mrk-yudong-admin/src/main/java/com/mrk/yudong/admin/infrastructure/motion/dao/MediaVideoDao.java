package com.mrk.yudong.admin.infrastructure.motion.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.api.motion.dto.qry.MediaPageQry;
import com.mrk.yudong.admin.infrastructure.motion.model.MediaVideo;

import java.util.List;
import java.util.Set;

public interface MediaVideoDao {
    MediaVideo createVideo(MediaVideo creation);

    MediaVideo updateVideo(MediaVideo updated);

    Boolean deleteVideo(Long id);

    MediaVideo getById(Long id);

    MediaVideo getByVideoId(String videoId);

    IPage<MediaVideo> pageVideos(MediaPageQry pageQry);

    List<MediaVideo> findVideosByIds(Set<Long> ids);
}
