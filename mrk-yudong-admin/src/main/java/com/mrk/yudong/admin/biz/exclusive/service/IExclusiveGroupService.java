package com.mrk.yudong.admin.biz.exclusive.service;

import com.mrk.yudong.admin.api.exclusive.dto.ExclusiveGroupDataDTO;
import com.mrk.yudong.admin.api.exclusive.dto.ExclusiveGroupOptionDTO;
import com.mrk.yudong.admin.api.exclusive.query.ExclusiveGroupQuery;
import com.mrk.yudong.admin.api.exclusive.vo.ExclusiveGroupVo;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.model.ResDTO;

public interface IExclusiveGroupService {

    PageDTO<ExclusiveGroupDataDTO> page(ExclusiveGroupQuery query);

    ExclusiveGroupOptionDTO option();

    ResDTO<Boolean> save(ExclusiveGroupVo exclusiveGroupVo);

    ResDTO<Boolean> update(Long groupId, ExclusiveGroupVo exclusiveGroupVo);

    ResDTO<Boolean> remove(Long groupId);

}
