package com.mrk.yudong.admin.infrastructure.user.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户登录日志
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties({"id", "userId", "terminal"})
public class LogUserLogin extends Model<LogUserLogin> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 登录IP
     */
    private String loginIp;

    /**
     * 登录省份
     */
    private String province;

    /**
     * 登录城市
     */
    private String city;

    /**
     * 登录区县
     */
    private String region;

    /**
     * 登录终端：1-Android，2-IOS
     */
    private Integer terminal;

    /**
     * 登录终端文字显示
     */
    @TableField(exist = false)
    private String terminalDesc;

    /**
     * 地址
     */
    private String address;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备唯一序列号
     */
    private String deviceNo;

    /**
     * 设备系统版本
     */
    private String deviceOs;

    /**
     * APP版本
     */
    private String appVersion;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
