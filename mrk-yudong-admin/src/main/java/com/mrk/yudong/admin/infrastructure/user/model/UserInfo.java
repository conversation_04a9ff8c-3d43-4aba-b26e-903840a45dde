package com.mrk.yudong.admin.infrastructure.user.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UserInfo extends Model<UserInfo> {


    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 密码
     */
    @JsonIgnore
    private String password;

    /**
     * 随机盐
     */
    @JsonIgnore
    private String salt;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 设备数量
     */
    private Integer equipmentNum;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 是否为会员：0-否，1-是
     */
    private Integer isMember;

    /**
     * 是否开通连续续费：0-否，1-是
     */
    private Integer isOpenRenew;

    /**
     * 会员到期时间
     */
    private LocalDate expireTime;

    /**
     * 会员自动续费扣款时间
     */
    private LocalDate autoMemberTime;

    /**
     * 加入时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime joinTime;

    /**
     * 是否绑定微信：0-否，1-是
     */
    private Integer isWx;

    /**
     * 微信openId
     */
    private String wxOpenId;

    /**
     * 微信unionid
     */
    private String wxUnionId;

    /**
     * 是否绑定QQ：0-否，1-是
     */
    private Integer isQq;

    /**
     * QQopenId
     */
    private String qqOpenId;

    /**
     * 是否绑定苹果ID
     */
    private Integer isApple;

    /**
     * 苹果openId
     */
    private String appleOpenId;

    /**
     * 是否绑定Facebook
     */
    private Integer isFacebook;

    /**
     * facebook ID
     */
    private String facebookId;

    /**
     * 状态：0-禁用，1-正常
     */
    private Integer status;

    /**
     * 用户备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;

    /**
     * 友盟推送设备token
     */
    private String deviceToken;

    /**
     * 注册渠道
     */
    private Integer channel;

    /**
     * 是否为机器人：0-否，1-是
     */
    private Integer isRobot;

    /**
     * 操作人id
     */
    private Long operationBy;

    /**
     * 操作人名称
     */
    @TableField(exist = false)
    private String operationName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
