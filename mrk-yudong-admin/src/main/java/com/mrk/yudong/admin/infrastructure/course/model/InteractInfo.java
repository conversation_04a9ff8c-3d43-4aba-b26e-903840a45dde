package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 互动词表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
@Data
@JsonIgnoreProperties(value = {"createId", "updateId", "updateTime"})
@EqualsAndHashCode(callSuper = false)
public class InteractInfo extends Model<InteractInfo> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "显示名称不能为空")
    @Length(max = 256, message = "显示名称长度不能超过240位")
    private String name;

    /**
     * 是否为预发数据：0-否，1-是
     */
    private Integer isTra;

    /**
     * 位建人ID
     */
    private Long createId;

    /**
     * 创建人名称
     */
    @TableField(exist = false)
    private String createName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
