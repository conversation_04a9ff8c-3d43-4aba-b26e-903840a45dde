package com.mrk.yudong.admin.biz.course.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.merach.sun.misc.form.CategoryFrom;
import com.mrk.yudong.admin.api.course.query.CourseOptionPageQry;
import com.mrk.yudong.admin.api.course.vo.*;
import com.mrk.yudong.admin.biz.course.bo.CourseOptionBO;
import com.mrk.yudong.admin.infrastructure.course.model.Course;
import com.mrk.yudong.admin.api.course.po.CourseDetailPO;
import com.mrk.yudong.admin.api.course.po.CoursePO;
import com.mrk.yudong.admin.api.course.po.PlanCoursePO;
import com.mrk.yudong.admin.api.course.po.TodayLivePO;
import com.mrk.yudong.admin.api.category.vo.CategoryVO;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.service.BaseService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 课程表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
public interface ICourseService extends BaseService<Course> {

    /**
     * 配置课程信息
     *
     * @param coursePO
     * @param courseVO
     * @return
     */
    R saveOrUpdate(Course coursePO, CourseVO courseVO);

    /**
     * 审核课程
     *
     * @param courseId
     * @param review
     * @param remark
     * @param course
     * @return
     */
    R review(Long courseId, Integer review, String remark, Course course);

    /**
     * 审核记录列表
     *
     * @param page
     * @param reviewQueryVO
     * @return
     */
    IPage<CoursePO> reviewList(PageDTO<CoursePO> page, ReviewQueryVO reviewQueryVO);

    /**
     * 删除课程
     *
     * @param courseId
     * @return
     */
    R remove(Long courseId);

    /**
     * 分页查询课程列表
     *
     * @param page
     * @param courseQueryVO
     * @return
     */
    IPage<CoursePO> query(PageDTO<CoursePO> page, CourseQueryVO courseQueryVO);

    /**
     * 查询课程列表
     *
     * @param courseQueryVO
     * @return
     */
    List<JSONObject> queryMap(CourseQueryVO courseQueryVO);

    /**
     * 课程详情
     *
     * @param courseId
     * @return
     */
    ResDTO<CourseDetailPO> detail(Long courseId);

    /**
     * 准备直播
     *
     * @param course
     * @return
     */
    R readyLive(Course course);

    /**
     * 开启 / 关闭 直播
     *
     * @param course
     * @param operation
     * @return
     */
    R live(Course course, Integer operation);

    /**
     * 上线 / 下线 课程
     *
     * @param course
     * @param operation
     * @param message
     * @return
     */
    R line(Course course, Integer operation, String message);

    /**
     * 查询今日直播课
     *
     * @param page
     * @param isTra
     * @param channel
     * @return
     */
    IPage<TodayLivePO> queryTodayLive(PageDTO<TodayLivePO> page, Integer isTra, Integer channel);

    /**
     * 更换教练
     *
     * @param course
     * @param coachName
     * @return
     */
    R coach(Course course, String coachName);

    /**
     * 审核课程导出
     *
     * @param reviewQueryVO
     * @return
     */
    List<JSONObject> reviewExport(ReviewQueryVO reviewQueryVO);

    /**
     * 课程主题查询课程列表
     *
     * @param page
     * @param themeCourseQueryVO
     * @return
     */
    IPage<JSONObject> themeCourseQuery(PageDTO<Course> page, ThemeCourseQueryVO themeCourseQueryVO);

    /**
     * 查询审核数量
     *
     * @param isTra
     * @param channel
     * @return
     */
    R reviewCount(Integer isTra, Integer channel);

    /**
     * 直播课数量查询
     *
     * @param isTra
     * @param channel
     * @return
     */
    Map<String, Object> liveCount(Integer isTra, Integer channel);

    /**
     * 导播修改课程
     *
     * @param coursePDVO
     * @return
     */
    R edit(CoursePDVO coursePDVO);

    /**
     * 查询课程反馈
     *
     * @param courseId
     * @param type
     * @return
     */
    R queryFeedback(Long courseId, Integer type);

    /**
     * 查询教练所属课程
     *
     * @param page
     * @param coachId
     * @return
     */
    IPage<JSONObject> queryBuCoachId(PageDTO<Map<String, Object>> page, Long coachId);

    /**
     * 根据课程ID获取标签
     *
     * @param courseId
     * @return
     */
    List<String> getTags(Long courseId);

    /**
     * 查询计划课程列表
     *
     * @param page
     * @param planCourseQueryVO
     * @return
     */
    R queryPlanCourse(PageDTO<PlanCoursePO> page, PlanCourseQueryVO planCourseQueryVO);

    /**
     * 修改课程状态
     *
     * @param courseId
     * @param status
     * @param liveTime
     * @param preStatus
     * @return
     */
    R updateStatus(Long courseId, Integer status, LocalDateTime liveTime, Integer preStatus);

    /**
     * 查询课程统计数据
     *
     * @param page
     * @param courseQueryVO
     * @return
     */
    IPage<CoursePO> queryStatistics(PageDTO<CoursePO> page, CourseQueryVO courseQueryVO);

    /**
     * 查询课程统计数据导出
     *
     * @param courseQueryVO 查询参数
     * @return java.util.List<com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @date 2022/3/1 13:11
     */
    List<JSONObject> queryStatisticsExport(CourseQueryVO courseQueryVO);

    /**
     * 修改训练计划上下线状态
     *
     * @param ids
     * @return
     */
    boolean updateActivityId(Set<Long> ids);

    /**
     * 复制课程
     *
     * @param course
     * @return
     */
    R copyCourse(Course course);

    /**
     * 更新比赛状态
     *
     * @param course 要更新的课程信息
     * @param isStart      是否为开始比赛
     * @return ResDTO<Boolean>
     * <AUTHOR>
     * @date 2022/8/8 15:25
     */
    ResDTO<Boolean> race(Course course, boolean isStart);

    PageDTO<CategoryVO> queryPage(CategoryFrom pageForm);

    /**
     * 通过courseId的名称获取名字
     * @param courseId
     * @return
     */
    String getCategoryName(Long courseId);

    /**
     * 判断课程表中是否有课程id数据
     * @param courseId
     * @return
     */
    Boolean isExistByCategoryId(Long courseId);

    IPage<CourseOptionBO> pageCourseOptions(PageDTO<Course> page, CourseOptionPageQry qry);
}
