package com.mrk.yudong.admin.biz.device.service.impl;

import com.mrk.yudong.admin.infrastructure.device.model.EquipmentCategory;
import com.mrk.yudong.admin.infrastructure.device.mapper.EquipmentCategoryMapper;
import com.mrk.yudong.admin.biz.device.service.IEquipmentCategoryService;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 设备分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@Service
public class EquipmentCategoryServiceImpl extends BaseServiceImpl<EquipmentCategoryMapper, EquipmentCategory> implements IEquipmentCategoryService {

    @Override
    public R queryList() {
        return R.ok(baseMapper.queryList());
    }

}
