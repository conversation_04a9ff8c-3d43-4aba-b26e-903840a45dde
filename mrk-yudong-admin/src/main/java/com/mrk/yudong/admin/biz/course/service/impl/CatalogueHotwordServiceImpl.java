package com.mrk.yudong.admin.biz.course.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.admin.infrastructure.course.model.CatalogueHotword;
import com.mrk.yudong.admin.infrastructure.course.mapper.CatalogueHotwordMapper;
import com.mrk.yudong.admin.biz.course.service.ICatalogueHotwordService;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 环节热词表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@Service
public class CatalogueHotwordServiceImpl extends BaseServiceImpl<CatalogueHotwordMapper, CatalogueHotword> implements ICatalogueHotwordService {

    @Override
    public List<JSONObject> getHotWordList(Long courseCatalogueId) {
        return baseMapper.getHotWordList(courseCatalogueId);
    }

}
