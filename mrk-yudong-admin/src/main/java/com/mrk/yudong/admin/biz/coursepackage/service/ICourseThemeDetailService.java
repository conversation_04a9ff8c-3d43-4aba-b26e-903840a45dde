package com.mrk.yudong.admin.biz.coursepackage.service;

import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CourseThemeDetail;
import com.mrk.yudong.core.service.BaseService;

import java.util.List;

/**
 * <p>
 * 课程 - 课程主题 中间关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
public interface ICourseThemeDetailService extends BaseService<CourseThemeDetail> {

    List<JSONObject> queryCourse(Long themeId);

}
