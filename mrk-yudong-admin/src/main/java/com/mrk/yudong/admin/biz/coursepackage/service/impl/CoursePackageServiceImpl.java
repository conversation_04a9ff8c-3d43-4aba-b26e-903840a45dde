package com.mrk.yudong.admin.biz.coursepackage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.merach.sun.common.lang.exception.BusinessException;
import com.mrk.yudong.admin.api.course.query.CourseOptionPageQry;
import com.mrk.yudong.admin.api.coursepackage.dto.packages.cmd.CoursePackageCourseDTO;
import com.mrk.yudong.admin.api.coursepackage.dto.packages.cmd.CreateCoursePackageCmd;
import com.mrk.yudong.admin.api.coursepackage.dto.packages.cmd.UpdateCoursePackageCmd;
import com.mrk.yudong.admin.api.coursepackage.dto.packages.qry.CoursePackagePageQry;
import com.mrk.yudong.admin.api.coursepackage.dto.packages.resp.CourseDTO;
import com.mrk.yudong.admin.api.coursepackage.dto.packages.resp.CoursePackageDTO;
import com.mrk.yudong.admin.api.coursepackage.dto.packages.resp.PageCoursePackageDTO;
import com.mrk.yudong.admin.biz.coach.service.ICoachInfoService;
import com.mrk.yudong.admin.biz.course.bo.CourseOptionBO;
import com.mrk.yudong.admin.biz.course.service.ICourseService;
import com.mrk.yudong.admin.biz.coursepackage.enums.SeriesStatusEnum;
import com.mrk.yudong.admin.biz.coursepackage.service.ICoursePackageRelService;
import com.mrk.yudong.admin.biz.coursepackage.service.ICoursePackageService;
import com.mrk.yudong.admin.biz.coursepackage.service.ICourseSeriesCoursePackageRelService;
import com.mrk.yudong.admin.biz.coursepackage.service.ICourseSeriesService;
import com.mrk.yudong.admin.biz.sys.service.ISysUserService;
import com.mrk.yudong.admin.feign.EquipmentFeign;
import com.mrk.yudong.admin.infrastructure.coach.model.CoachInfo;
import com.mrk.yudong.admin.infrastructure.coursepackage.mapper.CoursePackageMapper;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CoursePackage;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CoursePackageRel;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CourseSeries;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CourseSeriesCoursePackageRel;
import com.mrk.yudong.admin.infrastructure.sys.model.SysUser;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.share.po.equip.EquipmentType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 课程包 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CoursePackageServiceImpl extends BaseServiceImpl<CoursePackageMapper, CoursePackage> implements ICoursePackageService {

    private final ICoursePackageRelService coursePackageRelService;

    private final ICourseSeriesCoursePackageRelService courseSeriesCoursePackageRelService;

    private final ICourseSeriesService courseSeriesService;

    private final ISysUserService sysUserService;

    private final EquipmentFeign equipmentFeign;

    private final ICoachInfoService coachInfoService;

    private final ICourseService courseService;

    private final StringRedisTemplate redisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createCoursePackage(CreateCoursePackageCmd coursePackageCmd) {
        //增加主题
        CoursePackage coursePackage = BeanUtil.copyProperties(coursePackageCmd, CoursePackage.class);
        coursePackage.setCourseCount(coursePackageCmd.getCourses().size());
        coursePackage.setCreateId(SessionUtil.getId());
        coursePackage.setUpdateId(SessionUtil.getId());
        coursePackage.setUpdateTime(LocalDateTime.now());
        boolean saveCoursePackage = save(coursePackage);
        if (!saveCoursePackage) {
            throw new BusinessException("增加主题失败");
        }
        //增加主题关联课程数据
        boolean saveCoursePackageRel = isSaveCoursePackageRel(coursePackage.getId(), coursePackageCmd.getCourses());
        if (!saveCoursePackageRel) {
            throw new BusinessException("增加主题关联课程数据失败");
        }

        //增加系列关联主题数据
        CourseSeriesCoursePackageRel courseSeriesCoursePackageRel = new CourseSeriesCoursePackageRel();
        courseSeriesCoursePackageRel.setPackageId(coursePackage.getId());
        courseSeriesCoursePackageRel.setSeriesId(coursePackageCmd.getSeriesId());
        courseSeriesCoursePackageRel.setCreateId(SessionUtil.getId());
        courseSeriesCoursePackageRel.setBeginTime(coursePackageCmd.getBeginTime());
        courseSeriesCoursePackageRel.setEndTime(coursePackageCmd.getEndTime());
        courseSeriesCoursePackageRel.setSeq(coursePackageCmd.getSeq());
        courseSeriesCoursePackageRel.setUpdateId(SessionUtil.getId());
        courseSeriesCoursePackageRel.setUpdateTime(LocalDateTime.now());
        boolean saveCourseSeriesCoursePackageRel = courseSeriesCoursePackageRelService.save(courseSeriesCoursePackageRel);
        if (!saveCourseSeriesCoursePackageRel) {
            throw new BusinessException("增加系列关联主题数据失败");
        }
        redisTemplate.delete(RedisKeyConstant.SERIES_KEY);
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCoursePackage(UpdateCoursePackageCmd coursePackageCmd) {
        CourseSeriesCoursePackageRel dbCourseSeriesCoursePackageRel = courseSeriesCoursePackageRelService.getById(coursePackageCmd.getId());
        if (dbCourseSeriesCoursePackageRel == null) {
            throw new BusinessException("主题不存在");
        }

        boolean updateSeriesCoursePackageRel = courseSeriesCoursePackageRelService.updateById(BeanUtil.copyProperties(coursePackageCmd, CourseSeriesCoursePackageRel.class));
        if (!updateSeriesCoursePackageRel) {
            throw new BusinessException("更新系列关联主题数据失败");
        }

        CoursePackage coursePackage = BeanUtil.copyProperties(coursePackageCmd, CoursePackage.class);
        coursePackage.setId(dbCourseSeriesCoursePackageRel.getPackageId());
        boolean updateCoursePackage = this.updateById(coursePackage);
        if (!updateCoursePackage) {
            throw new BusinessException("更新主题失败");
        }

        operationCoursePackageRel(coursePackageCmd, dbCourseSeriesCoursePackageRel);
        redisTemplate.delete(RedisKeyConstant.SERIES_KEY);

        return Boolean.TRUE;
    }

    private void operationCoursePackageRel(UpdateCoursePackageCmd coursePackageCmd, CourseSeriesCoursePackageRel dbCourseSeriesCoursePackageRel) {
        List<CoursePackageCourseDTO> courses = coursePackageCmd.getCourses();
        if (CollUtil.isEmpty(courses)) {
            return;
        }

        List<CoursePackageRel> existCoursePackageRelList = coursePackageRelService.findByCoursePackageId(dbCourseSeriesCoursePackageRel.getPackageId());
        List<Long> existCourseIds = existCoursePackageRelList.stream().map(CoursePackageRel::getCourseId).collect(Collectors.toList());
        List<Long> courseIds = courses.stream().map(CoursePackageCourseDTO::getCourseId).collect(Collectors.toList());
        List<Long> needCreateCourseIds = courseIds.stream().filter(courseId -> !existCourseIds.contains(courseId)).collect(Collectors.toList());
        //增加主题关联课程数据
        if (CollUtil.isNotEmpty(needCreateCourseIds)) {
            isSaveCoursePackageRel(
                    dbCourseSeriesCoursePackageRel.getPackageId(),
                    coursePackageCmd.getCourses().stream().filter(c -> needCreateCourseIds.contains(c.getCourseId())).collect(Collectors.toList())
            );
        }

        List<Long> removeCourseIds = existCourseIds.stream().filter(courseId -> !courseIds.contains(courseId)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(removeCourseIds)) {
            coursePackageRelService.deletePackageCourses(dbCourseSeriesCoursePackageRel.getPackageId(), removeCourseIds);
        }

        Map<Long, CoursePackageCourseDTO> coursesPercent = courses.stream().collect(Collectors.toMap(CoursePackageCourseDTO::getCourseId, c -> c));
        for (CoursePackageRel existCoursePackage : existCoursePackageRelList) {
            if (!coursesPercent.containsKey(existCoursePackage.getCourseId())) {
                continue;
            }

            existCoursePackage.setSeq(coursesPercent.get(existCoursePackage.getCourseId()).getSeq());
            coursePackageRelService.updateById(existCoursePackage);
        }
    }

    private boolean isSaveCoursePackageRel(Long coursePackageId, List<CoursePackageCourseDTO> courses) {
        List<CoursePackageRel> coursePackageRelList = new ArrayList<>();
        for (CoursePackageCourseDTO courseDTO : courses) {
            CoursePackageRel coursePackageRel = new CoursePackageRel();
            coursePackageRel.setCourseId(courseDTO.getCourseId());
            coursePackageRel.setPackageId(coursePackageId);
            coursePackageRel.setCreateId(SessionUtil.getId());
            coursePackageRel.setUpdateId(SessionUtil.getId());
            coursePackageRel.setUpdateTime(LocalDateTime.now());
            coursePackageRel.setSeq(courseDTO.getSeq());
            coursePackageRelList.add(coursePackageRel);
        }

        return coursePackageRelService.saveBatch(coursePackageRelList);
    }

    @Override
    public Boolean deleteCoursePackage(Long id) {
        CourseSeriesCoursePackageRel courseSeriesCoursePackageRel = courseSeriesCoursePackageRelService.getById(id);
        if (courseSeriesCoursePackageRel == null) {
            throw new BusinessException("主题不存在");
        }
        boolean removeById = courseSeriesCoursePackageRelService.removeById(id);
        if (!removeById) {
            return Boolean.FALSE;
        }

        return updatePackageCount(courseSeriesCoursePackageRel.getSeriesId(), -1);
    }

    @Override
    public CoursePackageDTO getCoursePackage(Long id) {
        CourseSeriesCoursePackageRel courseSeriesCoursePackageRel = courseSeriesCoursePackageRelService.getById(id);
        if (courseSeriesCoursePackageRel == null) {
            throw new BusinessException("主题不存在");
        }

        CoursePackage coursePackage = getById(courseSeriesCoursePackageRel.getPackageId());
        if (coursePackage == null) {
            throw new BusinessException("主题不存在");
        }
        CoursePackageDTO coursePackageDTO = BeanUtil.copyProperties(coursePackage, CoursePackageDTO.class);
        coursePackageDTO.setBeginTime(courseSeriesCoursePackageRel.getBeginTime());
        coursePackageDTO.setEndTime(courseSeriesCoursePackageRel.getEndTime());
        coursePackageDTO.setSeq(courseSeriesCoursePackageRel.getSeq());
        coursePackageDTO.setSeriesId(courseSeriesCoursePackageRel.getSeriesId());

        coursePackageDTO.setCourses(convertCourseInfos(coursePackage.getId()));

        return coursePackageDTO;
    }

    private List<CourseDTO> convertCourseInfos(Long packageId) {
        List<CoursePackageRel> rels = coursePackageRelService.findByCoursePackageId(packageId);
        if (CollectionUtil.isEmpty(rels)) {
            return new ArrayList<>();
        }

        List<Long> courseIds = rels.stream().map(CoursePackageRel::getCourseId).collect(Collectors.toList());
        CourseOptionPageQry pageQry = new CourseOptionPageQry();
        pageQry.setIds(courseIds);
        IPage<CourseOptionBO> coursesResult = courseService.pageCourseOptions(new PageDTO<>(0, courseIds.size()), pageQry);
        if (ObjectUtil.isNull(coursesResult) || CollUtil.isEmpty(coursesResult.getRecords())) {
            return new ArrayList<>();
        }

        List<CourseOptionBO> courses = coursesResult.getRecords();
        Map<Long, CourseOptionBO> courseMap = courses.stream()
                .collect(Collectors.toMap(CourseOptionBO::getId, Function.identity()));

        List<CourseDTO> result = new ArrayList<>();
        for (CoursePackageRel rel : rels) {
            if (!courseMap.containsKey(rel.getCourseId())) {
                continue;
            }

            CourseOptionBO course = courseMap.get(rel.getCourseId());
            CourseDTO courseDTO = BeanUtil.copyProperties(course, CourseDTO.class);
            courseDTO.setSeq(rel.getSeq());
            CoachInfo coachInfo = coachInfoService.getById(course.getCoachId());
            if (coachInfo != null) {
                courseDTO.setCoachName(coachInfo.getName());
            }
            result.add(courseDTO);
        }

        result.stream().sorted(Comparator.comparing(CourseDTO::getSeq)).collect(Collectors.toList());
        return result;
    }

    @Override
    public Page<PageCoursePackageDTO> pageCoursePackage(CoursePackagePageQry coursePackagePageQry) {
        LambdaQueryWrapper<CourseSeriesCoursePackageRel> courseSeriesCoursePackageRelLambdaQueryWrapper = new LambdaQueryWrapper<>(new CourseSeriesCoursePackageRel()).
                eq(CourseSeriesCoursePackageRel::getIsDelete, 0)
                .eq(!Objects.isNull(coursePackagePageQry.getSeriesId()), CourseSeriesCoursePackageRel::getSeriesId, coursePackagePageQry.getSeriesId())
                .orderByDesc(CourseSeriesCoursePackageRel::getUpdateTime);
        Page<CourseSeriesCoursePackageRel> page = courseSeriesCoursePackageRelService.page(new Page<>(coursePackagePageQry.getCurrent(), coursePackagePageQry.getSize()), courseSeriesCoursePackageRelLambdaQueryWrapper);
        if (CollUtil.isEmpty(page.getRecords())) {
            return new Page<>();
        }
        Map<Long, CoursePackage> coursePackageRelMap = listByIds(page.getRecords().stream().map(CourseSeriesCoursePackageRel::getPackageId).collect(Collectors.toList())).stream().collect(Collectors.toMap(CoursePackage::getId, v -> v));
        Map<Long, String> userMap = sysUserService.listByIds(page.getRecords().stream().map(CourseSeriesCoursePackageRel::getUpdateId).collect(Collectors.toList())).stream().collect(Collectors.toMap(SysUser::getId, SysUser::getNickName));
        Map<Long, String> equipMap = Convert.toList(EquipmentType.class, equipmentFeign.getTopEquipment().getData()).stream().collect(Collectors.toMap(EquipmentType::getId, EquipmentType::getTypeName));
        Page<PageCoursePackageDTO> respIPage = new Page<>(coursePackagePageQry.getCurrent(), coursePackagePageQry.getSize(), page.getTotal());
        respIPage.setRecords(page.getRecords().stream().map(sales -> {
            PageCoursePackageDTO courseSeriesResp = BeanUtil.copyProperties(sales, PageCoursePackageDTO.class);
            if (MapUtil.isNotEmpty(coursePackageRelMap) && sales.getPackageId() != null) {
                CoursePackage coursePackage = MapUtil.get(coursePackageRelMap, sales.getPackageId(), CoursePackage.class);
                if (coursePackage != null) {
                    courseSeriesResp = BeanUtil.copyProperties(courseSeriesResp, PageCoursePackageDTO.class);
                    if (MapUtil.isNotEmpty(equipMap) && StrUtil.isNotBlank(coursePackage.getProductIds())) {
                        List<String> productNames = new ArrayList<>();
                        for (String productId : coursePackage.getProductIds().split(",")) {
                            String name = MapUtil.getStr(equipMap, Long.valueOf(productId));
                            if (StrUtil.isNotBlank(name)) {
                                productNames.add(name);
                            }
                        }
                        if (CollUtil.isNotEmpty(productNames)) {
                            courseSeriesResp.setProductNames(productNames);
                        }
                    }
                    courseSeriesResp.setCourseCount(coursePackage.getCourseCount());
                    courseSeriesResp.setName(coursePackage.getName());
                }
            }

            if (MapUtil.isNotEmpty(userMap) && sales.getUpdateId() != null) {
                String name = MapUtil.getStr(userMap, sales.getUpdateId());
                if (StrUtil.isNotBlank(name)) {
                    courseSeriesResp.setOperatorName(name);
                }
            }
            courseSeriesResp.setStatusName(SeriesStatusEnum.fromCode(sales.getStatus()).getDesc());
            courseSeriesResp.setOperationTime(sales.getUpdateTime());
            return courseSeriesResp;
        }).collect(Collectors.toList()));
        return respIPage;
    }

    @Override
    public Boolean changeCoursePackageStatus(Long id, int status) {
        CourseSeriesCoursePackageRel courseSeriesCoursePackageRel = new CourseSeriesCoursePackageRel();
        courseSeriesCoursePackageRel.setUpdateId(SessionUtil.getId());
        courseSeriesCoursePackageRel.setId(id);
        courseSeriesCoursePackageRel.setStatus(status);
        boolean updateById = courseSeriesCoursePackageRelService.updateById(courseSeriesCoursePackageRel);
        if (updateById) {
            CourseSeriesCoursePackageRel courseSeriesCoursePackageRel1 = courseSeriesCoursePackageRelService.getById(id);
            if (courseSeriesCoursePackageRel1 != null) {
                return updatePackageCount(courseSeriesCoursePackageRel1.getSeriesId(), status == 1 ? 1 : -1);
            }
        }
        return Boolean.FALSE;
    }

    private boolean updatePackageCount(Long seriesId, Integer operatorCount) {
        CourseSeries courseSeries = courseSeriesService.getById(seriesId);
        if (courseSeries == null) {
            throw new BusinessException("系列不存在");
        }
        courseSeries.setPackageCount(courseSeries.getPackageCount() + operatorCount);

        return courseSeriesService.updateById(courseSeries);
    }
}
