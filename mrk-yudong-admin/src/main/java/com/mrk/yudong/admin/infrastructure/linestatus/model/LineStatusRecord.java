package com.mrk.yudong.admin.infrastructure.linestatus.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * @description: 在线状态记录表
 * @author: ljx
 * @create: 2023/11/2 14:07
 * @Version 1.0
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("line_status_record")
public class LineStatusRecord {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务id
     */
    private Long businessId;

    /**
     * 业务类型，1课程，2计划，3课包
     */
    private Integer businessType;

    /**
     * 在线状态，0上线，1下线
     */
    private Integer lineStatus;
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;


}
