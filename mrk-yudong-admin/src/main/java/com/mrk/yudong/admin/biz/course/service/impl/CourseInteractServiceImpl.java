package com.mrk.yudong.admin.biz.course.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.mrk.yudong.admin.biz.course.service.ICourseInteractService;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseInteractMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseInteract;
import com.mrk.yudong.admin.infrastructure.course.model.InteractInfo;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 课程互动词表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
@RequiredArgsConstructor
@Service
public class CourseInteractServiceImpl extends BaseServiceImpl<CourseInteractMapper, CourseInteract> implements ICourseInteractService {

    private final StringRedisTemplate redisTemplate;

    @Override
    public List<InteractInfo> getListByCourseId(Long courseId) {
        String key = RedisKeyConstant.COURSE_INTERACT_LIST_KEY.replace("${courseId}", courseId.toString());
        List<String> range = redisTemplate.opsForList().range(key, 0, -1);

        List<InteractInfo> list;
        if (CollUtil.isEmpty(range)) {
            list = baseMapper.getListByCourseId(courseId);
            if (CollUtil.isNotEmpty(list)) {
                range = list.stream().map(JSON::toJSONString).collect(Collectors.toList());
                redisTemplate.opsForList().rightPushAll(key, range);
                redisTemplate.expire(key, Duration.ofDays(7L));
            }
        } else {
            list = range.stream().map(obj -> JSON.parseObject(obj, InteractInfo.class)).collect(Collectors.toList());
        }

        return list;
    }

}
