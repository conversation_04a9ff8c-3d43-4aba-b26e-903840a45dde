package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 课程音乐关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CourseMusic extends Model<CourseMusic> {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 音乐id
     */
    @NotNull(message ="请选择音乐信息")
    private Long musicId;

    /**
     * 状态0正常
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 音乐名称
     */
    @TableField(exist = false)
    private String musicName;
    /**
     * 音乐地址
     */
    @TableField(exist = false)
    private String musicUrl;
    /**
     * 逻辑删除
     */
    private Integer isDelete;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
