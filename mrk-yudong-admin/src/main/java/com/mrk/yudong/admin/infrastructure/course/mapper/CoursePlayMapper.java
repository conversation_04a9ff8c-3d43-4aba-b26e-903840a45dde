package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.api.course.vo.PlayerQueryVO;
import com.mrk.yudong.admin.infrastructure.course.model.CoursePlay;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 课程播放明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-07
 */
public interface CoursePlayMapper extends BaseMapper<CoursePlay> {


    /**
     * 根据日期统计总数量
     *
     * @param playerQueryVO
     * @return
     */
    List<JSONObject> sumByDate(@Param("playerQueryVO") PlayerQueryVO playerQueryVO);


}
