package com.mrk.yudong.admin.infrastructure.tag.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@TableName("user_tag_value_enum")
@Data
@Accessors(chain = true)
public class UserTagValue {

    private Long id;

    private Long tagId;

    private String tagCode;

    private String enumCode;

    private String enumLabel;

    private LocalDateTime createTime;

    private Long createId;

    private LocalDateTime updateTime;

    private Long updateId;

}
