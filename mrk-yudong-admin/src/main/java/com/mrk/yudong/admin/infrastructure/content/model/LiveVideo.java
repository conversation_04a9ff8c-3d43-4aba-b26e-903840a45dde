package com.mrk.yudong.admin.infrastructure.content.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.bo.TopRanksWithMeBO;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.dto.app.VideoDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 实景视频
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LiveVideo extends Model<LiveVideo> {

    private static final long serialVersionUID = 1L;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 品牌类型：1-Merit，2-绝影
     */
    @NotNull(message = "请选择品牌类型")
    private Integer brandType;

    /**
     * 会员类型：10-VIP、20-SVIP、30-绝影会员
     */
    private Integer vipType;

    /**
     * 设备类型id
     */
    @NotNull(message = "请选择具体的设备类型")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long equipTypeId;

    /**
     * 实景视频标题
     */
    @NotBlank(message = "请填写实景视频标题")
    @Length(max = 20, min = 2, message = "请输入2-20个字符，支持中英文、数字组成")
    private String title;

    /**
     * 实景视频图片
     */
    @NotBlank(message = "请上传实景视频图片")
    private String image;

    /**
     * 实景视频
     */
    private String video;

    /**
     * 阿里云视频id
     */
    @NotBlank(message = "请上传实景视频")
    private String videoId;

    /**
     * 转码后的实景视频
     */
    private String transcodingVideo;

    /**
     * 排序
     */
    @NotNull(message = "请填写排序")
    private Integer sort;
    /**
     * 上线状态，1上，0下
     */
    private Integer onlineStatus;

    /**
     * 删除状态0：未删除，1：已删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 视频时长
     */
    private String videoDuration;

    /**
     * 地标
     */
    private String landmarks;

    /**
     * 色值
     */
    private String color;

    /**
     * 视频时长单位秒
     */
    @TableField(exist = false)
    private String durationOfSeconds;
    /**
     * 创建人
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long operationBy;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime operationDate;
    /**
     * 预发布环境
     */
    private Integer isTra;
    /**
     * 操作人名称
     */
    @TableField(exist = false)
    private String operationByName;
    /**
     * 设备名称
     */
    @TableField(exist = false)
    private String equipName;
    /**
     * 设备icon
     */
    @TableField(exist = false)
    private String equipmentTypeIcon;
    /**
     * 阿里云视频信息
     */
    @TableField(exist = false)
    private List<VideoDTO> videoInfo;

    /**
     * 排名信息
     */
    @TableField(exist = false)
    private TopRanksWithMeBO rankInfo;

    /**
     * 背景音乐id
     */
    private Long musicId;

    /**
     * 背景音乐url
     */
    @TableField(exist = false)
    private String musicUrl;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
