package com.mrk.yudong.admin.infrastructure.device.gateway.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.merach.sun.common.layer.web.PageDTO;
import com.merach.sun.device.api.ProductApi;
import com.merach.sun.device.api.ProductModelApi;
import com.merach.sun.device.dto.cmd.model.*;
import com.merach.sun.device.dto.cmd.product.SetProductTsl;
import com.merach.sun.device.dto.cmd.product.UpdateProductCmd;
import com.merach.sun.device.dto.qry.ProductModelControlMappingQry;
import com.merach.sun.device.dto.qry.ProductModelQry;
import com.merach.sun.device.dto.qry.ProductQry;
import com.merach.sun.device.dto.resp.model.ModelControlMappingDTO;
import com.merach.sun.device.dto.resp.model.ModelFeatureDTO;
import com.merach.sun.device.dto.resp.model.ProductModelDetailDTO;
import com.merach.sun.device.dto.resp.product.ProductDetailDTO;
import com.merach.sun.device.enums.CommunicationProtocolEnum;
import com.merach.sun.device.enums.ProductModelTypeEnum;
import com.mrk.yudong.admin.api.device.dto.cmd.model.CreateBackstageProductModelCmd;
import com.mrk.yudong.admin.api.device.dto.cmd.model.SetProductModelTsl;
import com.mrk.yudong.admin.api.device.dto.cmd.model.UpdateBackstageProductCmd;
import com.mrk.yudong.admin.api.device.dto.cmd.model.UpdateBackstageProductModelCmd;
import com.mrk.yudong.admin.api.device.dto.cmd.product.ProductSortCmd;
import com.mrk.yudong.admin.api.device.dto.cmd.product.SetProductImage;
import com.mrk.yudong.admin.api.device.dto.qry.BackstageProductModelQry;
import com.mrk.yudong.admin.api.device.dto.qry.BackstageProductQry;
import com.mrk.yudong.admin.api.device.dto.resp.model.BackstageModelDetailDTO;
import com.mrk.yudong.admin.api.device.dto.resp.model.PageModelDTO;
import com.mrk.yudong.admin.api.device.dto.resp.product.BackstageProductDetailDTO;
import com.mrk.yudong.admin.api.device.dto.resp.product.PageProductDTO;
import com.mrk.yudong.admin.biz.device.service.IEquEquipmentTypeService;
import com.mrk.yudong.admin.biz.device.service.IEquipDictService;
import com.mrk.yudong.admin.biz.sys.service.ISysUserService;
import com.mrk.yudong.admin.feign.EquipmentFeign;
import com.mrk.yudong.admin.infrastructure.device.gateway.ProductGateway;
import com.mrk.yudong.admin.infrastructure.device.model.EquEquipmentType;
import com.mrk.yudong.admin.infrastructure.device.model.EquipDict;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.share.dto.equip.BackstageEquipmentType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class ProductGatewayImpl implements ProductGateway {
    private final ProductModelApi productModelApi;
    private final ProductApi productApi;
    private final ISysUserService systemUserService;
    private final EquipmentFeign equipmentFeign;
    private final IEquEquipmentTypeService equipmentTypeService;
    private final IEquipDictService equipDictService;

    @Override
    public BackstageProductDetailDTO updateProduct(UpdateBackstageProductCmd cmd) {
        UpdateProductCmd updateProductCmd = BeanUtil.copyProperties(cmd, UpdateProductCmd.class);
        updateProductCmd.setUpdateId(SessionUtil.getId());
        updateProductCmd.setTsl(BeanUtil.copyProperties(cmd.getTsl(), SetProductTsl.class));
        ProductDetailDTO productDetailDTO = productApi.updateProduct(updateProductCmd);
        BackstageProductDetailDTO backstageProductDetailDTO = BeanUtil.copyProperties(productDetailDTO, BackstageProductDetailDTO.class);
        updateEquProduct(cmd);
        return backstageProductDetailDTO;
    }

    @Override
    public BackstageProductDetailDTO getProduct(BackstageProductQry backstageProductQry) {
        return BeanUtil.copyProperties(productApi.getProduct(BeanUtil.copyProperties(backstageProductQry, ProductQry.class)), BackstageProductDetailDTO.class);
    }

    @Override
    public Page<PageProductDTO> pageProducts(BackstageProductQry backstageProductQry) {
        PageDTO<ProductDetailDTO> productDetailDTOPage = productApi.pageProducts(BeanUtil.copyProperties(backstageProductQry, ProductQry.class));
        Page<PageProductDTO> resultPage = new Page<>();
        if (CollUtil.isEmpty(productDetailDTOPage.getRecords())) {
            return resultPage;
        }
        List<PageProductDTO> pageProductDTOS = getPageProductDTOS(productDetailDTOPage);
        resultPage.setTotal(productDetailDTOPage.getTotal());
        resultPage.setRecords(pageProductDTOS);
        return resultPage;
    }

    @Override
    public Boolean productSort(List<ProductSortCmd> productSortCmd) {
        return productApi.productSort(BeanUtil.copyToList(productSortCmd, com.merach.sun.device.dto.cmd.product.ProductSortCmd.class));
    }

    @Override
    public BackstageModelDetailDTO createProductModel(CreateBackstageProductModelCmd cmd) {
        if (null == cmd) {
            return null;
        }

        CreateProductModelCmd createProductModelCmd = BeanUtil.copyProperties(cmd, CreateProductModelCmd.class);
        createProductModelCmd.setUserId(SessionUtil.getId());
        BackstageEquipmentType equipmentType = insertEquipmentModel(cmd);
        if (null == equipmentType) {
            return null;
        }
        createProductModelCmd.setOriginId(equipmentType.getId());
        return convertBackstageModelDetailDTO(productModelApi.createProductModel(createProductModelCmd));
    }


    @Override
    public BackstageModelDetailDTO updateProductModel(UpdateBackstageProductModelCmd cmd) {

        UpdateProductModelCmd updateProductModelCmd = BeanUtil.copyProperties(cmd, UpdateProductModelCmd.class);
        updateProductModelCmd.setUserId(SessionUtil.getId());
        BackstageModelDetailDTO backstageModelDetailDTO = convertBackstageModelDetailDTO(productModelApi.updateProductModel(updateProductModelCmd));
        if (null == backstageModelDetailDTO) {
            return null;
        }
        if (CollUtil.size(getProductModelDetails(cmd.getId())) <= 1) {
            updateEquipmentModel(cmd, backstageModelDetailDTO.getOriginId());
        }
        return backstageModelDetailDTO;
    }

    @Override
    public Boolean deleteProductModel(Long id) {
        if (null == id) {
            return Boolean.FALSE;
        }

        ProductModelDetailDTO productModelById = productModelApi.getProductModelById(id);
        if (null == productModelById) {
            return Boolean.FALSE;
        }
        Boolean delEquipment = delEquipment(productModelById.getOriginId());
        if (!delEquipment) {
            log.warn("删除型号失败,id：{}", id);
        }

        DeleteProductModelCmd deleteProductModelCmd = new DeleteProductModelCmd();
        deleteProductModelCmd.setId(id);
        deleteProductModelCmd.setCurrentUserId(SessionUtil.getId());
        return productModelApi.deleteProductModel(deleteProductModelCmd);
    }

    @Override
    public BackstageModelDetailDTO getProductModel(BackstageProductModelQry productQry) {

        return convertBackstageModelDetailDTO(productModelApi.getProductModel(BeanUtil.copyProperties(productQry, ProductModelQry.class)));
    }

    @Override
    public List<BackstageModelDetailDTO> listProductModels(BackstageProductModelQry productQry) {
        List<ProductModelDetailDTO> productModelDetailDTOS = productModelApi.listProductModels(BeanUtil.copyProperties(productQry, ProductModelQry.class));
        if (CollUtil.isEmpty(productModelDetailDTOS)) {
            return null;
        }

        return productModelDetailDTOS.stream().map(ProductGatewayImpl::convertBackstageModelDetailDTO).collect(Collectors.toList());
    }

    @Override
    public Page<PageModelDTO> pageProductModels(BackstageProductModelQry productQry) {
        PageDTO<ProductModelDetailDTO> productModelDetailDTOPageDTO = productModelApi.pageProductModels(BeanUtil.copyProperties(productQry, ProductModelQry.class));
        Page<PageModelDTO> resultPage = new Page<>(productQry.getCurrent(), productQry.getSize());
        if (CollUtil.isEmpty(productModelDetailDTOPageDTO.getRecords())) {
            return resultPage;
        }
        resultPage.setTotal(productModelDetailDTOPageDTO.getTotal());
        List<PageModelDTO> pageModels = getPageModels(productModelDetailDTOPageDTO);
        resultPage.setRecords(pageModels);
        return resultPage;
    }


    @Override
    public List<ModelControlMappingDTO> findModelControlMappings(ProductModelControlMappingQry qry) {
        if (ObjectUtil.isNull(qry.getModelId())) {
            return new ArrayList<>();
        }

        return productModelApi.findModelControlMappings(qry);
    }

    @Override
    public Boolean setModelControlMappings(SetModelControlMappingCmd cmd) {
        cmd.setUserId(SessionUtil.getId());
        log.info("ProductGatewayImpl->setModelControlMappings->cmd: {}", cmd);
        return productModelApi.setModelControlMapping(cmd);
    }

    private List<PageProductDTO> getPageProductDTOS(PageDTO<ProductDetailDTO> productDetailDTOPageDTO) {
        Map<Long, String> finalNickNameMap = systemUserService.listUserNameByIds(productDetailDTOPageDTO
                .getRecords()
                .stream()
                .map(ProductDetailDTO::getUpdateId)
                .collect(Collectors.toSet()));
        ProductModelQry productModelQry = new ProductModelQry();
        productModelQry.setProductIds(productDetailDTOPageDTO.getRecords().stream().map(ProductDetailDTO::getId).collect(Collectors.toList()));
        Map<Long, List<ProductModelDetailDTO>> ProductModelDetailDTOs = productModelApi.listProductModels(productModelQry).stream().collect(Collectors.groupingBy(ProductModelDetailDTO::getProductId));
        return productDetailDTOPageDTO.getRecords().stream().map(v -> {
            PageProductDTO pageProductDTO = BeanUtil.copyProperties(v, PageProductDTO.class);
            if (finalNickNameMap.containsKey(v.getUpdateId())) {
                pageProductDTO.setOperationName(finalNickNameMap.get(v.getUpdateId()));
            }
            if (v.getUpdateTime() != null) {
                pageProductDTO.setOperationTime(v.getUpdateTime());
            }
            List<ProductModelDetailDTO> productModelDetailDTOS = ProductModelDetailDTOs.get(pageProductDTO.getId());
            if (CollUtil.isNotEmpty(productModelDetailDTOS)) {
                pageProductDTO.setModelNumber(productModelDetailDTOS.size());
            }
            return pageProductDTO;
        }).collect(Collectors.toList());
    }

    private static BackstageModelDetailDTO convertBackstageModelDetailDTO(ProductModelDetailDTO productModel) {
        if (Objects.isNull(productModel)) {
            return null;
        }
        BackstageModelDetailDTO backstageModelDetailDTO = BeanUtil.copyProperties(
                productModel
                , BackstageModelDetailDTO.class,
                "controlMappings", "tsl", "uniqueModelIdentify", "featureDescription");
        backstageModelDetailDTO.setTsl(BeanUtil.copyProperties(productModel.getTsl(), SetProductModelTsl.class));
        if (CollUtil.isNotEmpty(productModel.getUniqueModelIdentify())) {
            backstageModelDetailDTO.setUniqueModelIdentify(BeanUtil.copyToList(productModel.getUniqueModelIdentify(), SetUniqueModelIdentify.class));
        }
        if (CollUtil.isNotEmpty(productModel.getControlMappings())) {
            backstageModelDetailDTO.setControlMappings(BeanUtil.copyToList(productModel.getControlMappings(), CreateModelControlMappingCmd.class));
        }
        if (CollUtil.isNotEmpty(productModel.getFeatureDescription())) {
            backstageModelDetailDTO.setFeatureDescription(BeanUtil.copyToList(productModel.getFeatureDescription(), ModelFeatureDTO.class));
        }
        backstageModelDetailDTO.setName(productModel.getModelName());
        backstageModelDetailDTO.setCode(productModel.getModelCode());
        return backstageModelDetailDTO;
    }

    private List<PageModelDTO> getPageModels(PageDTO<ProductModelDetailDTO> productModelDetailDTOPageDTO) {
        Set<Long> userIds = productModelDetailDTOPageDTO
                .getRecords()
                .stream()
                .map(ProductModelDetailDTO::getUpdateId)
                .collect(Collectors.toSet());
        Map<Long, String> finalNickNameMap = systemUserService.listUserNameByIds(userIds);
        Map<Integer, String> productModelType = ProductModelTypeEnum.getProductModelType();
        Map<Integer, String> communicationProtocols = CommunicationProtocolEnum.getCommunicationProtocol();
        Map<Integer, String> prefixes = equipDictService.list("dict_key", ConditionEnum.EQ, RedisKeyConstant.EQUIP_TYPE).stream().collect(Collectors.toMap(EquipDict::getId, EquipDict::getValue));
        return productModelDetailDTOPageDTO.getRecords().stream().map(v -> {
            PageModelDTO pageModelDTO = BeanUtil.copyProperties(v, PageModelDTO.class);
            pageModelDTO.setName(v.getModelName());
            pageModelDTO.setCode(v.getModelCode());
            if (null != v.getTsl()) {
                pageModelDTO.setIsOta(v.getTsl().getIsOta());
            }
            if (v.getUpdateTime() != null) {
                pageModelDTO.setOperationTime(v.getUpdateTime());
            }
            if (finalNickNameMap.containsKey(v.getUpdateId())) {
                pageModelDTO.setOperationName(finalNickNameMap.get(v.getUpdateId()));
            }
            String modelType = productModelType.get(v.getTsl().getIsElectromagneticControl());
            if (StrUtil.isNotBlank(modelType)) {
                pageModelDTO.setModelType(modelType);
            }
            String bluetoothPrefix = MapUtil.getStr(prefixes, v.getBluetoothPrefix());
            if (StrUtil.isNotBlank(bluetoothPrefix)) {
                pageModelDTO.setBluetoothPrefix(bluetoothPrefix);
            }
            String communicationProtocol = MapUtil.getStr(communicationProtocols, v.getCommunicationProtocol());
            if (StrUtil.isNotBlank(communicationProtocol)) {
                pageModelDTO.setCommunicationProtocol(communicationProtocol);
            }
            return pageModelDTO;
        }).collect(Collectors.toList());
    }

    private void updateEquProduct(UpdateBackstageProductCmd cmd) {
        BackstageEquipmentType backstageEquipmentType = new BackstageEquipmentType();
        backstageEquipmentType.setLevel(0);
        backstageEquipmentType.setType(cmd.getType());
        backstageEquipmentType.setParentId(0L);
        backstageEquipmentType.setTypeImages(cmd.getCover());
        backstageEquipmentType.setId(cmd.getId());
        SetProductImage iconImages = cmd.getIconImages();
        backstageEquipmentType.setIconImages(iconImages.getCourseDetailIcon());
        backstageEquipmentType.setBigIconImages(iconImages.getBigIcon());
        backstageEquipmentType.setLiveIcon(iconImages.getLiveIcon());
        backstageEquipmentType.setDarkIcon(iconImages.getUnconnectedIcon());
        backstageEquipmentType.setBrightIcon(iconImages.getConnectedIcon());
        equipmentFeign.updateEquipment(backstageEquipmentType);
    }


    private BackstageEquipmentType insertEquipmentModel(CreateBackstageProductModelCmd cmd) {
        LambdaQueryWrapper<EquEquipmentType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(EquEquipmentType::getParentId, cmd.getProductId());
        lambdaQueryWrapper.eq(EquEquipmentType::getPrefixId, cmd.getBluetoothPrefix());
        lambdaQueryWrapper.eq(EquEquipmentType::getCode, cmd.getCode());
        List<EquEquipmentType> types = equipmentTypeService.list(lambdaQueryWrapper);
        if (CollUtil.isNotEmpty(types)) {
            BackstageEquipmentType backstageEquipmentType = new BackstageEquipmentType();
            backstageEquipmentType.setId(types.get(0).getId());
            return backstageEquipmentType;
        }
        BackstageEquipmentType backstageEquipmentType = getBackstageEquipmentType(cmd.getTsl());
        backstageEquipmentType.setLevel(1);
        backstageEquipmentType.setPrefixId(cmd.getBluetoothPrefix());
        backstageEquipmentType.setCommunicationProtocol(cmd.getCommunicationProtocol());
        backstageEquipmentType.setCode(cmd.getCode());
        backstageEquipmentType.setTypeName(cmd.getName());
        backstageEquipmentType.setTypeImages(cmd.getCover());
        backstageEquipmentType.setIsSupportConnection(BaseConstant.INT_TRUE);
        backstageEquipmentType.setProductType(cmd.getCommunicationType());
        backstageEquipmentType.setHelpCenter(cmd.getHelpCenter());
        backstageEquipmentType.setInstallTutorial(cmd.getInstallatioTutorial());
        backstageEquipmentType.setParentId(cmd.getProductId());
        backstageEquipmentType.setCreateBy(SessionUtil.getId());
        backstageEquipmentType.setElectrodeType(cmd.getElectrodeType());
        R r = equipmentFeign.saveEquipment(backstageEquipmentType);
        if (R.isFail(r)) {
            log.warn("新增型号失败：{}", r.getMessage());
            throw new RuntimeException("新增型号失败：" + r.getMessage());
        }
        return Convert.convert(BackstageEquipmentType.class, r.getData());
    }

    private void updateEquipmentModel(UpdateBackstageProductModelCmd cmd, Long originId) {
        BackstageEquipmentType backstageEquipmentType = getBackstageEquipmentType(cmd.getTsl());
        backstageEquipmentType.setId(originId);
        backstageEquipmentType.setLevel(1);
        backstageEquipmentType.setPrefixId(cmd.getBluetoothPrefix());
        backstageEquipmentType.setCode(cmd.getCode());
        backstageEquipmentType.setCommunicationProtocol(cmd.getCommunicationProtocol());
        backstageEquipmentType.setTypeName(cmd.getName());
        backstageEquipmentType.setTypeImages(cmd.getCover());
        backstageEquipmentType.setProductType(cmd.getCommunicationType());
        backstageEquipmentType.setHelpCenter(cmd.getHelpCenter());
        backstageEquipmentType.setInstallTutorial(cmd.getInstallatioTutorial());
        backstageEquipmentType.setParentId(cmd.getProductId());
        backstageEquipmentType.setCreateBy(SessionUtil.getId());
        backstageEquipmentType.setElectrodeType(cmd.getElectrodeType());
        R r = equipmentFeign.updateEquipment(backstageEquipmentType);
        if (R.isFail(r)) {
            log.warn("修改型号失败：{}", r.getMessage());
            throw new RuntimeException("修改型号失败：" + r.getMessage());
        }
    }

    private static BackstageEquipmentType getBackstageEquipmentType(SetProductModelTsl tsl) {
        BackstageEquipmentType backstageEquipmentType = BeanUtil.copyProperties(tsl, BackstageEquipmentType.class);
        backstageEquipmentType.setLevel(1);
        backstageEquipmentType.setShowResistance(tsl.getControlResistance());
        backstageEquipmentType.setShowGear(tsl.getControlGear());
        backstageEquipmentType.setEigenValue(tsl.getVersionEigenValue());
        backstageEquipmentType.setShowSlope(tsl.getControlSlope());
        backstageEquipmentType.setShowSpeed(tsl.getControlSpeed());
        return backstageEquipmentType;
    }

    public Boolean delEquipment(Long id) {
        if (id == null) {
            return Boolean.FALSE;
        }
        List<ProductModelDetailDTO> productModelDetailDTOS = getProductModelDetails(id);
        boolean delStatus = Boolean.FALSE;
        if (CollUtil.size(productModelDetailDTOS) <= 1) {
            delStatus = equipmentTypeService.removeById(id);
        }

        if (delStatus) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private List<ProductModelDetailDTO> getProductModelDetails(Long id) {
        ProductModelDetailDTO productModel = productModelApi.getProductModelById(id);
        if (null == productModel) {
            return null;
        }
        ProductModelQry productModelQry = new ProductModelQry();
        productModelQry.setOriginId(productModel.getOriginId());
        return productModelApi.listProductModels(productModelQry);
    }
}
