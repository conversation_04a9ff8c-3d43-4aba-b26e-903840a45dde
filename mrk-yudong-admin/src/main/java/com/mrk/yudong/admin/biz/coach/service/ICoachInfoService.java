package com.mrk.yudong.admin.biz.coach.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.infrastructure.coach.model.CoachInfo;
import com.mrk.yudong.admin.api.coach.po.CoachPO;
import com.mrk.yudong.admin.api.coach.vo.QueryConditionVO;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.service.BaseService;

import java.util.List;

/**
 * <p>
 * 教练信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-24
 */
public interface ICoachInfoService extends BaseService<CoachInfo> {

    /**
     * 查询教练列表
     *
     * @param page
     * @param queryConditionVO
     * @return
     */
    IPage<CoachInfo> query(PageDTO<CoachInfo> page, QueryConditionVO queryConditionVO);

    /**
     * 导出教练列表
     *
     * @param queryConditionVO
     * @return
     */
    List<JSONObject> query(QueryConditionVO queryConditionVO);

    /**
     * 是否被禁用
     *
     * @param sysUserId
     * @return
     */
    boolean isDisable(String sysUserId);

    /**
     * 教练详情信息
     *
     * @param id
     * @return
     */
    CoachInfo detail(Long id);

    /**
     * 更新教练信息
     *
     * @param coachPO
     * @return
     */
    R updateCoachInfo(CoachPO coachPO);

    /**
     * 获取教练编辑信息
     *
     * @param id
     * @return
     */
    ResDTO<CoachPO> info(Long id);

    /**
     * 显示教练明细
     *
     * @param id
     * @return
     */
    R view(Long id);

    /**
     * 获取教练封面图
     *
     * @param coachId
     * @return
     */
    R getCoachCoverList(Long coachId);

}
