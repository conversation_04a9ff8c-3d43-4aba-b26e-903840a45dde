package com.mrk.yudong.admin.biz.sporttarget.service.impl;

import com.merach.sun.user.dto.cmd.sporttarget.SportTargetCmd;
import com.mrk.yudong.admin.biz.sporttarget.service.SportTargetService;
import com.mrk.yudong.admin.infrastructure.sporttarget.model.SportTarget;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class SportTargetServiceImpl implements SportTargetService {

    @Override
    public SportTarget getTodaySportTarget(Long userId) {
        return null;
    }

    @Override
    public Boolean updateSportTarget(SportTargetCmd sportTargetCmd) {
        return null;
    }
}

