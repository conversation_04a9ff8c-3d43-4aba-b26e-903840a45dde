package com.mrk.yudong.admin.biz.course.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.infrastructure.course.model.CourseKeywords;
import com.mrk.yudong.core.service.BaseService;

import java.util.Map;

/**
 * <p>
 * 关键词信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-09
 */
public interface ICourseKeywordsService extends BaseService<CourseKeywords> {

    IPage<CourseKeywords> query(Page<CourseKeywords> page, Map<String, Object> param);

    /**
     * 删除 维度/关键字
     */
    Boolean deleteCourseKeywords(CourseKeywords courseKeywords);
}
