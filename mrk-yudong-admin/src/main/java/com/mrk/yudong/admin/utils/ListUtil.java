package com.mrk.yudong.admin.utils;

import com.mrk.yudong.admin.biz.sys.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ListUtil {
    private final ISysUserService sysUserService;

    public <R> List<R> fillUserName(List<R> list, Function<R, Long> getFunction, BiConsumer<R, String> setFunction) {
        Set<Long> ids = list.stream().map(getFunction).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> userNameData = sysUserService.getOperationUserNameData(ids);
        list.stream().peek(data -> setFunction.accept(data, Optional.ofNullable(userNameData.get(getFunction.apply(data))).orElse(""))).collect(Collectors.toList());
        return list;
    }
}
