package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 课程字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CourseMeta extends Model<CourseMeta> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 显示名称
     */
    private String name;

    /**
     * 字典编码
     */
    private String code;

    /**
     * 值
     */
    private String val;

    /**
     * 排序
     */
    private Integer sort;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
