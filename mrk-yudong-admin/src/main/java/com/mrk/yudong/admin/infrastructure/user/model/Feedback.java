package com.mrk.yudong.admin.infrastructure.user.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 意见反馈表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Feedback extends Model<Feedback> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 手机号
     */
    @TableField(exist = false)
    private String mobile;

    /**
     * 反馈意见
     */
    private String remark;

    /**
     * 联系信息
     */
    private String contact;

    /**
     * 处理内容
     */
    private String review;

    /**
     * 媒体资源信息
     */
    private String mediaInfos;

    /**
     * 状态：0-未处理，1-已处理
     */
    private Integer status;

    /**
     * 类型：0app反馈，1内部反馈
     */
    private Integer typeId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 版本号
     */
    private String versionNumber;

    /**
     * 修改人名称
     */
    @TableField(exist = false)
    private String updateName;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 系统版本号
     */
    private String systemVersionNumber;

    /**
     * 手机型号
     */
    private String phoneModel;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
