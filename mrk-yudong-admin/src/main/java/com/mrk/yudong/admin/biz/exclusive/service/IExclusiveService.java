package com.mrk.yudong.admin.biz.exclusive.service;

import com.mrk.yudong.admin.api.exclusive.dto.ExclusiveDataDTO;
import com.mrk.yudong.admin.api.exclusive.dto.ExclusiveOptionDTO;
import com.mrk.yudong.admin.api.exclusive.query.ExclusiveQuery;
import com.mrk.yudong.admin.api.exclusive.vo.ExclusiveVo;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.model.ResDTO;

public interface IExclusiveService {

    PageDTO<ExclusiveDataDTO> page(ExclusiveQuery query);

    ExclusiveOptionDTO option(Integer type);

    ResDTO<Boolean> save(ExclusiveVo exclusiveVo);

    ResDTO<Boolean> update(Long exclusiveId, ExclusiveVo exclusiveVo);

    ResDTO<Boolean> remove(Long exclusiveId);

}
