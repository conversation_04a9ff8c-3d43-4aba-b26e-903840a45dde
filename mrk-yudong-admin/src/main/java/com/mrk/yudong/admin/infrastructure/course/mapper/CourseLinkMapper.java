package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseLink;
import com.mrk.yudong.share.po.CourseLinkPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 课程小节表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
public interface CourseLinkMapper extends BaseMapper<CourseLink> {

    /**
     * 根据课程ID和环节ID获取课程小节信息列表
     *
     * @param courseId
     * @param catalogueId
     * @return
     */
    List<CourseLinkPO> list(@Param("courseId") Long courseId, @Param("catalogueId") Long catalogueId);

}
