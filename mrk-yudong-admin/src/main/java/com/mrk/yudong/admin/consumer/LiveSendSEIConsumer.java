package com.mrk.yudong.admin.consumer;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.merach.sun.content.api.LiveApi;
import com.merach.sun.content.constant.ContentQueueConstant;
import com.merach.sun.content.dto.cmd.live.AddTrancodeSEICmd;
import com.merach.sun.content.dto.mq.LiveSendSEIDTO;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
@RocketMQMessageListener(topic = "${rocketmq.live.delay-topic}", consumerGroup = "${rocketmq.consumer.live.send-sei-group}", selectorExpression = ContentQueueConstant.LIVE_SEND_SEI_QUEUE)
public class LiveSendSEIConsumer implements RocketMQListener<LiveSendSEIDTO> {
    private final StringRedisTemplate redisTemplate;
    private final LiveApi liveApi;

    @Override
    public void onMessage(LiveSendSEIDTO liveSendSEIDTO) {
        log.warn("sei listener data: {}", JSON.toJSONString(liveSendSEIDTO));
        try {
            String seiKey = RedisKeyConstant.SEI_KEY.replace("${linkId}", liveSendSEIDTO.getLinkId());
            Boolean hasKey = redisTemplate.hasKey(seiKey);
            if (BooleanUtil.isFalse(hasKey)) {
                log.warn("sei listener key expire. linkId: {}", liveSendSEIDTO.getLinkId());
                return;
            }

            String key = RedisKeyConstant.COURSE_SEI_KEY.replace("${courseId}", liveSendSEIDTO.getCourseId());
            hasKey = redisTemplate.opsForHash().hasKey(key, liveSendSEIDTO.getUuid());
            if (BooleanUtil.isFalse(hasKey)) {
                log.warn("sei listener uuid expire. linkId: {} uuid: {}", liveSendSEIDTO.getLinkId(), liveSendSEIDTO.getUuid());
                return;
            }
            liveApi.addTrancodeSEI(new AddTrancodeSEICmd("", liveSendSEIDTO.getStreamName(), liveSendSEIDTO.getSei()));
            redisTemplate.opsForHash().delete(key, liveSendSEIDTO.getUuid());
            redisTemplate.delete(seiKey);
        } catch (Exception e) {
            log.error("sei listener exception: ", e);
        }
    }
}
