package com.mrk.yudong.admin.biz.content.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mrk.yudong.admin.biz.app.service.IMusicService;
import com.mrk.yudong.admin.biz.content.service.ILiveVideoService;
import com.mrk.yudong.admin.infrastructure.content.mapper.LiveVideoMapper;
import com.mrk.yudong.admin.infrastructure.content.model.LiveVideo;
import com.mrk.yudong.admin.infrastructure.course.model.Music;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.share.dto.app.LiveVideoDTO;
import com.mrk.yudong.share.dto.app.MusicDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 * 实景视频 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-05
 */
@Service
@Slf4j
public class LiveVideoServiceImpl extends BaseServiceImpl<LiveVideoMapper, LiveVideo> implements ILiveVideoService {


    @Autowired
    private IMusicService iMusicService;

    @Override
    public boolean updateOnlineStatus(Long id, Integer oper) {
        return baseMapper.updateOnlineStatus(id,oper);
    }


    @Override
    public LiveVideoDTO queryLiveVideoById(Long id) {

        LiveVideo liveVideo = this.getById(id);
        if (Objects.isNull(liveVideo)){
            log.warn("query live video failed, id :{}", id);
            return null;
        }

        LiveVideoDTO liveVideoDTO = BeanUtil.copyProperties(liveVideo, LiveVideoDTO.class);

        if (Objects.nonNull(liveVideo.getMusicId())){
            //查询音乐数据
            Music music = iMusicService.getById(liveVideo.getMusicId());
            if (Objects.nonNull(music)){
                MusicDTO musicDTO = BeanUtil.copyProperties(music, MusicDTO.class);
                liveVideoDTO.setMusicDTO(musicDTO);
            }
        }
        return liveVideoDTO;
    }
}
