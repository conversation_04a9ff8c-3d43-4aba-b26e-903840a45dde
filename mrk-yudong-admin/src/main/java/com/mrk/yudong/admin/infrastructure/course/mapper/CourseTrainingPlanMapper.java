package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.api.course.vo.CourseCatalogueVO;
import com.mrk.yudong.admin.infrastructure.course.model.CourseTrainingPlan;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 训练计划 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
public interface CourseTrainingPlanMapper extends BaseMapper<CourseTrainingPlan> {
    /**
     * 修改banner上下线状态
     * @param id
     * @param oper 1上线，2下线
     * @return
     */
    boolean updateOnlineStatus(@Param("id") Long id, @Param("oper") Integer oper);
    /**
     * 获条数
     * @return
     */
    List<CourseCatalogueVO> getCountList(Integer isTra );

}
