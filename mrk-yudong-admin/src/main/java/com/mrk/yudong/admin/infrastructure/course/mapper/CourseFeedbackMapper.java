package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseFeedback;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 课程反馈表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
public interface CourseFeedbackMapper extends BaseMapper<CourseFeedback> {

    /**
     * 查询课程反馈列表
     *
     * @param courseId
     * @param type
     * @return
     */
    List<JSONObject> query(@Param("courseId") Long courseId, @Param("type") Integer type);
}
