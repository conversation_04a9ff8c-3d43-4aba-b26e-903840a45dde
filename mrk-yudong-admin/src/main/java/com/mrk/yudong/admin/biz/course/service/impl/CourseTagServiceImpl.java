package com.mrk.yudong.admin.biz.course.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.mrk.yudong.admin.biz.course.service.ICourseTagDetailService;
import com.mrk.yudong.admin.biz.course.service.ICourseTagService;
import com.mrk.yudong.admin.biz.course.service.ICourseTrainingPlanService;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseTagMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseTag;
import com.mrk.yudong.admin.infrastructure.course.model.CourseTrainingPlan;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.share.constant.ResponseConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 课程标签表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
@RequiredArgsConstructor
@Service
public class CourseTagServiceImpl extends BaseServiceImpl<CourseTagMapper, CourseTag> implements ICourseTagService {

    private final StringRedisTemplate redisTemplate;

    private final ICourseTagDetailService courseTagDetailService;

    private final ICourseTrainingPlanService courseTrainingPlanService;

    /**
     * 根据父级ID查询标签
     *
     * @param parentId
     * @return
     */
    @Override
    public List<CourseTag> query(Long parentId) {
        return baseMapper.query(parentId, SessionUtil.getIsTra());
    }

    /**
     * 删除标签
     *
     * @param tagId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R removeCourseTag(Long tagId, CourseTag courseTag) {
        boolean remove = this.removeById(tagId);
        if (remove) {
            if (courseTag.getParentId() == null) {
                List<CourseTag> courseTags = this.query(tagId);
                if (CollUtil.isNotEmpty(courseTags)) {
                    courseTags.forEach(ct -> this.updatePlanTag(ct.getId()));
                }

                this.remove("parent_id", ConditionEnum.EQ, tagId);
                courseTagDetailService.remove("top_id", ConditionEnum.EQ, tagId);
            } else {
                courseTagDetailService.remove("tag_id", ConditionEnum.EQ, tagId);
                this.updatePlanTag(tagId);
            }

            String key = RedisKeyConstant.COURSE_TAG_CHILDREN_KEY.replace("${isTra}", Integer.toString(SessionUtil.getIsTra()));
            redisTemplate.delete(key);
            return R.ok();
        }

        return R.fail(ResponseConstant.COMMON_OPERATION_FAILED);
    }

    /**
     * 更新计划标签
     *
     * @param tagId
     */
    private void updatePlanTag(Long tagId) {
        List<CourseTrainingPlan> list = courseTrainingPlanService.list("FIND_IN_SET({0}, tag_id)", ConditionEnum.APPLY, tagId, "id", "tag_id");
        if (CollUtil.isEmpty(list)) {
            return;
        }

        list.forEach(v -> {
            List<String> tags = StrUtil.split(v.getTagId(), StrPool.C_COMMA, true, true);
            tags.remove(tagId.toString());
            v.setTagId(CollUtil.isEmpty(tags) ? StrUtil.EMPTY : String.join(StrPool.COMMA, tags));
        });
        courseTrainingPlanService.updateBatchById(list, list.size());
    }

}
