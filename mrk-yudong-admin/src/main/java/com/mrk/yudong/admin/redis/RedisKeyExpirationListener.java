package com.mrk.yudong.admin.redis;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.mrk.yudong.admin.biz.course.service.ICourseService;
import com.mrk.yudong.admin.biz.course.service.ICourseTrainingPlanService;
import com.mrk.yudong.admin.infrastructure.course.model.Course;
import com.mrk.yudong.admin.rabbit.IRabbitOutput;
import com.mrk.yudong.core.utils.RedisUtil;
import com.mrk.yudong.share.bo.RaceInfoBO;
import com.mrk.yudong.share.bo.RaceRankRedisBO;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.share.constant.course.CourseConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Description: Redis超时监听器
 *
 * <AUTHOR>
 * @create 2021−03-25 1:14 下午
 */
@Slf4j
@Component
public class RedisKeyExpirationListener extends KeyExpirationEventMessageListener {
    private final ICourseTrainingPlanService trainingPlanService;

    private final RedisUtil redisUtil;

    private final ICourseService courseService;

    private final IRabbitOutput rabbitOutput;

    public RedisKeyExpirationListener(RedisMessageListenerContainer listenerContainer, ICourseTrainingPlanService trainingPlanService, RedisUtil redisUtil, ICourseService courseService, IRabbitOutput rabbitOutput) {
        super(listenerContainer);
        this.trainingPlanService = trainingPlanService;
        this.redisUtil = redisUtil;
        this.courseService = courseService;
        this.rabbitOutput = rabbitOutput;
    }

    /**
     * redis监听回调
     *
     * @param message 失效的key
     * @param pattern 字节
     */
    @Override
    public void onMessage(Message message, byte[] pattern) {
        //获取过期的key
        String expireKey = message.toString();
        //训练计划上下线
        if (expireKey.contains("COURSE_UP_TRARIN")) {
            planStart(expireKey);
        }
        if (expireKey.contains("COURSE_DOWN_TRARIN")) {
            planEnd(expireKey);
        }

        String[] split = expireKey.split(":");
        switch (split[0]) {
            case RedisKeyConstant.COURSE_RACE_START:
                this.race(Long.valueOf(split[1]), true);
                break;
            case RedisKeyConstant.COURSE_RACE_CLOSE:
                this.race(Long.valueOf(split[1]), false);
                break;
        }
    }

    /**
     * 计划上线
     *
     * @param expireKey key
     */
    public void planStart(String expireKey) {
        log.debug("COURSE_UP_TRARIN is:" + expireKey);
        List<String> collect = Stream.of(expireKey.split(":")).collect(Collectors.toList());
        String id = collect.get(1);
        Boolean oldLock = redisUtil.setIfAbsent(id, "1");
        if (!oldLock) {
            return;
        }
        if (StrUtil.isNotBlank(id)) {
            trainingPlanService.updateOnlineStatus(Long.valueOf(id), 2);
        }
    }

    /**
     * 计划下线
     *
     * @param expireKey key
     */
    public void planEnd(String expireKey) {
        log.debug("COURSE_DOWN_TRARIN is:" + expireKey);
        List<String> collect = Stream.of(expireKey.split(":")).collect(Collectors.toList());
        String id = collect.get(1);
        Boolean oldLock = redisUtil.setIfAbsent("END:" + id, "1");
        if (!oldLock) {
            return;
        }
        if (StrUtil.isNotBlank(id)) {
            trainingPlanService.updateOnlineStatus(Long.valueOf(id), 3);
        }
    }


    /**
     * 开始 / 结束课程比赛
     *
     * @param courseId 课程ID
     * @param isStart  是否为开始比赛
     * <AUTHOR>
     * @date 2022/8/8 18:23
     */
    @Transactional(rollbackFor = Exception.class)
    public void race(Long courseId, boolean isStart) {
        log.warn("=== courseId: {} 触发自动开启/关闭竞速比赛redis监听 ===", courseId);

        String lock = RedisKeyConstant.COURSE_RACE_LOCK.replace("${courseId}", courseId.toString());
        Boolean absent = redisUtil.setIfAbsent(lock, courseId.toString(), 10L);
        if (absent == null || !absent) {
            log.warn("=========== 重复触发自动开启/关闭竞速比赛redis监听，courseId: {} ===========", courseId);
            return;
        }

        Course course = courseService.getById(courseId);
        if (course == null) {
            log.warn("=== courseId: {} get data is null. closeCourseRace is fail ===", courseId);
            return;
        }

        Integer status = course.getStatus();
        if (!status.equals(CourseConstant.STATUS_40)) {
            log.warn("=== courseId: {} status: {} 不满足比赛规则 ===", courseId, status);
            return;
        }

        Integer raceStatus = course.getRaceStatus();
        if (isStart && !raceStatus.equals(CourseConstant.RACE_STATUS_0)) {
            log.warn("=== courseId: {} raceStatus: {} 不满足开始比赛规则 ===", courseId, raceStatus);
            return;
        }

        if (!isStart && !raceStatus.equals(CourseConstant.RACE_STATUS_10)) {
            log.warn("=== courseId: {} raceStatus: {} 不满足关闭比赛规则 ===", courseId, raceStatus);
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        int updateRaceStatus = isStart ? CourseConstant.RACE_STATUS_10 : CourseConstant.RACE_STATUS_20;
        Course updateCourse = new Course().setId(courseId).setRaceStatus(updateRaceStatus);
        RaceInfoBO raceInfoBO = new RaceInfoBO(updateCourse.getRaceStatus(), course.getRaceTarget());
        if (isStart) {
            updateCourse.setRaceStartTime(now);
            raceInfoBO.setRaceStartTime(now);
            String courseRaceKey = RedisKeyConstant.COURSE_RACE_KEY.replace("${courseId}", courseId.toString());
            redisUtil.setCacheObject(courseRaceKey, courseId.toString(), 1L, TimeUnit.HOURS);

            RaceRankRedisBO.KEYS.forEach(key -> {
                String raceRecodeKey = RedisKeyConstant.COURSE_RACE_USER_RECORD_DATA_KEY.replace("${courseId}", courseId.toString()).replace("${time}", key.toString());
                redisUtil.setCacheObject(raceRecodeKey, courseId.toString(), key.longValue(), TimeUnit.MINUTES);
            });
        } else {
            updateCourse.setRaceEndTime(now);
            raceInfoBO.setRaceEndTime(now).setRaceStartTime(course.getRaceStartTime());
        }

        String raceInfoKey = RedisKeyConstant.COURSE_RACE_INFO_KEY.replace("${courseId}", courseId.toString());
        redisUtil.setCacheObject(raceInfoKey, JSON.toJSONString(raceInfoBO), 1L, TimeUnit.HOURS);
        if (!isStart) {
            rabbitOutput.userCourseRaceOutput().send(MessageBuilder.withPayload(courseId).build());
        }

        courseService.updateById(updateCourse);
        log.warn("=== courseId: {} 自动关闭比赛成功 ===", courseId);
    }

}
