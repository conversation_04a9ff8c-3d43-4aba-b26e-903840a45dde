package com.mrk.yudong.admin.infrastructure.coursepackage.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 课程 - 课程主题 中间关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CourseThemeDetail extends Model<CourseThemeDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 课程ID
     */
    private Long courseId;

    private Integer seq;

    /**
     * 计划id
     */
    private Long planId;

    /**
     * 主题ID
     */
    private Long themeId;

    /**
     * 实景视频id
     */
    private Long liveVideoId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public CourseThemeDetail(Long themeId, Long courseId, Integer seq) {
        this.courseId = courseId;
        this.themeId = themeId;
        this.seq = seq;
        this.planId = null;
        this.liveVideoId = null;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
