package com.mrk.yudong.admin.biz.course.service.impl;

import com.mrk.yudong.admin.api.course.vo.CourseCatalogueVO;
import com.mrk.yudong.admin.biz.course.service.ICourseTrainingPlanService;
import com.mrk.yudong.admin.biz.linestatusrecord.service.LineStatusRecordService;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseTrainingPlanMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseTrainingPlan;
import com.mrk.yudong.admin.infrastructure.linestatus.model.LineStatusRecord;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.share.enums.linestatus.LineStatusBusinessEnum;
import com.mrk.yudong.share.enums.linestatus.LineStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 训练计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CourseTrainingPlanServiceImpl extends BaseServiceImpl<CourseTrainingPlanMapper, CourseTrainingPlan> implements ICourseTrainingPlanService {

    private final LineStatusRecordService lineStatusRecordService;
    @Override
    public boolean updateOnlineStatus(Long id, Integer oper) {
        boolean updateOnlineStatus = baseMapper.updateOnlineStatus(id, oper);
        saveLineStatus(id, oper);
        return updateOnlineStatus;
    }

    @Override
    public List<CourseCatalogueVO> getCountList() {
        return baseMapper.getCountList(SessionUtil.getIsTra());
    }


    private void saveLineStatus(Long id, Integer oper) {
        try {
            log.info("保存计划上下线状态:{}",id);
            LineStatusRecord createLineStatusRecordCmd = new LineStatusRecord();
            if (oper.equals(2)) {
                createLineStatusRecordCmd.setLineStatus(LineStatusEnum.ONLINE.getCode());
            } else if(oper.equals(3)) {
                createLineStatusRecordCmd.setLineStatus(LineStatusEnum.OFFLINE.getCode());
            }
            createLineStatusRecordCmd.setBusinessId(id);
            createLineStatusRecordCmd.setCreateId(SessionUtil.getId());
            createLineStatusRecordCmd.setBusinessType(LineStatusBusinessEnum.PLAN.getCode());
            lineStatusRecordService.createLineStatusRecord(createLineStatusRecordCmd);
        } catch (Exception e) {
            log.error("保存计划上下线状态异常:{}",e);
        }
    }

}
