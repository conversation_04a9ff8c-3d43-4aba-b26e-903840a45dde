package com.mrk.yudong.admin.infrastructure.producttest.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * app版本控制表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TestAppVersion extends Model<TestAppVersion> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 终端：1-Android，2-IOS
     */
    private Integer terminal;

    /**
     * 显示名称
     */
    private String name;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 版本描述
     */
    private String remark;

    /**
     * 更新类型：1-选择更新，2-强制更新
     */
    private Integer type;

    /**
     * 安卓更新包下载地址
     */
    private String url;

    /**
     * 是否支持会员兑换码兑换
     */
    private Integer isMemberCode;

    /**
     * 是否开放：0-否，1-是
     */
    private Integer isOpen;

    /**
     * 是否为预发数据：0-否，1-是
     */
    private Integer isTra;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改人名称
     */
    @TableField(exist = false)
    private String updateName;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime updateTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
