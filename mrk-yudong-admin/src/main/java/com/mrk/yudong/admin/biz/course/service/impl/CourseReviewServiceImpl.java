package com.mrk.yudong.admin.biz.course.service.impl;

import com.mrk.yudong.admin.infrastructure.course.model.CourseReview;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseReviewMapper;
import com.mrk.yudong.admin.biz.course.service.ICourseReviewService;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 课程审核明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
@Service
public class CourseReviewServiceImpl extends BaseServiceImpl<CourseReviewMapper, CourseReview> implements ICourseReviewService {

    @Override
    public int count(Integer isTra) {
        return baseMapper.count(isTra);
    }

}
