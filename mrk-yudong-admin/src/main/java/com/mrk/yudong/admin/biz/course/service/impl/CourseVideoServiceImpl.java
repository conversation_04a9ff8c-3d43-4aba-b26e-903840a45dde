package com.mrk.yudong.admin.biz.course.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.admin.biz.course.service.ICourseVideoService;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseVideoMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseVideo;
import com.mrk.yudong.core.model.BaseQuery;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 课程视频表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-21
 */
@RequiredArgsConstructor
@Service
public class CourseVideoServiceImpl extends BaseServiceImpl<CourseVideoMapper, CourseVideo> implements ICourseVideoService {

    private final StringRedisTemplate redisTemplate;

    /**
     * 获取课程视频
     *
     * @param courseId
     * @return
     */
    @Override
    public List<JSONObject> getVideos(Long courseId) {
        String videosKey = RedisKeyConstant.COURSE_VIDEOS_KEY.replace("${courseId}", courseId.toString());
        List<String> range = redisTemplate.opsForList().range(videosKey, 0, -1);
        List<JSONObject> videos;
        if (CollUtil.isEmpty(range)) {
            BaseQuery<CourseVideo> baseQuery = new BaseQuery<>();
            baseQuery.eq("status", BaseConstant.INT_TRUE).eq("course_id", courseId);
            baseQuery.select("definition", "file_url", "duration");
            baseQuery.orderByAsc("FIELD(definition, 'SD', 'OD', 'HD', 'LD', 'FD')");
            List<CourseVideo> courseVideos = this.list(baseQuery);
            if (CollUtil.isEmpty(courseVideos)) {
                return null;
            }

            videos = courseVideos.stream().map(courseVideo -> {
                JSONObject dict = new JSONObject(3);
                dict.put("definition", courseVideo.getDefinition());
                dict.put("duration", courseVideo.getDuration());
                dict.put("url", courseVideo.getFileUrl());
                redisTemplate.opsForList().rightPush(videosKey, dict.toJSONString());
                return dict;
            }).collect(Collectors.toList());

            redisTemplate.expire(videosKey, 15L, TimeUnit.DAYS);
        } else {
            videos = range.stream().map(JSON::parseObject).collect(Collectors.toList());
        }

        return videos;
    }

}
