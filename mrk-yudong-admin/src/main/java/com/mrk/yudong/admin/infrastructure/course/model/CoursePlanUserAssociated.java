package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户计划关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CoursePlanUserAssociated extends Model<CoursePlanUserAssociated> {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 计划id
     */
    private Long planId;

    /**
     * 计划开始时间
     */
    private LocalDateTime stratTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime endTime;

    /**
     * 预计训练日，以,分割
     */
    private String trainingDay;

    /**
     * 训练状态1.进行中2.已完成
     */
    private Integer status;

    /**
     * 推送时间
     */
    private String pushTime;

    /**
     * 最后一次训练时间
     */
    private LocalDateTime lastTime;

    /**
     * 是否预发
     */
    private Integer isTra;
    /**
     * 是否预发
     */
    @TableField(exist = false)
    private Integer count;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
