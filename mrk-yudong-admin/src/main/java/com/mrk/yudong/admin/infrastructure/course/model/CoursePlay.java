package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 课程播放明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CoursePlay extends Model<CoursePlay> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 播放课程状态：1-直播，2-录播
     */
    private Integer playStatus;

    /**
     * 播放时长：秒
     */
    private Integer playTime;

    /**
     * 视频播放类型：1-课程，3-计划训练，4-活动训练
     */
    private Integer type;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 用户计划ID
     */
    private Long planUserId;

    /**
     * 计划ID
     */
    private Long planId;

    /**
     * 视频类型：1-课程，2-实景视频
     */
    private Integer videoType;

    /**
     * 视频时长：秒
     */
    private Integer courseTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
