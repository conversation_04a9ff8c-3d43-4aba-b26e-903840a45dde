package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseCatalogue;
import com.mrk.yudong.share.po.CourseCataloguePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 课程目录（课程环节）表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
public interface CourseCatalogueMapper extends BaseMapper<CourseCatalogue> {

    /**
     * 根据课程ID获取课程教案环节信息列表
     *
     * @param courseId
     * @return
     */
    List<CourseCataloguePO> listByCourseId(@Param("courseId") Long courseId);

}
