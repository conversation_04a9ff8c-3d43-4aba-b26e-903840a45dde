package com.mrk.yudong.admin.biz.coursepackage.service;

import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.admin.api.coursepackage.dto.theme.cmd.CreateCourseThemeCmd;
import com.mrk.yudong.admin.api.coursepackage.dto.theme.cmd.UpdateCourseThemeCmd;
import com.mrk.yudong.admin.api.coursepackage.dto.theme.qry.CourseThemePageQuery;
import com.mrk.yudong.admin.biz.coursepackage.bo.CourseThemeBO;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CourseTheme;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.service.BaseService;

import javax.validation.Valid;

/**
 * <p>
 * 课程主题表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
public interface ICourseThemeService extends BaseService<CourseTheme> {

    PageDTO<CourseThemeBO> pageThemes(@Valid CourseThemePageQuery courseThemePageQuery);

    Boolean createCourseTheme(CreateCourseThemeCmd cmd);

    Boolean updateCourseTheme(UpdateCourseThemeCmd cmd);


    /**
     * 课程主题详情
     *
     * @param themeId 主题ID
     * @return com.mrk.yudong.core.model.R
     * <AUTHOR>
     * @date 2022/2/15 17:40
     */
    CourseThemeBO detail(Long themeId);

    /**
     * 删除课程主题
     *
     * @param themeId 主题ID
     * @return com.mrk.yudong.core.model.R
     * <AUTHOR>
     * @date 2022/2/15 17:36
     */
    Boolean remove(Long themeId);

    Boolean onlineCourseTheme(Long id, Long userId);

    Boolean offlineCourseTheme(Long id, Long userId);

}
