package com.mrk.yudong.admin.infrastructure.motion.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
public class Motion extends Model<Motion> {
    /**
     * 开发主键
     */
    private Long id;

    /**
     * 动作名称
     */
    private String name;

    /**
     * 动作类型：1-热身，2-训练，3-拉伸
     */
    private Integer type;

    private String nameAudio;

    /**
     * 部位描述
     */
    private String part;

    /**
     * 器械id
     */
    private Long productId;
    /**
     * 教练id
     */
    private Long coachId;
    /**
     * 动作封面
     */
    private String cover;

    /**
     * 详情描述
     */
    private String introduce;

    /**
     * 视频时长（秒）
     */
    private Integer duration;

    /**
     * 单个视频动作次数
     */
    private Integer motionCount;

    /**
     * 单个动作时长（秒）
     */
    private Integer oneMotionTime;

    /**
     * 每多少秒记录一次热量
     */
    private Integer secondPerTime;

    /**
     * 每次记录热量是多少
     */
    private Double kcalPerTime;

    /**
     * 上线状态：0-下线，1-上线
     */
    private Integer status;

    /**
     * 是否需要语音播报：0-不需要，1-需要
     */
    private Integer broadcastRequired;

    /**
     * '播报方式：1-计数播报，2-时长播报'
     */
    private Integer broadcastMode;

    /**
     * 语音风格：1-男声，2-女声
     */
    private Integer broadcastSound;

    private Long followAloneVideoId;

    /**
     * 跟练视频地址
     */
    private String followAloneVideo;

    private Long introduceVideoId;

    /**
     * 精讲视频地址
     */
    private String introduceVideo;

    /**
     * 动作难度
     */
    private Integer grade;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    private Integer isDelete;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
