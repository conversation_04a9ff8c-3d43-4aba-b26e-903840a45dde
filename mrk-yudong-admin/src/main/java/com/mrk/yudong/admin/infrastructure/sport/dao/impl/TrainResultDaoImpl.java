package com.mrk.yudong.admin.infrastructure.sport.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mrk.yudong.admin.infrastructure.sport.dao.TrainResultDao;
import com.mrk.yudong.admin.infrastructure.sport.mapper.TrainResultMapper;
import com.mrk.yudong.admin.infrastructure.sport.model.TrainResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TrainResultDaoImpl implements TrainResultDao {
    private final TrainResultMapper trainResultMapper;


    @Override
    public Boolean removeByTrainId(Long trainId) {
        LambdaQueryWrapper<TrainResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TrainResult::getTrainId, trainId);
        return trainResultMapper.delete(queryWrapper) > 1;
    }
}
