package com.mrk.yudong.admin.biz.device.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.merach.sun.common.layer.web.PageDTO;
import com.merach.sun.device.api.FirmwareApi;
import com.merach.sun.device.dto.cmd.firmware.CreateProductModelFirmwareCmd;
import com.merach.sun.device.dto.cmd.firmware.DeleteProductModelFirmwareCmd;
import com.merach.sun.device.dto.cmd.firmware.UpdateProductModelFirmwareCmd;
import com.merach.sun.device.dto.qry.ProductModelFirmwareQry;
import com.merach.sun.device.dto.resp.firmware.ProductModelFirmwareDetailDTO;
import com.mrk.yudong.admin.api.device.dto.cmd.firmware.CreateModelFirmwareCmd;
import com.mrk.yudong.admin.api.device.dto.cmd.firmware.UpdateModelFirmwareCmd;
import com.mrk.yudong.admin.api.device.dto.resp.firmware.BackstageModelFirmwareDetailDTO;
import com.mrk.yudong.admin.biz.device.service.IProductModelFirmwareService;
import com.mrk.yudong.core.utils.SessionUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: 产品型号固件信息
 * @author: ljx
 * @create: 2023/1/8 10:30
 * @Version 1.0
 **/
@Service
@RequiredArgsConstructor
public class ProductModelFirmwareServiceImpl implements IProductModelFirmwareService {
    private final FirmwareApi firmwareApi;


    @Override
    public BackstageModelFirmwareDetailDTO createProductModelFirmware(CreateModelFirmwareCmd cmd) {
        ProductModelFirmwareDetailDTO productModelFirmware = firmwareApi.createProductModelFirmware(BeanUtil.copyProperties(cmd, CreateProductModelFirmwareCmd.class));
        BackstageModelFirmwareDetailDTO backstageModelFirmwareDetailDTO = BeanUtil.copyProperties(productModelFirmware, BackstageModelFirmwareDetailDTO.class);
        backstageModelFirmwareDetailDTO.setCreateId(SessionUtil.getId());
        return backstageModelFirmwareDetailDTO;
    }

    @Override
    public BackstageModelFirmwareDetailDTO updateProductModelFirmware(UpdateModelFirmwareCmd cmd) {
        ProductModelFirmwareDetailDTO productModelFirmware = firmwareApi.updateProductModelFirmware(BeanUtil.copyProperties(cmd, UpdateProductModelFirmwareCmd.class));
        BackstageModelFirmwareDetailDTO backstageModelFirmwareDetailDTO = BeanUtil.copyProperties(productModelFirmware, BackstageModelFirmwareDetailDTO.class);
        backstageModelFirmwareDetailDTO.setUpdateId(SessionUtil.getId());
        return backstageModelFirmwareDetailDTO;
    }

    @Override
    public Boolean deleteProductModelFirmware(Long id) {
        if (null == id) {
            return Boolean.FALSE;
        }
        DeleteProductModelFirmwareCmd deleteProductModelFirmwareCmd = new DeleteProductModelFirmwareCmd();
        deleteProductModelFirmwareCmd.setId(id);
        deleteProductModelFirmwareCmd.setCurrentUserId(SessionUtil.getId());
        return firmwareApi.deleteProductModelFirmware(deleteProductModelFirmwareCmd);
    }

    @Override
    public BackstageModelFirmwareDetailDTO getProductModelFirmware(ProductModelFirmwareQry productModelFirmwareQry) {
        ProductModelFirmwareDetailDTO productModelFirmware = firmwareApi.getProductModelFirmware(BeanUtil.copyProperties(productModelFirmwareQry, ProductModelFirmwareQry.class));
        return BeanUtil.copyProperties(productModelFirmware, BackstageModelFirmwareDetailDTO.class);
    }

    @Override
    public List<BackstageModelFirmwareDetailDTO> listProductModelFirmwares(ProductModelFirmwareQry productModelFirmwareQry) {
        List<ProductModelFirmwareDetailDTO> productModelFirmwareDetailDTOS = firmwareApi.listProductModelFirmwares(BeanUtil.copyProperties(productModelFirmwareQry, ProductModelFirmwareQry.class));

        return BeanUtil.copyToList(productModelFirmwareDetailDTOS, BackstageModelFirmwareDetailDTO.class);
    }

    @Override
    public Page<BackstageModelFirmwareDetailDTO> pageProductModelFirmwares(ProductModelFirmwareQry productModelFirmwareQry) {
        Page<BackstageModelFirmwareDetailDTO> resultPage = new Page<>();
        PageDTO<ProductModelFirmwareDetailDTO> productModelFirmwareDetailDTOPageDTO = firmwareApi.pageProductModelFirmwares(BeanUtil.copyProperties(productModelFirmwareQry, ProductModelFirmwareQry.class));
        if (null == productModelFirmwareDetailDTOPageDTO) {
            return resultPage;
        }
        resultPage.setRecords(BeanUtil.copyToList(productModelFirmwareDetailDTOPageDTO.getRecords(), BackstageModelFirmwareDetailDTO.class));
        resultPage.setTotal(productModelFirmwareDetailDTOPageDTO.getTotal());
        return resultPage;
    }
}
