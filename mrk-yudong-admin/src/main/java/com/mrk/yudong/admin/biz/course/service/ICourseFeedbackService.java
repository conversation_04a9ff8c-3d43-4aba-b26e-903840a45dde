package com.mrk.yudong.admin.biz.course.service;

import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.admin.infrastructure.course.model.CourseFeedback;
import com.mrk.yudong.core.service.BaseService;

import java.util.List;

/**
 * <p>
 * 课程反馈表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
public interface ICourseFeedbackService extends BaseService<CourseFeedback> {

    /**
     * 查询课程反馈列表
     *
     * @param courseId
     * @param type
     * @return
     */
    List<JSONObject> query(Long courseId, Integer type);

}
