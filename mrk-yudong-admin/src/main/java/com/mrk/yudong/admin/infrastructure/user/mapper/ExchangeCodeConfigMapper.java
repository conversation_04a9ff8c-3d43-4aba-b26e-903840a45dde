package com.mrk.yudong.admin.infrastructure.user.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.infrastructure.user.model.ExchangeCodeConfig;
import com.mrk.yudong.admin.api.user.vo.ExchangeCodeConfigQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员兑换码配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Mapper
public interface ExchangeCodeConfigMapper extends BaseMapper<ExchangeCodeConfig> {

    IPage<ExchangeCodeConfig> query(Page<ExchangeCodeConfig> page, @Param("exchangeCodeConfigQueryVO") ExchangeCodeConfigQueryVO exchangeCodeConfigQueryVO);

    ExchangeCodeConfig detail(@Param("id") Long id);

    List<JSONObject> queryMap(@Param("exchangeCodeConfigQueryVO") ExchangeCodeConfigQueryVO exchangeCodeConfigQueryVO);

    List<JSONObject> createOption();

}
