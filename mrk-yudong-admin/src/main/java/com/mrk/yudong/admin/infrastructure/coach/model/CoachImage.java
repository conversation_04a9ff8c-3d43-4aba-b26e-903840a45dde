package com.mrk.yudong.admin.infrastructure.coach.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 教练生活照图片表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-24
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CoachImage extends Model<CoachImage> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 教练ID
     */
    private Long coachId;

    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public CoachImage(Long coachId, String imgUrl, Long createId) {
        this.coachId = coachId;
        this.imgUrl = imgUrl;
        this.createId = createId;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
