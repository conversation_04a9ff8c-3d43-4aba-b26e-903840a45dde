package com.mrk.yudong.admin.infrastructure.producttest.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.merach.sun.common.layer.web.PageForm;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2021−12-07 11:20 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AppVersionQueryVO extends PageForm {

    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime beginTime;

    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime endTime;

    private String name;

    private Integer terminal;

    private Integer type;

    private Long sysUserId;

}
