package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.infrastructure.course.model.InteractInfo;
import com.mrk.yudong.admin.api.course.vo.InteractInfoQueryVO;
import com.mrk.yudong.core.model.PageDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 互动词表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
public interface InteractInfoMapper extends BaseMapper<InteractInfo> {

    IPage<InteractInfo> query(PageDTO<InteractInfo> pageDTO, @Param("interactInfoQueryVO") InteractInfoQueryVO interactInfoQueryVO);

}
