package com.mrk.yudong.admin.biz.course.service.impl;

import com.mrk.yudong.admin.biz.course.service.ICourseCatalogueService;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseCatalogueMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseCatalogue;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.share.po.CourseCataloguePO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 课程目录（课程环节）表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
@Service
public class CourseCatalogueServiceImpl extends BaseServiceImpl<CourseCatalogueMapper, CourseCatalogue> implements ICourseCatalogueService {

    /**
     * 根据课程ID获取课程教案环节信息列表
     *
     * @param courseId
     * @return
     */
    @Override
    public List<CourseCataloguePO> listByCourseId(Long courseId) {
        return baseMapper.listByCourseId(courseId);
    }

}
