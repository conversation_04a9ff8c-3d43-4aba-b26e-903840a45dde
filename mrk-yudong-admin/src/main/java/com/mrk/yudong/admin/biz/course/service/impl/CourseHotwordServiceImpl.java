package com.mrk.yudong.admin.biz.course.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.biz.course.service.ICatalogueHotwordService;
import com.mrk.yudong.admin.biz.course.service.ICourseHotwordService;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseHotwordMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseHotword;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.share.constant.ResponseConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 课程热词表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
@RequiredArgsConstructor
@Service
public class CourseHotwordServiceImpl extends BaseServiceImpl<CourseHotwordMapper, CourseHotword> implements ICourseHotwordService {

    private final ICatalogueHotwordService catalogueHotwordService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R remove(Long id) {
        boolean remove = this.removeById(id);
        if (remove) {
            catalogueHotwordService.remove("hotword_id", ConditionEnum.EQ, id);
            return R.ok();
        }

        return R.fail(ResponseConstant.COMMON_OPERATION_FAILED);
    }

    @Override
    public IPage<CourseHotword> query(PageDTO<CourseHotword> pageDTO, String name, Long equipmentId, Integer stage, Integer isTra) {
        return baseMapper.query(pageDTO, name, equipmentId, stage, isTra);
    }

}
