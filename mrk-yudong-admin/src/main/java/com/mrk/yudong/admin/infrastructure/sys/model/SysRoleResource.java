package com.mrk.yudong.admin.infrastructure.sys.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 后台角色 - 后台资源 关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-17
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SysRoleResource extends Model<SysRoleResource> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 后台角色ID
     */
    private Long sysRoleId;

    /**
     * 后台资源ID
     */
    private Long sysResourceId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public SysRoleResource(Long sysRoleId, Long sysResourceId) {
        this.sysRoleId = sysRoleId;
        this.sysResourceId = sysResourceId;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
