package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 训练计划报告地标/食物配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PlanReportConfig extends Model<PlanReportConfig> {

    public static final int CONFIG_TYPE_CITY = 1;

    public static final int CONFIG_TYPE_FOOD = 2;


    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 显示名称：城市/食物
     */
    @NotBlank(message = "显示名称不能为空")
    @Length(max = 32, message = "名称最大长度不能超过32位")
    private String name;

    /**
     * 显示标识：地标/单位
     */
    @NotBlank(message = "显示标识不能为空")
    @Length(max = 32, message = "标识最大长度不能超过32位")
    private String identification;

    /**
     * 绑定设备ID
     */
    @TableField(exist = false)
    private List<Long> equipmentIds;

    /**
     * 绑定设备ID，多个 , 分割
     */
    @JsonIgnore
    private String equipmentId;

    /**
     * 数量：距离/卡路里
     */
    @NotNull(message = "数量不能为空")
    @DecimalMin(value = "0.01", message = "数量必须大于0")
    private BigDecimal num;

    /**
     * 类型：1-城市，2-食物
     */
    @NotNull(message = "请选择类型")
    @Range(min = 1, max = 2, message = "类型数据不合法")
    private Integer type;

    /**
     * 最小区间
     */
    @Min(value = 0, message = "最小区间不能小于{value}")
    private Integer minNum;

    /**
     * 最大区间
     */
    @Max(value = 99999999, message = "最大区间不能超过{value}")
    private Integer maxNum;

    /**
     * 图片
     */
    private String img;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 状态文字
     */
    @TableField(exist = false)
    private String statusDesc;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建人时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改人名称
     */
    @TableField(exist = false)
    private String updateName;

    /**
     * 修改人时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
