package com.mrk.yudong.admin.biz.course.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseTagDetailMapper;
import com.mrk.yudong.admin.infrastructure.course.model.CourseTag;
import com.mrk.yudong.admin.infrastructure.course.model.CourseTagDetail;
import com.mrk.yudong.admin.biz.course.service.ICourseTagDetailService;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 课程-课程标签关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-17
 */
@Service
public class CourseTagDetailServiceImpl extends BaseServiceImpl<CourseTagDetailMapper, CourseTagDetail> implements ICourseTagDetailService {

    /**
     * 根据课程ID获取标签信息
     *
     * @param courseId
     * @return
     */
    @Override
    public List<CourseTag> getTagList(Long courseId) {
        return baseMapper.getTagList(courseId);
    }

    /**
     * 根据课程ID获取标签
     *
     * @param courseId
     * @return
     */
    @Override
    public List<String> getTags(Long courseId) {
        if (courseId == null) {
            return null;
        }

        List<CourseTag> tagList = this.getTagList(courseId);
        if (CollUtil.isEmpty(tagList)) {
            return null;
        }

        return tagList.stream().filter(Objects::nonNull).map(CourseTag::getName).collect(Collectors.toList());
    }

}
