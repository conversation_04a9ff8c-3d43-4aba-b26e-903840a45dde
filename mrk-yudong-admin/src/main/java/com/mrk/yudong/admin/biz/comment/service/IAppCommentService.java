package com.mrk.yudong.admin.biz.comment.service;


import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.admin.api.comment.dto.AppCommentQueryDTO;
import com.mrk.yudong.admin.api.comment.vo.AppCommentVO;
import com.mrk.yudong.admin.infrastructure.comment.model.AppComment;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.service.BaseService;

import java.util.List;

/**
 * <p>
 * app评论表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
public interface IAppCommentService extends BaseService<AppComment> {
    PageDTO<AppCommentVO> query(AppCommentQueryDTO appCommentQueryVO);

    List<JSONObject> queryExportDate(AppCommentQueryDTO appCommentQueryVO);
}
