package com.mrk.yudong.admin.security.filter;

import cn.hutool.core.util.StrUtil;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.mrk.yudong.admin.security.handler.UserDetailsHandler;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.utils.JwtUtil;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.core.utils.WebUtil;
import com.mrk.yudong.share.bo.SignInDTO;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.BaseTipConstant;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final UserDetailsHandler userDetailsHandler;

    private final StringRedisTemplate redisTemplate;

    private final JwtUtil jwtUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String requestUri = request.getRequestURI();
        String ip = WebUtil.getIP(request);
        log.warn("============== 请求地址为：{} . 请求IP为：{} ==============", requestUri, ip);

        int isTra = SessionUtil.getIsTra(request.getHeader(BaseConstant.TRA_HEADER_NAME));
        SignInDTO signInDTO = new SignInDTO().setIsTra(isTra);
        String authorization = request.getHeader(HttpHeaders.AUTHORIZATION);
        if (StrUtil.isBlank(authorization)) {
            SessionUtil.set(signInDTO);
            SessionUtil.setId(signInDTO.getUserId());
            filterChain.doFilter(request, response);
            return;
        }

        signInDTO.setUserId(jwtUtil.getId(authorization)).setLogin(jwtUtil.getLogin(authorization)).setUuid(jwtUtil.getUuid(authorization));
        Long userId = signInDTO.getUserId();
        if (userId == null) {
            log.warn("============= 无效Token：{} =============", authorization);
            R.writer(R.fail(HttpStatus.UNAUTHORIZED.value(), BaseTipConstant.REQUEST_UNAUTHORIZED_VOID), response);
            return;
        }

        String statusKey = RedisKeyConstant.SYS_USER_STATUS_KEY.replace("${sysUserId}", userId.toString());
        Boolean hasKey = redisTemplate.hasKey(statusKey);
        if (hasKey != null && hasKey) {
            R.writer(R.fail(HttpStatus.UNAUTHORIZED.value(), BaseTipConstant.REQUEST_FORBIDDEN), response);
            return;
        }

        DecodedJWT decodedJwt = jwtUtil.sysDecodedJwt(authorization);
        assert decodedJwt != null;
        String username = decodedJwt.getClaim("username").asString();
        String nickname = decodedJwt.getClaim("nickname").asString();
        log.warn("====== 操作人员ID为：{}，账号为：{}，昵称为：{} ======", userId, username, nickname);

        UserDetails userDetails = userDetailsHandler.loadUserByUsername(username);
        if (userDetails == null) {
            log.warn("============= 无效Token：{} 可能的原因为：不同环境Token数据，查不到对应账号 =============", authorization);
            R.writer(R.fail(HttpStatus.UNAUTHORIZED.value(), BaseTipConstant.REQUEST_UNAUTHORIZED_VOID), response);
            return;
        }

        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authenticationToken);
        SessionUtil.set(signInDTO);
        SessionUtil.setId(signInDTO.getUserId());
        filterChain.doFilter(request, response);
        SessionUtil.remove();
        SessionUtil.removeId();
    }

}
