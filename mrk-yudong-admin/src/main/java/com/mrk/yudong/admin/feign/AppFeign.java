package com.mrk.yudong.admin.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.share.dto.admin.marketing.activity.*;
import com.mrk.yudong.share.dto.admin.marketing.goods.*;
import com.mrk.yudong.share.dto.app.PrizeRecordOptionDTO;
import com.mrk.yudong.share.dto.app.PrizeRecordQuery;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@FeignClient(name = "yudong-app")
public interface AppFeign {

    /**
     * 活动列表
     *
     * @param activityQueryDTO 活动查询条件
     * @return IPage<ActivityDTO>
     */
    @PostMapping("/activity/setting/list")
    Page<ActivityDTO> list(@RequestBody ActivityQueryDTO activityQueryDTO);

    /**
     * 活动数量统计
     *
     * @param activityQueryDTO 活动查询条件
     * @return List<ActivityNumDTO>
     */
    @PostMapping("/activity/setting/count")
    List<ActivityNumDTO> count(@RequestBody ActivityQueryDTO activityQueryDTO);

    /**
     * 活动详情
     *
     * @param activityId      活动ID
     * @return ActivitySettingDetailDTO
     */
    @GetMapping("/activity/setting/detail")
    ActivitySettingDTO detail(@RequestParam("activityId") Long activityId);

    /**
     * 创建活动
     *
     * @param settingDTO      活动配置数据
     * @return Boolean
     */
    @PostMapping("/activity/setting/create")
    Boolean create(@RequestBody ActivitySettingDTO settingDTO);

    /**
     * 编辑活动
     *
     * @param settingDTO      活动配置数据
     * @return Boolean
     */
    @PostMapping("/activity/setting/edit")
    Boolean edit(@RequestBody ActivitySettingDTO settingDTO);

    /**
     * 删除活动
     *
     * @param activityId      活动ID
     * @return Boolean
     */
    @PostMapping("/activity/setting/delete")
    Boolean delete(@RequestParam("activityId") Long activityId);

    /**
     * 上线活动
     *
     * @param activityId      活动ID
     * @return Boolean
     */
    @PostMapping("/activity/setting/online")
    Boolean online(@RequestParam("activityId") Long activityId);

    /**
     * 下线活动
     *
     * @param activityId      活动ID
     * @param reason          下线原因
     * @return Boolean
     */
    @PostMapping("/activity/setting/offline")
    Boolean offline(@RequestParam("activityId") Long activityId, @RequestParam("reason") String reason);

    @PostMapping("/marketing/goods/create")
    ResDTO<MarketingGoodsDTO> create(@RequestBody CreateMarketingGoodsDTO marketingGoods);

    @PostMapping("/marketing/goods/edit")
    ResDTO<MarketingGoodsDTO> edit(@RequestBody UpdateMarketingGoodsDTO marketingGoods);

    /**
     * 商品详情
     *
     * @param goodsId      商品ID
     * @return MarketingGoodsDetailDTO
     */
    @GetMapping("/marketing/goods/detail")
    ResDTO<MarketingGoodsDetailDTO> goodsDetail(@RequestParam("goodsId") Long goodsId);

    @GetMapping("/marketing/goods/page")
    ResDTO<PageDTO<MarketingGoodsDTO>> page(@SpringQueryMap MarketingGoodsPageQryDTO pageQry);

    @PostMapping("/marketing/goods/publish/{id}")
    ResDTO<Boolean> publish(@PathVariable("id") Long id);

    @PostMapping("/marketing/goods/unPublish/{id}")
    ResDTO<Boolean> unPublish(@PathVariable("id") Long id);

    @PostMapping("/marketing/goods/list")
    ResDTO<List<MarketingGoodsDTO>> findGoodsByIds(@RequestBody List<Long> goodsIds);

    @PostMapping("/activity/setting/prize-record")
    PageDTO<PrizeRecordDTO> prizeRecord(@RequestBody PrizeRecordQuery prizeRecordQuery);

    @GetMapping("/activity/setting/prize-record/option")
    PrizeRecordOptionDTO prizeRecordOption();

    @GetMapping("/activity/setting/prize-record/export")
    ResDTO<List<PrizeRecordDTO>> prizeRecordExport(@SpringQueryMap PrizeRecordQuery prizeRecordQuery);

    @PostMapping("/activity/setting/import-order/{activityId}")
    ResDTO<Boolean> importOrderId(@PathVariable("activityId") Long activityId, @RequestBody Set<String> orderIds);

}
