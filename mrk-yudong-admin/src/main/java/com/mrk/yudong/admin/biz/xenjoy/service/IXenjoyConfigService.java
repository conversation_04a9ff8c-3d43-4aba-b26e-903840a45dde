package com.mrk.yudong.admin.biz.xenjoy.service;

import com.mrk.yudong.admin.api.xenjoy.dto.XenjoyConfigDTO;
import com.mrk.yudong.admin.api.xenjoy.dto.XenjoyOptionDTO;
import com.mrk.yudong.admin.api.xenjoy.query.XenjoyQuery;
import com.mrk.yudong.admin.api.xenjoy.vo.XenjoyConfigVo;
import com.mrk.yudong.admin.infrastructure.xenjoy.model.XenjoyConfig;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.service.BaseService;

/**
 * <p>
 * 绝影会员卡配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-30
 */
public interface IXenjoyConfigService extends BaseService<XenjoyConfig> {

    PageDTO<XenjoyConfigDTO> page(XenjoyQuery query);

    XenjoyOptionDTO option();

    ResDTO<Boolean> save(XenjoyConfigVo xenjoyConfigVo);

    ResDTO<Boolean> update(Long configId, XenjoyConfigVo xenjoyConfigVo);

    ResDTO<Boolean> remove(Long configId);

    ResDTO<Boolean> retryOpen(Long configId);

}
