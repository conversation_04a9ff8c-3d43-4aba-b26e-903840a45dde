package com.mrk.yudong.admin.infrastructure.coursepackage.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mrk.yudong.admin.infrastructure.coursepackage.dao.CourseThemeDetailDao;
import com.mrk.yudong.admin.infrastructure.coursepackage.mapper.CourseThemeDetailMapper;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CourseThemeDetail;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class CourseThemeDetailDaoImpl extends BaseServiceImpl<CourseThemeDetailMapper, CourseThemeDetail> implements CourseThemeDetailDao {
    @Override
    public Boolean batchCreateCourseThemeDetail(List<CourseThemeDetail> courseThemeDetails) {
        if (CollUtil.isEmpty(courseThemeDetails)) {
            return Boolean.FALSE;
        }
        return this.saveBatch(courseThemeDetails);
    }

    @Override
    public Boolean deleteByThemeId(Long themeId) {
        if (ObjectUtil.isNull(themeId)) {
            return Boolean.FALSE;
        }

        LambdaQueryWrapper<CourseThemeDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CourseThemeDetail::getThemeId, themeId);
        return this.remove(queryWrapper);
    }

    @Override
    public List<CourseThemeDetail> findByThemeIds(List<Long> themeIds) {
        if (CollUtil.isEmpty(themeIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CourseThemeDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CourseThemeDetail::getThemeId, themeIds);
        return this.baseMapper.selectList(queryWrapper);
    }
}
