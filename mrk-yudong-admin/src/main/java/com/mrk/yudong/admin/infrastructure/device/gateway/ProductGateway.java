package com.mrk.yudong.admin.infrastructure.device.gateway;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.merach.sun.device.dto.cmd.model.SetModelControlMappingCmd;
import com.merach.sun.device.dto.qry.ProductModelControlMappingQry;
import com.merach.sun.device.dto.resp.model.ModelControlMappingDTO;
import com.mrk.yudong.admin.api.device.dto.cmd.model.CreateBackstageProductModelCmd;
import com.mrk.yudong.admin.api.device.dto.cmd.model.UpdateBackstageProductCmd;
import com.mrk.yudong.admin.api.device.dto.cmd.model.UpdateBackstageProductModelCmd;
import com.mrk.yudong.admin.api.device.dto.cmd.product.ProductSortCmd;
import com.mrk.yudong.admin.api.device.dto.qry.BackstageProductModelQry;
import com.mrk.yudong.admin.api.device.dto.qry.BackstageProductQry;
import com.mrk.yudong.admin.api.device.dto.resp.model.BackstageModelDetailDTO;
import com.mrk.yudong.admin.api.device.dto.resp.model.PageModelDTO;
import com.mrk.yudong.admin.api.device.dto.resp.product.BackstageProductDetailDTO;
import com.mrk.yudong.admin.api.device.dto.resp.product.PageProductDTO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface ProductGateway {

    /**
     * 修改产品信息
     *
     * @param cmd 修改信息
     * @return com.merach.sun.device.dto.resp.product.BackstageProductDetailDTO
     * <AUTHOR>
     * @date 13:39 2022/10/13
     **/
    BackstageProductDetailDTO updateProduct(UpdateBackstageProductCmd cmd) throws Exception;

    /**
     * 查询产品信息详情
     *
     * @param backstageProductQry 产品查询条件
     * @return com.merach.sun.device.dto.resp.product.BackstageProductDetailDTO
     * <AUTHOR>
     * @date 13:46 2022/10/13
     **/
    BackstageProductDetailDTO getProduct(BackstageProductQry backstageProductQry);

    /**
     * 查询产品信息分页
     *
     * @param backstageProductQry 产品查询条件
     * @return com.merach.sun.common.layer.web.PageDTO<com.merach.sun.device.dto.resp.product.BackstageProductDetailDTO>
     * <AUTHOR>
     * @date 13:46 2022/10/13
     **/
    Page<PageProductDTO> pageProducts(BackstageProductQry backstageProductQry);

    /**
     * 产品排序
     *
     * @param productSortCmd
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 14:02 2023/2/17
     **/
    Boolean productSort(@RequestBody List<ProductSortCmd> productSortCmd);

    /**
     * 创建产品型号
     *
     * @param cmd
     * @return com.merach.sun.device.dto.resp.model.ProductModelDetailDTO
     * <AUTHOR>
     * @date 18:53 2022/10/14
     **/
    BackstageModelDetailDTO createProductModel(CreateBackstageProductModelCmd cmd) throws Exception;

    /**
     * 修改产品型号
     *
     * @param cmd
     * @return com.merach.sun.device.dto.resp.model.ProductModelDetailDTO
     * <AUTHOR>
     * @date 18:53 2022/10/14
     **/
    BackstageModelDetailDTO updateProductModel(UpdateBackstageProductModelCmd cmd) throws Exception;

    /**
     * 删除产品型号¬
     *
     * @param id
     * @return com.merach.sun.device.dto.resp.model.ProductModelDetailDTO
     * <AUTHOR>
     * @date 18:53 2022/10/14
     **/
    Boolean deleteProductModel(Long id);

    /**
     * 查询产品型号信息
     *
     * @param productQry
     * @return com.merach.sun.device.dto.resp.model.ProductModelDetailDTO
     * <AUTHOR>
     * @date 18:53 2022/10/14
     **/
    BackstageModelDetailDTO getProductModel(BackstageProductModelQry productQry);

    /**
     * 查询产品型号信息列表
     *
     * @param productQry
     * @return com.merach.sun.device.dto.resp.model.ProductModelDetailDTO
     * <AUTHOR>
     * @date 18:53 2022/10/14
     **/
    List<BackstageModelDetailDTO> listProductModels(BackstageProductModelQry productQry);

    /**
     * 查询产品型号信息分页
     *
     * @param productQry
     * @return com.merach.sun.device.dto.resp.model.ProductModelDetailDTO
     * <AUTHOR>
     * @date 18:53 2022/10/14
     **/
    Page<PageModelDTO> pageProductModels(BackstageProductModelQry productQry);

    List<ModelControlMappingDTO> findModelControlMappings(ProductModelControlMappingQry qry);

    Boolean setModelControlMappings(SetModelControlMappingCmd cmd);
}
