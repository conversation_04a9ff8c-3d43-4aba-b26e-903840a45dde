package com.mrk.yudong.admin.infrastructure.link.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 链路环节表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LinkSection extends Model<LinkSection> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 链路ID
     */
    private Long linkId;

    /**
     * 触发时机ID
     */
    private Long whenId;

    /**
     * 条件ID
     */
    private Long conditionId;

    /**
     * 来源ID
     */
    private Long sourceId;

    /**
     * 目标ID
     */
    private Long targetId;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 创建人
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
