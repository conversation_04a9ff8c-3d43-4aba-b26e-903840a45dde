package com.mrk.yudong.admin.infrastructure.recommend.gateway;

import com.merach.sun.common.layer.web.PageDTO;
import com.merach.sun.data.dto.cmd.recommend.CreateRecommendConfigCmd;
import com.merach.sun.data.dto.cmd.recommend.UpdateRecommendConfigCmd;
import com.merach.sun.data.dto.qry.recommend.RecommendConfigPageQry;
import com.merach.sun.data.dto.qry.recommend.RecommendQry;
import com.merach.sun.data.dto.resp.recommend.RecommendConfigDTO;
import com.merach.sun.data.dto.resp.recommend.RecommendDetailDTO;

import java.util.List;

public interface RecommendGateway {
    RecommendConfigDTO getConfig(Long id);
    Boolean enableConfig(Long id);

    Boolean disableConfig(Long id);

    Boolean deleteConfig(Long id);

    Boolean createConfigs(CreateRecommendConfigCmd cmd);

    RecommendConfigDTO updateConfig(UpdateRecommendConfigCmd cmd);

    PageDTO<RecommendConfigDTO> pageConfigs(RecommendConfigPageQry pageQry);

    List<RecommendDetailDTO> findUserRecommendDetails(RecommendQry qry);
}
