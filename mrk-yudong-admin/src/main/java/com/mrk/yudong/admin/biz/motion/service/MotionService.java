package com.mrk.yudong.admin.biz.motion.service;

import com.mrk.yudong.admin.api.motion.dto.qry.MotionPageQry;
import com.mrk.yudong.admin.biz.motion.bo.CreateMotionBO;
import com.mrk.yudong.admin.biz.motion.bo.MotionBO;
import com.mrk.yudong.admin.biz.motion.bo.UpdateMotionBO;
import com.mrk.yudong.admin.infrastructure.motion.model.Motion;
import com.mrk.yudong.core.model.PageDTO;

public interface MotionService {
    Motion createMotion(CreateMotionBO creation);

    Motion updateMotion(UpdateMotionBO update);

    PageDTO<MotionBO> pageMotions(MotionPageQry qry);

    Boolean publishMotion(Long id);

    Boolean unpublishMotion(Long id);

}
