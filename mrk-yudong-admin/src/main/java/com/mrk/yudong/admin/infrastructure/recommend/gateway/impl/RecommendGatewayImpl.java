package com.mrk.yudong.admin.infrastructure.recommend.gateway.impl;

import com.merach.sun.common.layer.web.PageDTO;
import com.merach.sun.data.api.RecommendApi;
import com.merach.sun.data.api.RecommendConfigApi;
import com.merach.sun.data.dto.cmd.recommend.*;
import com.merach.sun.data.dto.qry.recommend.RecommendConfigPageQry;
import com.merach.sun.data.dto.qry.recommend.RecommendQry;
import com.merach.sun.data.dto.resp.recommend.RecommendConfigDTO;
import com.merach.sun.data.dto.resp.recommend.RecommendDetailDTO;
import com.mrk.yudong.admin.infrastructure.recommend.gateway.RecommendGateway;
import com.mrk.yudong.core.utils.SessionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class RecommendGatewayImpl implements RecommendGateway {
    private final RecommendConfigApi recommendConfigApi;

    private final RecommendApi recommendApi;

    @Override
    public RecommendConfigDTO getConfig(Long id) {
        log.info("RecommendGateway->getConfig->cmd: {}", id);
        try {
            return recommendConfigApi.getConfig(id);
        } catch (Throwable e) {
            log.error("RecommendGateway->getConfig->error, message:{}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Boolean enableConfig(Long id) {
        EnableRecommendConfigCmd cmd = new EnableRecommendConfigCmd();
        cmd.setId(id);
        cmd.setUserId(SessionUtil.getId());
        log.info("RecommendGateway->enableConfig->cmd: {}", cmd);

        try {
            return recommendConfigApi.enableConfig(cmd);
        } catch (Throwable e) {
            log.error("RecommendGateway->enableConfig->error, message:{}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Boolean disableConfig(Long id) {
        DisableRecommendConfigCmd cmd = new DisableRecommendConfigCmd();
        cmd.setId(id);
        cmd.setUserId(SessionUtil.getId());
        log.info("RecommendGateway->disableConfig->cmd: {}", cmd);

        try {
            return recommendConfigApi.disableConfig(cmd);
        } catch (Throwable e) {
            log.error("RecommendGateway->disableConfig->error, message:{}", e.getMessage(), e);
            throw e;
        }

    }

    @Override
    public Boolean deleteConfig(Long id) {
        DeleteRecommendConfigCmd cmd = new DeleteRecommendConfigCmd();
        cmd.setId(id);
        cmd.setUserId(SessionUtil.getId());
        log.info("RecommendGateway->deleteConfig->cmd: {}", cmd);

        try {
            return recommendConfigApi.delete(cmd);
        } catch (Throwable e) {
            log.error("RecommendGateway->deleteConfig->error, message:{}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Boolean createConfigs(CreateRecommendConfigCmd cmd) {
        cmd.setUserId(SessionUtil.getId());
        log.info("RecommendGateway->createConfigs->cmd: {}", cmd);
        try {
            return recommendConfigApi.createConfigs(cmd);
        } catch (Throwable e) {
            log.error("RecommendGateway->deleteConfig->createConfigs, message:{}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public RecommendConfigDTO updateConfig(UpdateRecommendConfigCmd cmd) {
        log.info("RecommendGateway->createConfigs->cmd: {}", cmd);
        try {
            cmd.setUserId(SessionUtil.getId());
            return recommendConfigApi.updateConfig(cmd);
        } catch (Throwable e) {
            log.error("RecommendGateway->deleteConfig->updateConfig, message:{}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public PageDTO<RecommendConfigDTO> pageConfigs(RecommendConfigPageQry pageQry) {
        log.info("RecommendGateway->createConfigs->pageConfigs: {}", pageQry);

        try {
            return recommendConfigApi.pageConfigs(pageQry);
        } catch (Throwable e) {
            log.error("RecommendGateway->deleteConfig->pageConfigs, message:{}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<RecommendDetailDTO> findUserRecommendDetails(RecommendQry qry) {
        log.info("RecommendGateway->findUserRecommendDetails->qry: {}", qry);

        try {
            return recommendApi.findRecommendDetails(qry);
        } catch (Throwable e) {
            log.error("RecommendGateway->findUserRecommendDetails->error, message:{}", e.getMessage(), e);
            throw e;
        }
    }
}
