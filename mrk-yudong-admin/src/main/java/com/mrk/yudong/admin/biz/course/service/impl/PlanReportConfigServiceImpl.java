package com.mrk.yudong.admin.biz.course.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.admin.api.course.query.PlanReportConfigQuery;
import com.mrk.yudong.admin.biz.course.service.IPlanReportConfigService;
import com.mrk.yudong.admin.infrastructure.course.mapper.PlanReportConfigMapper;
import com.mrk.yudong.admin.infrastructure.course.model.PlanReportConfig;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.exception.MyException;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.course.CourseConstant;
import com.mrk.yudong.share.constant.user.TrainConstant;
import com.mrk.yudong.share.util.ImageUtil;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 训练计划报告地标/食物配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Service
public class PlanReportConfigServiceImpl extends BaseServiceImpl<PlanReportConfigMapper, PlanReportConfig> implements IPlanReportConfigService {

    /**
     * plan_report_config字段名称
     */
    private static final String IDENTIFICATION = "identification";
    private static final String NAME = "name";
    private static final String ID = "id";

    @Override
    public PageDTO<PlanReportConfig> query(PlanReportConfigQuery planReportConfigQuery) {
        PageDTO<PlanReportConfig> pageDTO = baseMapper.query(PageDTO.of(planReportConfigQuery.getCurrent(), planReportConfigQuery.getSize()), planReportConfigQuery.getType());
        if (CollUtil.isNotEmpty(pageDTO.getRecords())) {
            pageDTO.getRecords().forEach(planReportConfig -> {
                String equipmentId = planReportConfig.getEquipmentId();
                if (StrUtil.isBlank(equipmentId)) {
                    return;
                }

                List<Long> equipmentIds = StrUtil.split(equipmentId, StrPool.C_COMMA, true, true).stream().map(Long::parseLong).collect(Collectors.toList());
                planReportConfig.setEquipmentIds(equipmentIds);
            });
        }
        return pageDTO;
    }

    /**
     * 获取数量
     *
     * @return
     */
    @Override
    public Map<Integer, Integer> getNum() {
        List<JSONObject> mapList = baseMapper.getNum();
        if (CollUtil.isEmpty(mapList)) {
            Map<Integer, Integer> data = new HashMap<>(2);
            data.put(CourseConstant.PLAN_CONFIG_TYPE_1, 0);
            data.put(CourseConstant.PLAN_CONFIG_TYPE_2, 0);
            return data;
        }

        Map<Integer, Integer> data = mapList.stream().collect(Collectors.toMap(dict -> MapUtil.getInt(dict, "type"), dict -> MapUtil.getInt(dict, "num")));
        if (!data.containsKey(CourseConstant.PLAN_CONFIG_TYPE_1)) {
            data.put(CourseConstant.PLAN_CONFIG_TYPE_1, 0);
        }
        if (!data.containsKey(CourseConstant.PLAN_CONFIG_TYPE_2)) {
            data.put(CourseConstant.PLAN_CONFIG_TYPE_2, 0);
        }
        return data;
    }

    /**
     * 【新增记录】地标/食物配置信息
     * @param planReportConfig 训练计划报告配置实体类
     */
    @Override
    public PlanReportConfig createPlanReportConfig(PlanReportConfig planReportConfig) {

        verifyParam(planReportConfig);

        planReportConfig.setId(null);
        planReportConfig.setStatus(BaseConstant.INT_FALSE);
        List<Long> equipmentIds = planReportConfig.getEquipmentIds();
        if (CollUtil.isNotEmpty(equipmentIds)) {
            String equipmentId = String.join(StrPool.COMMA, equipmentIds.stream().map(Object::toString).collect(Collectors.toList()));
            planReportConfig.setEquipmentId(equipmentId);
        }
        this.save(planReportConfig);
        return planReportConfig;
    }

    /**
     * 【修改记录】地标/食物配置信息
     */
    @Override
    public PlanReportConfig updatePlanReportConfig(PlanReportConfig planReportConfig) {

        if (planReportConfig.getId() == null || !isExist(ID, ConditionEnum.EQ, planReportConfig.getId())) {
            log.warn(String.format("Failed to modify data, Invalid id: '%s'", planReportConfig.getId()));
            throw new MyException(401, "无效的配置ID");
        }

        verifyParam(planReportConfig);

        planReportConfig.setStatus(null);
        List<Long> equipmentIds = planReportConfig.getEquipmentIds();
        if (CollUtil.isNotEmpty(equipmentIds)) {
            String equipmentId = String.join(",", equipmentIds.stream().map(Object::toString).collect(Collectors.toList()));
            planReportConfig.setEquipmentId(equipmentId);
        }

        this.updateById(planReportConfig);
        return planReportConfig;
    }

    /**
     * 配置入参校验
     * @param planReportConfig 训练计划报告配置实体类
     */
    private void verifyParam(PlanReportConfig planReportConfig) {

        //1. 城市
        if (Objects.equals(planReportConfig.getType(), PlanReportConfig.CONFIG_TYPE_CITY)){

            //地标名称唯一性校验
            if (!isOnly(planReportConfig.getId(), IDENTIFICATION, planReportConfig.getIdentification())){
                log.warn(String.format("param verify failed, '%s' = '%s' is not unique", IDENTIFICATION, planReportConfig.getIdentification()));
                throw new MyException(401, "该地标名称已经被使用");
            }

            //区间规范校验
            Integer minNum = planReportConfig.getMinNum();
            Integer maxNum = planReportConfig.getMaxNum();
            if (minNum == null || maxNum == null || minNum > maxNum) {
                log.warn(String.format("param verify failed, 区间范围不能为空且最小区间不能大于最大区间, minNum: %s, maxNum: %s", minNum, maxNum));
                throw new MyException(401, "区间范围不能为空且最小区间不能大于最大区间");
            }

            //是否绑定设备校验
            List<Long> equipmentIds = planReportConfig.getEquipmentIds();
            if (CollUtil.isEmpty(equipmentIds)) {
                log.warn("param verify failed, device id is null, 请选择绑定设备");
                throw new MyException(401, "请选择绑定设备");
            }

            //设备违规校验
            if (equipmentIds.stream().anyMatch(id -> !TrainConstant.LARGES.contains(id))) {
                log.warn(String.format("param verify failed, 存在不合法的设备数据, devices list:[%s]", equipmentIds));
                throw new MyException(401, "存在不合法的设备数据");
            }
        }

        //2. 食物
        else if (Objects.equals(planReportConfig.getType(), PlanReportConfig.CONFIG_TYPE_FOOD)) {

            //食物名称唯一性校验
            if (!isOnly(planReportConfig.getId(), NAME, planReportConfig.getName())){
                log.warn(String.format("param verify failed, '%s' = '%s' is not unique", NAME, planReportConfig.getName()));
                throw new MyException(401, "该食物名称已经被使用");
            }
        }

        //图片校验
        if (StrUtil.isBlank(planReportConfig.getImg())) {
            log.warn("param verify failed, Uploaded image is empty");
            throw new MyException(401, "请上传图片");
        }
        if (ImageUtil.isNotImage(planReportConfig.getImg())) {
            log.warn("param verify failed, incorrect image format");
            throw new MyException(401, "不被支持的图片格式, 请重新上传图片");
        }
    }

}
