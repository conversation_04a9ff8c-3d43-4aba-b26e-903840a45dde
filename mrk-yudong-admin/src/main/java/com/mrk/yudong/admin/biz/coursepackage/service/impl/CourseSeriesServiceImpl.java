package com.mrk.yudong.admin.biz.coursepackage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.merach.sun.common.layer.web.PageForm;
import com.mrk.yudong.admin.api.coursepackage.dto.series.cmd.CreateCourseSeriesCmd;
import com.mrk.yudong.admin.api.coursepackage.dto.series.cmd.UpdateCourseSeriesCmd;
import com.mrk.yudong.admin.api.coursepackage.dto.series.resp.CourseSeriesDTO;
import com.mrk.yudong.admin.biz.sys.service.ISysUserService;
import com.mrk.yudong.admin.biz.coursepackage.enums.SeriesStatusEnum;
import com.mrk.yudong.admin.biz.coursepackage.service.ICourseSeriesService;
import com.mrk.yudong.admin.infrastructure.coursepackage.mapper.CourseSeriesMapper;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CourseSeries;
import com.mrk.yudong.admin.infrastructure.sys.model.SysUser;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CourseSeriesServiceImpl extends BaseServiceImpl<CourseSeriesMapper, CourseSeries> implements ICourseSeriesService {

    private final ISysUserService sysUserService;

    @Override
    public Boolean createCourseSeries(CreateCourseSeriesCmd courseSeries) {
        CourseSeries courseSeriesDo = BeanUtil.copyProperties(courseSeries, CourseSeries.class);
        courseSeriesDo.setUpdateId(SessionUtil.getId());
        courseSeriesDo.setCreateId(SessionUtil.getId());
        courseSeriesDo.setUpdateTime(LocalDateTime.now());
        return save(courseSeriesDo);
    }

    @Override
    public CourseSeriesDTO getCourseSeries(Long id) {
        return BeanUtil.copyProperties(getById(id), CourseSeriesDTO.class);
    }

    @Override
    public Boolean updateCourseSeries(UpdateCourseSeriesCmd courseSeries) {
        CourseSeries courseSeriesDo = BeanUtil.copyProperties(courseSeries, CourseSeries.class);
        courseSeriesDo.setUpdateId(SessionUtil.getId());
        return updateById(courseSeriesDo);
    }

    @Override
    public Boolean updateCourseSeriesStatus(Long id, Integer status) {
        CourseSeries courseSeriesDo = new CourseSeries();
        courseSeriesDo.setUpdateId(SessionUtil.getId());
        courseSeriesDo.setId(id);
        courseSeriesDo.setStatus(status);
        return updateById(courseSeriesDo);
    }

    @Override
    public Boolean deleteCourseSeries(Long id) {
        return removeById(id);
    }

    @Override
    public Page<CourseSeriesDTO> pageCourseSeries(PageForm pageForm) {
        Page<CourseSeries> page = page(new Page<>(pageForm.getCurrent(), pageForm.getSize()), new LambdaQueryWrapper<>(new CourseSeries()).eq(CourseSeries::getIsDelete, 0));
        if (CollUtil.isEmpty(page.getRecords())) {
            return new Page<>();
        }
        Map<Long, String> userMap = sysUserService.listByIds(page.getRecords().stream().map(CourseSeries::getUpdateId).collect(Collectors.toList())).stream().collect(Collectors.toMap(SysUser::getId, SysUser::getNickName));
        Page<CourseSeriesDTO> respIPage = new Page<>(pageForm.getCurrent(), pageForm.getSize(), page.getTotal());
        respIPage.setRecords(page.getRecords().stream().map(sales -> {
            CourseSeriesDTO courseSeriesDTO = BeanUtil.copyProperties(sales, CourseSeriesDTO.class);
            if (MapUtil.isNotEmpty(userMap) && sales.getUpdateId() != null) {
                String name = MapUtil.getStr(userMap, sales.getUpdateId());
                if (StrUtil.isNotBlank(name)) {
                    courseSeriesDTO.setOperatorName(name);
                }
            }
            courseSeriesDTO.setStatusName(SeriesStatusEnum.fromCode(sales.getStatus()).getDesc());
            courseSeriesDTO.setOperationTime(sales.getUpdateTime());
            return courseSeriesDTO;
        }).collect(Collectors.toList()));
        return respIPage;
    }

}
