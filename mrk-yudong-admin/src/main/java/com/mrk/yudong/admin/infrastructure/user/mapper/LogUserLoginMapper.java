package com.mrk.yudong.admin.infrastructure.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.infrastructure.user.model.LogUserLogin;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 用户登录日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
public interface LogUserLoginMapper extends BaseMapper<LogUserLogin> {

    /**
     * 查询用户登录日志
     *
     * @param page
     * @param userId
     * @return
     */
    IPage<LogUserLogin> query(Page<LogUserLogin> page, @Param("userId") String userId);

    /**
     * 根据日期获取登录人数
     *
     * @param beginDate
     * @param endDate
     * @return
     */
    int countDate(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 获取最新登录记录
     *
     * @param userId 用户ID
     * @return LogUserLogin
     * <AUTHOR>
     * @date 2022/12/7 16:23
     */
    @Select("SELECT * FROM log_user_login WHERE user_id = #{userId} ORDER BY create_time DESC LIMIT 1")
    LogUserLogin getTopLoginData(@Param("userId") Long userId);

}
