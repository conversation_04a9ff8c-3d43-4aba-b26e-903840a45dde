package com.mrk.yudong.admin.infrastructure.motion.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.api.motion.dto.qry.MotionPageQry;
import com.mrk.yudong.admin.infrastructure.motion.model.Motion;

public interface MotionDao {
    Motion getById(Long id);

    Motion getByFollowVideoId(Long followVideoId);

    Motion getByIntroduceVideoId(Long introduceId);

    IPage<Motion> pageMotions(MotionPageQry pageQry);

    Motion update(Motion motion);

    Motion create(Motion motion);

    Motion getByName(String name);
}
