package com.mrk.yudong.admin.infrastructure.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.infrastructure.user.model.ExchangeOrderConfig;
import com.mrk.yudong.admin.api.user.vo.ExchangeCodeConfigQueryVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 兑换订单配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-30
 */
public interface ExchangeOrderConfigMapper extends BaseMapper<ExchangeOrderConfig> {

    /**
     * 查询配置列表
     *
     * @param page
     * @param exchangeCodeConfigQueryVO
     * @return
     */
    IPage<ExchangeOrderConfig> query(Page<ExchangeOrderConfig> page, @Param("exchangeCodeConfigQueryVO") ExchangeCodeConfigQueryVO exchangeCodeConfigQueryVO);

    /**
     * 课程详情
     *
     * @param id
     * @return
     */
    ExchangeOrderConfig detail(@Param("id") Long id);

}
