package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.LinkedHashSet;

/**
 * <p>
 * 课程热词表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
@Data
@JsonIgnoreProperties(value = {"createId", "createTime", "updateId", "updateTime"})
@EqualsAndHashCode(callSuper = false)
public class CourseHotword extends Model<CourseHotword> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "热词名称不能为空")
    @Length(max = 32, message = "热词名称长度不能超过32位")
    private String name;

    /**
     * 设备ID，JSON数组
     */
    private String equipmentId;

    /**
     * 设备ID
     */
    @Size(min = 1, message = "请选择所属设备")
    @TableField(exist = false)
    private LinkedHashSet<Long> equipmentIds;

    /**
     * 设备名称
     */
    @TableField(exist = false)
    private String equipmentName;

    /**
     * 阶段，JSON数组
     */
    private String stage;

    /**
     * 阶段
     */
    @Size(min = 1, message = "请选择所属阶段")
    @TableField(exist = false)
    private LinkedHashSet<Integer> stages;

    /**
     * 阶段
     */
    @TableField(exist = false)
    private String stageName;

    /**
     * 是否为预发数据：0-否，1-是
     */
    private Integer isTra;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改人
     */
    @TableField(exist = false)
    private String updateName;

    /**
     * 最近操作时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(exist = false)
    private LocalDateTime time;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
