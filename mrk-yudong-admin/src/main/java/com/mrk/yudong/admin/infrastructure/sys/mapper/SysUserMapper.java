package com.mrk.yudong.admin.infrastructure.sys.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.infrastructure.sys.model.SysUser;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 后台用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-17
 */
public interface SysUserMapper extends BaseMapper<SysUser> {

    IPage<SysUser> query(Page<SysUser> page, @Param("param") Map<String, Object> param);

}
