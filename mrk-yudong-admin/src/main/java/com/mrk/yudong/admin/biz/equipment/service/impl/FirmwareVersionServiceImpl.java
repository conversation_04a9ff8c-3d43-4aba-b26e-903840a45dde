package com.mrk.yudong.admin.biz.equipment.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.biz.equipment.service.IFirmwareVersionService;
import com.mrk.yudong.admin.infrastructure.equipment.mapper.FirmwareVersionMapper;
import com.mrk.yudong.admin.infrastructure.equipment.model.FirmwareVersion;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 固件版本管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FirmwareVersionServiceImpl extends BaseServiceImpl<FirmwareVersionMapper, FirmwareVersion> implements IFirmwareVersionService {

    @Override
    public IPage<FirmwareVersion> getPage(IPage<FirmwareVersion> page, FirmwareVersion firmwareVersion) {
        Integer isTra = SessionUtil.getIsTra();
        return baseMapper.getPage(page, firmwareVersion.getEquipTypeId(), firmwareVersion.getOtaType(), isTra);
    }




}
