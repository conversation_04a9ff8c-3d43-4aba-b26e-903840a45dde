package com.mrk.yudong.admin.biz.comment.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.mrk.yudong.admin.api.comment.dto.AppCommentQueryDTO;
import com.mrk.yudong.admin.api.comment.vo.AppCommentVO;
import com.mrk.yudong.admin.biz.comment.service.IAppCommentService;
import com.mrk.yudong.admin.biz.comment.service.enums.AppCommnetTypeEnum;
import com.mrk.yudong.admin.infrastructure.comment.mapper.AppCommentMapper;
import com.mrk.yudong.admin.infrastructure.comment.model.AppComment;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.service.BaseServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * app评论表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
@Service
@RequiredArgsConstructor
public class AppCommentServiceImpl extends BaseServiceImpl<AppCommentMapper, AppComment> implements IAppCommentService {

    /**
     * 查詢用户评论
     * @param app
     */
    public PageDTO<AppCommentVO> query(AppCommentQueryDTO app) {
        //设置分页参数
        PageDTO<AppComment> page = getPageDTO(app);
        // 转化数据
        List<AppCommentVO> list = new ArrayList<>();
        page.getRecords().forEach(v -> {
            AppCommentVO response = new AppCommentVO();
            BeanUtil.copyProperties(v, response);
            response.setType(AppCommnetTypeEnum.getStrAppCommetType(v.getType()));
            list.add(response);
        });
        //返回
        PageDTO pageDTO = new PageDTO();
        BeanUtil.copyProperties(page, pageDTO);
        pageDTO.setRecords(list);
        return pageDTO;
    }

    /**
     * 分页查询用户评论信息
     * @param app
     * @return
     */
    public PageDTO<AppComment> getPageDTO(AppCommentQueryDTO app) {
        PageDTO<AppComment> appPage = new PageDTO<>(app.getCurrent(), app.getSize());
        QueryWrapper queryWrapper =  new QueryWrapper();
        if (app.getType() != null){
            queryWrapper.eq("type", app.getType());
        }
        if (!StringUtils.isEmpty(app.getMobile())){
            queryWrapper.eq("mobile", app.getMobile());
        }
        if (!StringUtils.isEmpty(app.getVersion())){
            queryWrapper.eq("version", app.getVersion());
        }
        if (!ObjectUtils.isEmpty(app.getStartTime())
                && !ObjectUtils.isEmpty(app.getEndTime())){
            queryWrapper.ge("create_time", app.getStartTime());
            queryWrapper.le("create_time", app.getEndTime());
        }
        return this.baseMapper.selectPage(appPage, queryWrapper);
    }

    /**
     * 查询导出数据
     * @param appCommentQueryVO
     * @return
     */
    @Override
    public List<JSONObject> queryExportDate(AppCommentQueryDTO appCommentQueryVO) {
        PageDTO<AppCommentVO> query = query(appCommentQueryVO);
        if (ObjectUtils.isEmpty(query)){
            return new ArrayList<>();
        }
        List<AppCommentVO> records = query.getRecords();
        return BeanUtil.copyToList(records, JSONObject.class);
    }

}
