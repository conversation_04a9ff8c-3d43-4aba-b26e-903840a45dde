package com.mrk.yudong.admin.infrastructure.device.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 其他设备信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@JsonIgnoreProperties(value = {"status", "createId", "createTime", "updateId", "updateTime"})
@Data
@EqualsAndHashCode(callSuper = false)
public class EquipmentOther extends Model<EquipmentOther> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 设备分类ID
     */
    @NotNull(message = "请选择设备分类")
    private Long categoryId;

    /**
     * 显示名称
     */
    @NotBlank(message = "名称不能为空")
    @Length(max = 32, message = "名称长度不能超过32位")
    private String name;

    /**
     * 显示封面
     */
    @NotBlank(message = "请上传封面")
    @Length(max = 128, message = "图片地址不合法")
    private String cover;

    /**
     * 型号编码
     */
    @NotBlank(message = "设备型号不能为空")
    @Length(max = 32, message = "设备型号长度不能超过32位")
    private String modelCode;

    /**
     * 蓝牙传播编码
     */
    @NotBlank(message = "蓝牙传播编码不能为空")
    @Length(max = 32, message = "蓝牙传播编码长度不能超过32位")
    private String bluetoothCode;

    /**
     * 是否支持连接APP：0-否，1-是
     */
    @NotNull(message = "请选择是否支持连接APP")
    @Range(min = 0, max = 1, message = "是否支持连接APP数据不合法")
    private Integer isLinkApp;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
