package com.mrk.yudong.admin.infrastructure.producttest.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * <p>
 * 参数型号关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TestModelParamsAssocited extends Model<TestModelParamsAssocited> {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 功能参数
     */
    private Long functionParamsId;

    /**
     * 设备参数
     */
    private Long equipParamsId;

    /**
     * 型号id
     */
    private Long modelId;

    /**
     *参数名称
     */
    @TableField(exist = false)
    private String paramsName;
    /**
     * 设备参数值
     */
    @Length(max = 20, message = "设备参数配置只不能超过20个长度")
    private String equipValue;
    /**
     * 设备默认值
     */
    @TableField(exist = false)
    private String defaultValue;
    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
