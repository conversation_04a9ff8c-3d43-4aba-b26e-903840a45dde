package com.mrk.yudong.admin.utils;

import cn.hutool.core.util.StrUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.mrk.yudong.admin.biz.sys.service.ISysRoleDepartmentService;
import com.mrk.yudong.admin.biz.sys.service.ISysUserService;
import com.mrk.yudong.admin.constant.URLConstant;
import com.mrk.yudong.core.utils.RedisUtil;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.taobao.api.ApiException;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 获取钉钉的token
 * <AUTHOR>
 * @create 2021−05-27 1:37 下午
 */
@Component
@RequiredArgsConstructor
public class DingTlakUtil {
    @Value("${mrk.dingtalk.appKey}")
    private  String appKey;
    @Value("${mrk.dingtalk.appSecret}")
    private  String appSecret;

    private static final Logger bizLogger = LoggerFactory.getLogger(DingTlakUtil.class);


     final RedisUtil redisUtil;
     final ISysUserService sysUserService;

     final ISysRoleDepartmentService sysRoleDepartmentService;

    /**
     * 获取企业内部应用的access_token
     * @return access_token
     * @throws RuntimeException
     */
    public  String getToken() throws RuntimeException {
        try {
            String accessToken = redisUtil.getCacheObject(RedisKeyConstant.DING_TALK_ACCESS_TOKEN);
            if(StrUtil.isBlank(accessToken)) {
                DefaultDingTalkClient client = new DefaultDingTalkClient(URLConstant.URL_GET_TOKKEN);
                OapiGettokenRequest request = new OapiGettokenRequest();
                request.setAppkey(appKey);
                request.setAppsecret(appSecret);
                request.setHttpMethod("GET");
                OapiGettokenResponse response = client.execute(request);
                accessToken = response.getAccessToken();
                redisUtil.setCacheObject(RedisKeyConstant.DING_TALK_ACCESS_TOKEN, accessToken, 2L, TimeUnit.HOURS);
                return accessToken;
            }
            return accessToken;
        } catch (ApiException e) {
            bizLogger.warn("getAccessToken failed", e);
            throw new RuntimeException();
        }
    }

}
