package com.mrk.yudong.admin.infrastructure.user.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.enums.training.TrainingRecordStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 训练明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TrainDetail extends Model<TrainDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 训练类型：1-课程训练，2-自由训练
     */
    private Integer type;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 设备信息ID
     */
    private Long equipmentId;

    /**
     * 型号ID
     */
    private Long modelId;

    /**
     * 用户计划ID
     */
    private Long planUserId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 耗时：秒
     */
    private Long takeTime;

    /**
     * 消耗
     */
    private Double kcal;

    /**
     * 心率带卡路里
     */
    private Double rateKcal;

    /**
     * 距离
     */
    private Double distance;

    /**
     * 速度
     */
    private Double speed;

    /**
     * 总数量
     */
    private Double num;

    /**
     * 频率
     */
    private Double rate;

    /**
     * 最高阻力
     */
    private Double drag;

    /**
     * 最高坡度
     */
    private Double slope;

    /**
     * 心率
     */
    private Double rat;

    /**
     * 最高踏数
     */
    private Double maxSpeed;

    /**
     * 平均踏频
     */
    private Double avgRate;

    /**
     * 功率
     */
    private Double power;

    /**
     * 是否为心率带训练：0-否，1-是
     */
    private Integer isRate;

    /**
     * 心率带设备ID
     */
    private Long rateId;

    /**
     * 播放时长：秒
     */
    private Long playTime;

    @TableLogic
    private Integer isDelete;


    /**
     * 状态
     * @see TrainingRecordStatusEnum
     */
    private Integer status;

    /**
     * 创建时间（结算时间）
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime startTime;

    /**
     * 训练标题
     */
    private String title;

    /**
     * 训练模式
     */
    private Integer trainType;


    /**
     * 设备信息id(equ_equipment_info主键)
     */
    private Long equipmentInfoId;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
