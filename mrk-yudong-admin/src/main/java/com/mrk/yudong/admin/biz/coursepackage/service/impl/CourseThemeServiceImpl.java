package com.mrk.yudong.admin.biz.coursepackage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mrk.yudong.admin.api.coursepackage.dto.theme.cmd.CreateCourseThemeCmd;
import com.mrk.yudong.admin.api.coursepackage.dto.theme.cmd.UpdateCourseThemeCmd;
import com.mrk.yudong.admin.api.coursepackage.dto.theme.qry.CourseThemePageQuery;
import com.mrk.yudong.admin.biz.coursepackage.bo.CourseThemeBO;
import com.mrk.yudong.admin.biz.coursepackage.bo.CourseThemeCourseBO;
import com.mrk.yudong.admin.biz.coursepackage.enums.CourseThemeStatusEnum;
import com.mrk.yudong.admin.biz.coursepackage.service.ICourseThemeService;
import com.mrk.yudong.admin.biz.linestatusrecord.service.LineStatusRecordService;
import com.mrk.yudong.admin.infrastructure.coursepackage.dao.CourseThemeDetailDao;
import com.mrk.yudong.admin.infrastructure.coursepackage.dao.CourseThemeTagDao;
import com.mrk.yudong.admin.infrastructure.coursepackage.mapper.CourseThemeMapper;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CourseTheme;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CourseThemeDetail;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CourseThemeTagRel;
import com.mrk.yudong.admin.infrastructure.linestatus.model.LineStatusRecord;
import com.mrk.yudong.core.exception.MyException;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.share.enums.linestatus.LineStatusBusinessEnum;
import com.mrk.yudong.share.enums.linestatus.LineStatusEnum;
import com.mrk.yudong.share.util.ImageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 课程主题表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class CourseThemeServiceImpl extends BaseServiceImpl<CourseThemeMapper, CourseTheme> implements ICourseThemeService {
    private final CourseThemeTagDao courseThemeTagDao;
    private final CourseThemeDetailDao courseThemeDetailDao;
    private final StringRedisTemplate redisTemplate;
    private final LineStatusRecordService lineStatusRecordService;

    @Override
    public PageDTO<CourseThemeBO> pageThemes(CourseThemePageQuery courseThemePageQuery) {
        LambdaQueryWrapper<CourseTheme> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotNull(courseThemePageQuery.getEquipmentId()), CourseTheme::getEquipmentId, courseThemePageQuery.getEquipmentId());
        queryWrapper.eq(ObjectUtil.isNotNull(courseThemePageQuery.getDisplayLocation()), CourseTheme::getDisplayLocation, courseThemePageQuery.getDisplayLocation());
        queryWrapper.eq(ObjectUtil.isNotNull(courseThemePageQuery.getId()), CourseTheme::getId, courseThemePageQuery.getId());
        queryWrapper.likeRight(ObjectUtil.isNotNull(courseThemePageQuery.getName()), CourseTheme::getName, courseThemePageQuery.getName());
        queryWrapper.orderByDesc(CourseTheme::getSort).orderByDesc(CourseTheme::getUpdateTime);
        convertThemeStatusCondition(courseThemePageQuery, queryWrapper);

        PageDTO<CourseTheme> searchResult = this.baseMapper.selectPage(PageDTO.of(courseThemePageQuery.getCurrent(), courseThemePageQuery.getSize()), queryWrapper);
        PageDTO<CourseThemeBO> result = new PageDTO<>();
        result.setCurrent(courseThemePageQuery.getCurrent());
        result.setSize(courseThemePageQuery.getSize());
        result.setTotal(searchResult.getTotal());
        if (ObjectUtil.isNull(searchResult) || CollUtil.isEmpty(searchResult.getRecords())) {
            return result;

        }
        List<CourseThemeBO> courseThemes = BeanUtil.copyToList(searchResult.getRecords(), CourseThemeBO.class);
        List<Long> themeIds = courseThemes.stream().map(CourseThemeBO::getId).collect(Collectors.toList());
        List<CourseThemeTagRel> tagRels = courseThemeTagDao.findByThemeIds(themeIds);
        Map<Long, List<CourseThemeTagRel>> tagRelsPerTheme = tagRels.stream().collect(Collectors.groupingBy(CourseThemeTagRel::getThemeId));
        List<CourseThemeDetail> courseThemeDetails = courseThemeDetailDao.findByThemeIds(themeIds);
        Map<Long, List<CourseThemeDetail>> tagDetailsPerTheme = courseThemeDetails.stream().collect(Collectors.groupingBy(CourseThemeDetail::getThemeId));

        for (CourseThemeBO courseTheme : courseThemes) {
            if (tagRelsPerTheme.containsKey(courseTheme.getId())) {
                courseTheme.setTagIds(tagRelsPerTheme.get(courseTheme.getId()).stream().map(CourseThemeTagRel::getTagId).collect(Collectors.toList()));
            }
            if (tagDetailsPerTheme.containsKey(courseTheme.getId())) {
                List<CourseThemeDetail> currentCourseThemeDetail = tagDetailsPerTheme.get(courseTheme.getId());
                courseTheme.setCourseIds(currentCourseThemeDetail.stream().map(CourseThemeDetail::getCourseId).collect(Collectors.toList()));
                courseTheme.setCourses(BeanUtil.copyToList(currentCourseThemeDetail, CourseThemeCourseBO.class));
            }


        }
        result.setRecords(courseThemes);

        return result;
    }

    @Override
    @Transactional
    public Boolean createCourseTheme(CreateCourseThemeCmd cmd) {
        CourseTheme creation = BeanUtil.copyProperties(cmd, CourseTheme.class);
        creation.setType(1);
        creation.setDisplayForm(2);
        creation.setUseType(1);
        validateSaveCourseTheme(creation);
        this.save(creation);
        List<CourseThemeDetail> themeDetails = cmd.getCourses().stream()
                .map(course -> new CourseThemeDetail(
                        creation.getId(),
                        course.getCourseId(),
                        course.getSeq()
                ))
                .collect(Collectors.toList());
        courseThemeDetailDao.batchCreateCourseThemeDetail(themeDetails);
        if (CollUtil.isNotEmpty(cmd.getTagIds())) {
            List<CourseThemeTagRel> themeTags = cmd.getTagIds().stream()
                    .map(tagId -> new CourseThemeTagRel(creation.getId(), tagId))
                    .collect(Collectors.toList());
            courseThemeTagDao.batchCreateCourseThemeTag(themeTags);
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional
    public Boolean updateCourseTheme(UpdateCourseThemeCmd cmd) {
        CourseTheme existCourseTheme = tryGetTheme(cmd.getId());
        courseThemeDetailDao.deleteByThemeId(cmd.getId());
        courseThemeTagDao.deleteByThemeId(cmd.getId());
        BeanUtil.copyProperties(cmd, existCourseTheme);

        List<CourseThemeDetail> themeDetails = cmd.getCourses().stream()
                .map(course -> new CourseThemeDetail(
                        cmd.getId(),
                        course.getCourseId(),
                        course.getSeq()
                ))
                .collect(Collectors.toList());
        courseThemeDetailDao.batchCreateCourseThemeDetail(themeDetails);
        if (CollUtil.isNotEmpty(cmd.getTagIds())) {
            List<CourseThemeTagRel> themeTags = cmd.getTagIds().stream()
                    .map(tagId -> new CourseThemeTagRel(cmd.getId(), tagId))
                    .collect(Collectors.toList());
            courseThemeTagDao.batchCreateCourseThemeTag(themeTags);
        }
        this.updateById(existCourseTheme);
        return Boolean.TRUE;
    }

    private void convertThemeStatusCondition(CourseThemePageQuery courseThemePageQuery, LambdaQueryWrapper<CourseTheme> queryWrapper) {
        if (ObjectUtil.isNull(courseThemePageQuery.getDisplayStatus())) {
            return;
        }

        // 查询类型：1-未上线，2-进行中，3-已下线
        String nowDateTime = LocalDateTimeUtil.format(LocalDateTime.now(), BaseConstant.DATE_TIME_FORMAT);
        switch (courseThemePageQuery.getDisplayStatus()) {
            case 1:
                queryWrapper.eq(CourseTheme::getStatus, BaseConstant.INT_TRUE).gt(CourseTheme::getBeginTime, nowDateTime);
                break;
            case 2:
                queryWrapper.eq(CourseTheme::getStatus, BaseConstant.INT_TRUE).le(CourseTheme::getBeginTime, nowDateTime)
                        .and(i -> i.eq(CourseTheme::getIsForever, BaseConstant.INT_TRUE).or().ge(CourseTheme::getEndTime, nowDateTime));
                break;
            case 3:
                queryWrapper.and(i -> i.eq(CourseTheme::getStatus, BaseConstant.INT_FALSE)
                        .or(o -> o.and(q -> q.eq(CourseTheme::getIsForever, BaseConstant.INT_FALSE).lt(CourseTheme::getEndTime, nowDateTime))));
                break;
        }

    }

    /**
     * 课程主题详情
     *
     * @param themeId 主题ID
     * @return com.mrk.yudong.core.model.R
     * <AUTHOR>
     * @date 2022/2/15 17:40
     */
    @Override
    public CourseThemeBO detail(Long themeId) {
        CourseTheme existCourseTheme = tryGetTheme(themeId);
        CourseThemeBO courseThemeBO = BeanUtil.copyProperties(existCourseTheme, CourseThemeBO.class);
        List<Long> themeIds = CollUtil.newArrayList(courseThemeBO.getId());
        List<CourseThemeTagRel> tagRels = courseThemeTagDao.findByThemeIds(themeIds);
        if (CollUtil.isNotEmpty(tagRels)) {
            courseThemeBO.setTagIds(tagRels.stream().map(CourseThemeTagRel::getTagId).collect(Collectors.toList()));
        }
        List<CourseThemeDetail> courseThemeDetails = courseThemeDetailDao.findByThemeIds(themeIds);
        if (CollUtil.isNotEmpty(courseThemeDetails)) {
            courseThemeBO.setCourseIds(courseThemeDetails.stream().map(CourseThemeDetail::getCourseId).collect(Collectors.toList()));
            courseThemeBO.setCourses(BeanUtil.copyToList(courseThemeDetails, CourseThemeCourseBO.class));
        }

        return courseThemeBO;
    }

    /**
     * 删除课程主题
     *
     * @param themeId 主题ID
     * @return com.mrk.yudong.core.model.R
     * <AUTHOR>
     * @date 2022/2/15 17:36
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean remove(Long themeId) {
        CourseTheme existTheme = tryGetTheme(themeId);
        validateDeleteTheme(existTheme);

        boolean remove = this.removeById(themeId);
        if (remove) {
            courseThemeDetailDao.deleteByThemeId(themeId);
            courseThemeTagDao.deleteByThemeId(themeId);
            String key = RedisKeyConstant.THEME_COURSE_KEY.replace("${themeId}", themeId.toString());
            String numKey = RedisKeyConstant.THEME_COURSE_NUM_KEY.replace("${themeId}", themeId.toString());
            redisTemplate.delete(ListUtil.of(key, numKey));
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    public Boolean onlineCourseTheme(Long id, Long userId) {
        CourseTheme existTheme = tryGetTheme(id);
        if (existTheme.getStatus().equals(CourseThemeStatusEnum.ONLINE.getStatus())) {
            return Boolean.TRUE;
        }
        existTheme.setStatus(CourseThemeStatusEnum.ONLINE.getStatus());
        existTheme.setUpdateId(userId);
        Boolean result = this.baseMapper.updateById(existTheme) > 0 ? Boolean.TRUE : Boolean.FALSE;
        if (result) {
            saveLineStatus(id, CourseThemeStatusEnum.ONLINE.getStatus());
        }
        return result;
    }

    @Override
    public Boolean offlineCourseTheme(Long id, Long userId) {
        CourseTheme existTheme = tryGetTheme(id);
        if (existTheme.getStatus().equals(CourseThemeStatusEnum.OFFLINE.getStatus())) {
            return Boolean.TRUE;
        }
        existTheme.setStatus(CourseThemeStatusEnum.OFFLINE.getStatus());
        existTheme.setUpdateId(userId);
        Boolean result = this.baseMapper.updateById(existTheme) > 0 ? Boolean.TRUE : Boolean.FALSE;
        if (result) {
            saveLineStatus(id, CourseThemeStatusEnum.OFFLINE.getStatus());
        }
        return result;
    }

    private void saveLineStatus(Long id, Integer oper) {
        try {
            log.info("保存课包上下线状态:{},{}", id, oper);
            LineStatusRecord createLineStatusRecordCmd = new LineStatusRecord();
            if (oper.equals(CourseThemeStatusEnum.ONLINE.getStatus())) {
                createLineStatusRecordCmd.setLineStatus(LineStatusEnum.ONLINE.getCode());
            } else {
                createLineStatusRecordCmd.setLineStatus(LineStatusEnum.OFFLINE.getCode());
            }
            createLineStatusRecordCmd.setBusinessId(id);
            createLineStatusRecordCmd.setCreateId(SessionUtil.getId());
            createLineStatusRecordCmd.setBusinessType(LineStatusBusinessEnum.COURSE_PACKAGE.getCode());
            lineStatusRecordService.createLineStatusRecord(createLineStatusRecordCmd);
        } catch (Exception e) {
            log.error("保存课包上下线状态异常:{}", e);
        }
    }

    private void validateSaveCourseTheme(CourseTheme courseTheme) {
        if (StrUtil.isBlank(courseTheme.getThemeMap())) {
            throw new MyException(400, "请上传首页主题图");
        }

        if (ImageUtil.isNotImage(courseTheme.getThemeMap())) {
            throw new MyException(400, "主题封面文件格式不被支持");
        }

        if (courseTheme.getIsForever().equals(BaseConstant.INT_FALSE)) {
            LocalDateTime endTime = courseTheme.getEndTime();
            if (endTime == null || !endTime.isAfter(courseTheme.getBeginTime())) {
                throw new MyException(400, "下线时间不能为空且必须大于上线时间");
            }
        }

        if (courseTheme.getId() == null) {
            LocalDateTime beginTime = courseTheme.getBeginTime();
            if (beginTime == null || !beginTime.isAfter(LocalDateTime.now())) {
                throw new MyException(400, "开始时间不能为空且必须大于当前时间");
            }
        }
    }

    private CourseTheme tryGetTheme(Long id) {
        CourseTheme existTheme = getById(id);
        if (ObjectUtil.isNull(existTheme)) {
            throw new MyException(400, "主题不存在");
        }

        return existTheme;
    }

    private void validateDeleteTheme(CourseTheme courseTheme) {
        if (courseTheme.getStatus().equals(CourseThemeStatusEnum.ONLINE.getStatus())
                && (courseTheme.getIsForever().equals(BaseConstant.INT_TRUE) && LocalDateTime.now().isAfter(courseTheme.getBeginTime()))
                && (courseTheme.getIsForever().equals(BaseConstant.INT_FALSE) && LocalDateTime.now().isAfter(courseTheme.getBeginTime()) && LocalDateTime.now().isBefore(courseTheme.getEndTime()))
        ) {
            throw new MyException(400, "不能删除在线的主题");
        }
    }

}
