package com.mrk.yudong.admin.infrastructure.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.infrastructure.user.model.MemberExchangeConfig;
import com.mrk.yudong.admin.api.user.vo.MemeberExchangeQueryVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 会员兑换规则配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
public interface MemberExchangeConfigMapper extends BaseMapper<MemberExchangeConfig> {

    IPage<MemberExchangeConfig> query(Page<MemberExchangeConfig> page, @Param("memeberExchangeQueryVO") MemeberExchangeQueryVO memeberExchangeQueryVO);

}
