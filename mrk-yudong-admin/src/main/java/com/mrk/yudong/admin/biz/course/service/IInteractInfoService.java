package com.mrk.yudong.admin.biz.course.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.infrastructure.course.model.InteractInfo;
import com.mrk.yudong.admin.api.course.vo.InteractInfoQueryVO;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.service.BaseService;

/**
 * <p>
 * 互动词表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
public interface IInteractInfoService extends BaseService<InteractInfo> {

    IPage<InteractInfo> query(PageDTO<InteractInfo> pageDTO, InteractInfoQueryVO interactInfoQueryVO);

    ResDTO<Boolean> remove(Long id);

}
