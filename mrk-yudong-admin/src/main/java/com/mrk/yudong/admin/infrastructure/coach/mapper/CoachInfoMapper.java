package com.mrk.yudong.admin.infrastructure.coach.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.infrastructure.coach.model.CoachInfo;
import com.mrk.yudong.admin.api.coach.vo.QueryConditionVO;
import com.mrk.yudong.core.model.PageDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 教练信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-24
 */
public interface CoachInfoMapper extends BaseMapper<CoachInfo> {

    /**
     * 查询教练列表
     *
     * @param page
     * @param queryConditionVO
     * @return
     */
    IPage<CoachInfo> query(PageDTO<CoachInfo> page, @Param("queryConditionVO") QueryConditionVO queryConditionVO);

    /**
     * 导出教练列表
     *
     * @param queryConditionVO
     * @return
     */
    List<JSONObject> queryMap(@Param("queryConditionVO") QueryConditionVO queryConditionVO);

    /**
     * 教练详情信息
     *
     * @param id
     * @return
     */
    CoachInfo detail(@Param("id") Long id);

}
