package com.mrk.yudong.admin.biz.course.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.api.course.vo.InteractInfoQueryVO;
import com.mrk.yudong.admin.biz.coach.service.ICoachInteractService;
import com.mrk.yudong.admin.biz.course.service.ICourseInteractService;
import com.mrk.yudong.admin.biz.course.service.IInteractInfoService;
import com.mrk.yudong.admin.infrastructure.course.mapper.InteractInfoMapper;
import com.mrk.yudong.admin.infrastructure.course.model.InteractInfo;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.share.constant.ResponseConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 互动词表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
@RequiredArgsConstructor
@Service
public class InteractInfoServiceImpl extends BaseServiceImpl<InteractInfoMapper, InteractInfo> implements IInteractInfoService {

    private final ICoachInteractService coachInteractService;

    private final ICourseInteractService courseInteractService;

    @Override
    public IPage<InteractInfo> query(PageDTO<InteractInfo> pageDTO, InteractInfoQueryVO interactInfoQueryVO) {
        return baseMapper.query(pageDTO, interactInfoQueryVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResDTO<Boolean> remove(Long id) {
        boolean remove = this.removeById(id);
        if (remove) {
            coachInteractService.remove("interact_id", ConditionEnum.EQ, id);
            courseInteractService.remove("interact_id", ConditionEnum.EQ, id);
            return ResDTO.ok();
        }

        return ResDTO.fail(ResponseConstant.COMMON_OPERATION_FAILED);
    }

}
