package com.mrk.yudong.admin.infrastructure.producttest.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.infrastructure.producttest.model.TestAppVersion;
import com.mrk.yudong.admin.infrastructure.producttest.vo.AppVersionQueryVO;
import com.mrk.yudong.core.model.PageDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * app版本控制表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
public interface TestAppVersionMapper extends BaseMapper<TestAppVersion> {

    /**
     * 根据终端获取最新版本信息
     *
     * @param isTra
     * @param terminal
     * @return
     */
    TestAppVersion getTop(@Param("isTra") Integer isTra, @Param("terminal") Integer terminal);

    /**
     * 查询版本列表
     *
     * @param page
     * @param appVersionQueryVO
     * @return
     */
    IPage<TestAppVersion> query(PageDTO<TestAppVersion> page, @Param("appVersionQueryVO") AppVersionQueryVO appVersionQueryVO);

    /**
     * 获取选择数据字典
     *
     * @param isTra
     * @return
     */
    List<JSONObject> option(Integer isTra);
}
