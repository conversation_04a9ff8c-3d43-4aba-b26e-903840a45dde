package com.mrk.yudong.admin.biz.course.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.merach.sun.content.api.LiveApi;
import com.merach.sun.content.constant.ContentQueueConstant;
import com.merach.sun.content.dto.cmd.live.AddTrancodeSEICmd;
import com.merach.sun.content.dto.mq.LiveSendSEIDTO;
import com.mrk.yudong.admin.biz.course.service.IAsyncTaskService;
import com.mrk.yudong.admin.biz.course.service.ICourseLinkService;
import com.mrk.yudong.admin.infrastructure.course.model.Course;
import com.mrk.yudong.admin.infrastructure.course.model.CourseLink;
import com.mrk.yudong.admin.rabbit.IRabbitOutput;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.share.constant.course.CourseConstant;
import com.mrk.yudong.share.constant.user.TrainConstant;
import com.mrk.yudong.share.vo.WebsocketSendVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Async
@Service
public class AsyncTaskServiceImpl implements IAsyncTaskService {

    private final ICourseLinkService courseLinkService;

    private final StringRedisTemplate redisTemplate;

    private final IRabbitOutput rabbitOutput;

    private final RocketMQTemplate rocketMQTemplate;

    private final LiveApi liveApi;

    @Value("${rocketmq.live.delay-topic}")
    private String topic;

    @Override
    public void courseLive(Course course) {
        String streamName = course.getId().toString();
        JSONObject endSEI = new JSONObject(1);
        JSONObject dict = new JSONObject(1);
        if (course.getStatus().equals(CourseConstant.STATUS_40)) {
            // 推送大屏长连接开始直播指令
            dict.put("liveStatus", BaseConstant.INT_TRUE);
            log.warn("courseId: {} 开始发送长连接数据", course.getId());

            WebsocketSendVO websocketSendVO = new WebsocketSendVO(course.getId().toString(), dict, false);
            Message<WebsocketSendVO> message = MessageBuilder.withPayload(websocketSendVO).build();
            rabbitOutput.websocketOutput().send(message);

            // 开启发送教案SEI消息任务
            this.sendCourseLinkSEI(course.getId(), course.getType(), course.getEquipmentId());

            // 发送开始直播SEI消息
            endSEI.put("isStartLive", BaseConstant.INT_TRUE);
            liveApi.addTrancodeSEI(new AddTrancodeSEICmd("", streamName, endSEI.toJSONString()));
        } else {
            log.warn("courseId: {} 开始发送关闭长连接数据", course.getId());
            // 发送结束直播SEI消息
            endSEI.put("isCloseLive", BaseConstant.INT_TRUE);
            liveApi.addTrancodeSEI(new AddTrancodeSEICmd("", streamName, endSEI.toJSONString()));

            // 推送大屏长连接结束直播指令
            dict.put("liveStatus", BaseConstant.INT_FALSE);
            WebsocketSendVO websocketSendVO = new WebsocketSendVO();
            websocketSendVO.setKey(course.getId().toString());
            websocketSendVO.setData(dict);
            Message<WebsocketSendVO> message = MessageBuilder.withPayload(websocketSendVO).build();
            rabbitOutput.closeSocketOutput().send(message);
        }
    }

    /**
     * 添加转码SEI信息
     *
     * @param courseId
     * @param type
     * @param equipmentId
     */
    @Override
    public void sendCourseLinkSEI(Long courseId, Integer type, Long equipmentId) {
        if (courseId == null || type == null) {
            log.warn("============= 入参异常，无法找到对应数据，入参数据courseId为：{} type：{} =============", courseId, type);
            return;
        }

        log.warn("发送电磁控指令入参数据courseId为：{} type：{}", courseId, type);
        if (!type.equals(CourseConstant.TYPE_MERIT) && !type.equals(CourseConstant.TYPE_PLOT)) {
            log.warn("============= 课程：{} 不是Merit课程，无法发送电磁控 =============", courseId);
            return;
        }

        List<CourseLink> courseLinks = courseLinkService.list("course_id", ConditionEnum.EQ, courseId, ConditionEnum.ASC, "begin_time");
        if (CollUtil.isEmpty(courseLinks)) {
            log.warn("============= 课程教案数据为空，无法进行数据发送 =============");
            return;
        }

        String streamName = courseId.toString();
        Map<String, String> seiKeyMap = new HashMap<>(courseLinks.size());
        courseLinks.forEach(courseLink -> {
            String linkId = courseLink.getId().toString();
            Integer beginTime = courseLink.getBeginTime();
            Double minNum = courseLink.getMinNum();
            Double maxNum = courseLink.getMaxNum();
            Integer adviseNum = courseLink.getAdviseNum();
            Integer slopeNum = courseLink.getSlopeNum();

            minNum = minNum == null ? 0 : minNum;
            maxNum = maxNum == null ? 0 : maxNum;
            if (equipmentId.equals(TrainConstant.EQUIPMENT_2)) {
                minNum = minNum * 10;
                maxNum = maxNum * 10;
            }
            adviseNum = adviseNum == null ? 0 : adviseNum;
            slopeNum = slopeNum == null ? 0 : slopeNum;

            if (minNum <= 0 && adviseNum <= 0 && slopeNum <= 0) {
                return;
            }

            JSONObject dict = new JSONObject(7);
            dict.put("id", linkId);
            dict.put("minNum", minNum);
            dict.put("maxNum", maxNum);
            dict.put("adviseNum", adviseNum);
            dict.put("slopeNum", slopeNum);
            dict.put("crux", courseLink.getCrux());
            dict.put("timestamp", System.currentTimeMillis());

            if (beginTime <= 0) {
                log.info("=== 第一小节，直接发送SEI消息，课程ID为：{} 小节ID为：{} ===", courseId, linkId);
                liveApi.addTrancodeSEI(new AddTrancodeSEICmd("", streamName, dict.toJSONString()));
                return;
            }
            String uuid = IdUtil.fastSimpleUUID();
            LiveSendSEIDTO sendSEIDTO = new LiveSendSEIDTO(courseId.toString(), streamName, dict.toJSONString(), linkId, uuid);
            String seiKey = RedisKeyConstant.SEI_KEY.replace("${linkId}", linkId);
            redisTemplate.opsForValue().set(seiKey, JSON.toJSONString(sendSEIDTO), Duration.ofSeconds(beginTime));
            seiKeyMap.put(uuid, uuid);

            Message<LiveSendSEIDTO> message = MessageBuilder.withPayload(sendSEIDTO).build();
            log.info("=== 开始发送教案非第一节教案SEI消息到rabbitmq 课程ID为：{} 小节ID为：{} message：{} ==== ", courseId, linkId, message);
            rocketMQTemplate.syncSendDelayTimeSeconds(String.format("%s:%s", topic, ContentQueueConstant.LIVE_SEND_SEI_QUEUE), message, beginTime - 5);
        });

        if (MapUtil.isNotEmpty(seiKeyMap)) {
            String key = RedisKeyConstant.COURSE_SEI_KEY.replace("${courseId}", courseId.toString());
            redisTemplate.delete(key);
            redisTemplate.opsForHash().putAll(key, seiKeyMap);
            redisTemplate.expire(key, Duration.ofHours(1L));
        }
    }

}
