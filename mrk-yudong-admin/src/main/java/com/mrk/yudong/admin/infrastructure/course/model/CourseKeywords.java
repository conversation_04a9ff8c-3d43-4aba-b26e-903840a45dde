package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 关键词信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CourseKeywords extends Model<CourseKeywords> {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 维度/关键字名称
     */
    @Length(min = 2, max = 10, message = "名称应在2-20个字符")
    private String keywords;

    /**
     * 维度id
     */
    private Long theDimension;

    /**
     * 设备类型id
     */
    private String equipTypeId;

    /**
     * 设备类型名称
     */
    @TableField(exist = false)
    private String equipTypeName;

    /**
     * 插入类型：0维度，1关键词
     */
    @TableField("type")
    private Integer type;

    /**
     * 删除状态
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建人名称
     */
    @TableField(exist = false)
    private String createName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
