package com.mrk.yudong.admin.biz.user.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.merach.sun.misc.enums.BrandTypeEnum;
import com.mrk.yudong.admin.api.user.vo.ExchangeCodeConfigQueryVO;
import com.mrk.yudong.admin.biz.user.service.IExchangeImageService;
import com.mrk.yudong.admin.biz.user.service.IExchangeOrderConfigService;
import com.mrk.yudong.admin.constant.AdminConstant;
import com.mrk.yudong.admin.infrastructure.user.mapper.ExchangeOrderConfigMapper;
import com.mrk.yudong.admin.infrastructure.user.model.ExchangeImage;
import com.mrk.yudong.admin.infrastructure.user.model.ExchangeOrderConfig;
import com.mrk.yudong.admin.properties.BizProperties;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.share.constant.ResponseConstant;
import com.mrk.yudong.share.constant.user.ProductConstant;
import com.mrk.yudong.share.enums.VipTypeConfigEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 兑换订单配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-30
 */
@RequiredArgsConstructor
@Service
public class ExchangeOrderConfigServiceImpl extends BaseServiceImpl<ExchangeOrderConfigMapper, ExchangeOrderConfig> implements IExchangeOrderConfigService {

    private final IExchangeImageService exchangeImageService;

    private final BizProperties bizProperties;

    @Override
    public IPage<ExchangeOrderConfig> query(Page<ExchangeOrderConfig> page, ExchangeCodeConfigQueryVO exchangeCodeConfigQueryVO) {
        return baseMapper.query(page, exchangeCodeConfigQueryVO);
    }

    /**
     * 新增订单配置
     *
     * @param exchangeOrderConfig
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R saveExchangeOrderConfig(ExchangeOrderConfig exchangeOrderConfig) {
        // 设置有效期时间
        long days = Objects.equals(exchangeOrderConfig.getVipType(), VipTypeConfigEnum.XVIP.getCode()) ? bizProperties.getXvipDay() : bizProperties.getRedeemEffectiveDay();
        exchangeOrderConfig.setEffectiveTime(LocalDate.now().plusDays(days));

        boolean save = this.save(exchangeOrderConfig);
        if (save) {
            List<String> images = exchangeOrderConfig.getImages();
            List<ExchangeImage> exchangeImages = images.stream().map(url -> new ExchangeImage(exchangeOrderConfig.getId(), url, ProductConstant.EXCHANGE_TYPE_1, exchangeOrderConfig.getCreateId())).collect(Collectors.toList());
            exchangeImageService.saveBatch(exchangeImages, exchangeImages.size());

            return R.ok();
        }

        return R.fail(ResponseConstant.COMMON_OPERATION_FAILED);
    }

    /**
     * 修改订单配置
     *
     * @param exchangeOrderConfig
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R updateExchangeOrderConfig(ExchangeOrderConfig exchangeOrderConfig) {
        boolean update = this.updateById(exchangeOrderConfig);
        if (update) {
            List<String> images = exchangeOrderConfig.getImages();
            if (CollUtil.isNotEmpty(images)) {
                exchangeImageService.remove("exchange_id", ConditionEnum.EQ, exchangeOrderConfig.getId());

                List<ExchangeImage> exchangeImages = images.stream().map(url -> new ExchangeImage(exchangeOrderConfig.getId(), url, ProductConstant.EXCHANGE_TYPE_1, exchangeOrderConfig.getUpdateId())).collect(Collectors.toList());
                exchangeImageService.saveBatch(exchangeImages, exchangeImages.size());
            }
            return R.ok();
        }

        return R.fail(ResponseConstant.COMMON_OPERATION_FAILED);
    }

    /**
     * 课程详情
     *
     * @param id
     * @return
     */
    @Override
    public R detail(Long id) {
        ExchangeOrderConfig exchangeOrderConfig = baseMapper.detail(id);
        if (exchangeOrderConfig == null) {
            return R.fail("无效的订单配置ID");
        }
        LocalDate localDate = LocalDate.now();
        LocalDate effectiveTime = exchangeOrderConfig.getEffectiveTime();
        Integer status = exchangeOrderConfig.getStatus();
        if (status == 0 && effectiveTime != null && localDate.isAfter(effectiveTime)) {
            exchangeOrderConfig.setStatus(AdminConstant.EXPIRE_STATUS);
            exchangeOrderConfig.setStatusDesc(AdminConstant.EXPIRE_STATUS_DESC);
        }

        List<ExchangeImage> list = exchangeImageService.list("exchange_id", ConditionEnum.EQ, id, ConditionEnum.ASC, "create_time");
        if (CollUtil.isNotEmpty(list)) {
            List<String> images = list.stream().map(ExchangeImage::getUrl).collect(Collectors.toList());
            exchangeOrderConfig.setImages(images);
        }

        Opt.ofNullable(BrandTypeEnum.get(exchangeOrderConfig.getVipClassify())).ifPresent(e -> exchangeOrderConfig.setVipClassifyName(e.getDesc()));
        Opt.ofNullable(VipTypeConfigEnum.get(exchangeOrderConfig.getVipType())).ifPresent(e -> exchangeOrderConfig.setVipTypeName(e.getName()));

        return R.ok(exchangeOrderConfig);
    }

}
