package com.mrk.yudong.admin.infrastructure.user.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * app版本控制表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AppVersion extends Model<AppVersion> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 终端：1-Android，2-IOS
     */
    @NotNull(message = "终端类型不能为空")
    private Integer terminal;
    /**
     * 平台类型1, "手机"2, "平板"3, "彩屏"
     */
    @NotNull(message = "平台类型不能为空")
    private Integer platforms;
    /**
     * 终端文字描述
     */
    @TableField(exist = false)
    private String terminalDesc;

    /**
     * 显示名称
     */
    @NotBlank(message = "版本名称不能为空")
    @Length(max = 16, message = "版本名称长度不能超过16位长度")
    private String name;

    /**
     * 彩屏设备编号
     */
    private String code;
    /**
     * 版本号
     */
    @NotNull(message = "版本号不能为空")
    @Min(value = 1, message = "版本号必须大于0")
    private Integer version;

    /**
     * 版本描述
     */
    @NotBlank(message = "版本描述不能为空")
    @Length(max = 256, message = "版本描述不能超过256位长度")
    private String remark;

    /**
     * 更新类型：1-选择更新，2-强制更新
     */
    @NotNull(message = "更新类型不能为空")
    private Integer type;

    /**
     * 更新类型文字描述
     */
    @TableField(exist = false)
    private String typeDesc;

    /**
     * 安卓更新包下载地址
     */
    private String url;

    /**
     * 是否支持会员兑换码兑换
     */
    private Integer isMemberCode;

    /**
     * 是否开放：0-否，1-是
     */
    @Range(max = 1L, message = "是否开放数据不合法")
    private Integer isOpen;

    /**
     * 是否为预发数据：0-否，1-是
     */
    private Integer isTra;

    /**
     * 是否开启会员订阅开关：0-否，1-是
     */
    private Integer isMemberUnsign;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 最小支持的系统版本
     */
    private String minOsVersion;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改人名称
     */
    @TableField(exist = false)
    private String updateName;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}