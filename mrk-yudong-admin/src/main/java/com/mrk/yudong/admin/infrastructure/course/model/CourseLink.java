package com.mrk.yudong.admin.infrastructure.course.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 课程小节表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CourseLink extends Model<CourseLink> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 课程目录（课程环节）ID
     */
    private Long catalogueId;

    /**
     * 名称
     */
    private String name;

    /**
     * 关键词
     */
    private String crux;

    /**
     * 开始时间（标题）
     */
    private String beginDesc;

    /**
     * 开始时间：秒
     */
    private Integer beginTime;

    /**
     * 维持时间：秒
     */
    private Integer sustainTime;

    /**
     * 结束时间：秒
     */
    private Integer endTime;

    /**
     * 最小速度
     */
    private Double minNum;

    /**
     * 最大速度
     */
    private Double maxNum;

    /**
     * 建议阻力
     */
    private Integer adviseNum;

    /**
     * 建议坡度
     */
    private Integer slopeNum;

    /**
     * 预计距离
     */
    private Double distance;

    /**
     * 预计消耗
     */
    private Double kcal;

    /**
     * 音乐名称
     */
    private String musicName;

    /**
     * 音乐时间
     */
    private Integer musicTime;

    /**
     * 排序号
     */
    private Integer sort;

    /**
     * 创建人ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    public CourseLink(Long id, Integer sustainTime) {
        this.id = id;
        this.sustainTime = sustainTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
