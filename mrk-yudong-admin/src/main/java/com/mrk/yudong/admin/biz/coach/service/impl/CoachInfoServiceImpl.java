package com.mrk.yudong.admin.biz.coach.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.api.coach.po.CoachPO;
import com.mrk.yudong.admin.api.coach.po.ImagePO;
import com.mrk.yudong.admin.api.coach.vo.QueryConditionVO;
import com.mrk.yudong.admin.biz.coach.service.ICoachCoverService;
import com.mrk.yudong.admin.biz.coach.service.ICoachImageService;
import com.mrk.yudong.admin.biz.coach.service.ICoachInfoService;
import com.mrk.yudong.admin.biz.coach.service.ICoachInteractService;
import com.mrk.yudong.admin.biz.course.service.IInteractInfoService;
import com.mrk.yudong.admin.infrastructure.coach.mapper.CoachInfoMapper;
import com.mrk.yudong.admin.infrastructure.coach.model.CoachCover;
import com.mrk.yudong.admin.infrastructure.coach.model.CoachImage;
import com.mrk.yudong.admin.infrastructure.coach.model.CoachInfo;
import com.mrk.yudong.admin.infrastructure.coach.model.CoachInteract;
import com.mrk.yudong.admin.infrastructure.course.model.InteractInfo;
import com.mrk.yudong.core.enums.ConditionEnum;
import com.mrk.yudong.core.model.PageDTO;
import com.mrk.yudong.core.model.R;
import com.mrk.yudong.core.model.ResDTO;
import com.mrk.yudong.core.model.SortModel;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.core.utils.SessionUtil;
import com.mrk.yudong.share.constant.BaseConstant;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.share.constant.ResponseConstant;
import com.mrk.yudong.share.util.ImageUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 教练信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-24
 */
@RequiredArgsConstructor
@Service
public class CoachInfoServiceImpl extends BaseServiceImpl<CoachInfoMapper, CoachInfo> implements ICoachInfoService {

    private final ICoachImageService coachImageService;

    private final ICoachInteractService coachInteractService;

    private final IInteractInfoService interactInfoService;

    private final ICoachCoverService coachCoverService;

    private final StringRedisTemplate redisTemplate;

    /**
     * 查询教练列表
     *
     * @param page
     * @param queryConditionVO
     * @return
     */
    @Override
    public IPage<CoachInfo> query(PageDTO<CoachInfo> page, QueryConditionVO queryConditionVO) {
        IPage<CoachInfo> query = baseMapper.query(page, queryConditionVO);
        List<CoachInfo> records = query.getRecords();
        if (CollUtil.isEmpty(records)) {
            return query;
        }

        records.forEach(coachInfo -> {
            int imageNum = coachImageService.count("coach_id", ConditionEnum.EQ, coachInfo.getId());
            coachInfo.setImageNum(imageNum);

            coachInfo.setFollowNum(0);
        });

        return query;
    }

    /**
     * 导出教练列表
     *
     * @param queryConditionVO
     * @return
     */
    @Override
    public List<JSONObject> query(QueryConditionVO queryConditionVO) {
        List<JSONObject> list = baseMapper.queryMap(queryConditionVO);

        if (CollUtil.isNotEmpty(list)) {
            for (Map<String, Object> dict : list) {
                String id = MapUtil.getStr(dict, "id");
                int imageNum = coachImageService.count("coach_id", ConditionEnum.EQ, id);
                dict.put("image_num", imageNum);
                dict.put("follow_num", 0);
            }
        }

        return list;
    }

    /**
     * 是否被禁用
     *
     * @param sysUserId
     * @return
     */
    @Override
    public boolean isDisable(String sysUserId) {
        CoachInfo coachInfo = this.getOne("sys_user_id", ConditionEnum.EQ, sysUserId, "status");
        return coachInfo.getStatus().equals(BaseConstant.INT_FALSE);
    }

    /**
     * 教练详情信息
     *
     * @param id
     * @return
     */
    @Override
    public CoachInfo detail(Long id) {
        return baseMapper.detail(id);
    }

    /**
     * 更新教练信息
     *
     * @param coachPO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public R updateCoachInfo(CoachPO coachPO) {
        int count = coachImageService.count("coach_id", ConditionEnum.EQ, coachPO.getId());
        List<String> images = coachPO.getImages();
        List<String> removeIds = coachPO.getRemoveIds();
        if (CollUtil.isNotEmpty(images)) {
            if (ImageUtil.isNotImages(images)) {
                return R.paramFail("生活照存在不被支持图片格式");
            }

            count += images.size();
            if (CollUtil.isNotEmpty(removeIds)) {
                count -= removeIds.size();
            }
            if (count > 20) {
                return R.paramFail("教练生活照不能超过20张");
            }
        } else {
            if (count == 0) {
                return R.paramFail("请上传教练生活照");
            }

            if (CollUtil.isNotEmpty(removeIds) && count <= removeIds.size()) {
                return R.paramFail("请至少保留一张生活照");
            }
        }

        int coverNum = coachCoverService.count("coach_id", ConditionEnum.EQ, coachPO.getId());
        List<String> covers = coachPO.getCovers();
        List<String> removeCoverIds = coachPO.getRemoveCoverIds();
        if (CollUtil.isNotEmpty(covers)) {
            if (ImageUtil.isNotImages(covers)) {
                return R.paramFail("封面图存在不被支持图片格式");
            }

            coverNum += covers.size();
            if (CollUtil.isNotEmpty(removeCoverIds)) {
                coverNum -= removeCoverIds.size();
            }
            if (coverNum > 20) {
                return R.paramFail("封面图不能超过20张");
            }
        } else {
            if (coverNum == 0) {
                return R.paramFail("请上传教练封面图");
            }

            if (CollUtil.isNotEmpty(removeCoverIds) && coverNum <= removeIds.size()) {
                return R.paramFail("请至少保留一张封面图");
            }
        }

        LinkedHashSet<Long> interactIds = coachPO.getInteractIds();
        if (CollUtil.isEmpty(interactIds) || interactIds.size() < 1 || interactIds.size() > 12) {
            return R.paramFail("互动词必须在1-12个之间");
        }

        count = interactInfoService.count("id", ConditionEnum.IN, interactIds);
        if (count < coachPO.getInteractIds().size()) {
            return R.paramFail("存在非法互动词");
        }
        if (StrUtil.isBlank(coachPO.getSelectAvatar())) {
            return R.paramFail("请上传找教练缩略图");
        }

        CoachInfo coachInfo = new CoachInfo();
        BeanUtil.copyProperties(coachPO, coachInfo);
        coachInfo.setUpdateId(SessionUtil.getId());
        boolean update = this.updateById(coachInfo);
        if (update) {
            Long userId = SessionUtil.getId();
            if (CollUtil.isNotEmpty(removeIds)) {
                coachImageService.removeByIds(removeIds);
            }

            if (CollUtil.isNotEmpty(images)) {
                List<CoachImage> coachImages = images.stream().map(imgUrl -> new CoachImage(coachPO.getId(), imgUrl, userId)).collect(Collectors.toList());
                coachImageService.saveBatch(coachImages, coachImages.size());
            }

            coachInteractService.remove("coach_id", ConditionEnum.EQ, coachPO.getId());
            List<CoachInteract> coachInteracts = interactIds.stream().map(interactId -> new CoachInteract(coachPO.getId(), interactId)).collect(Collectors.toList());
            coachInteractService.saveBatch(coachInteracts, coachInteracts.size());

            if (CollUtil.isNotEmpty(removeCoverIds)) {
                coachCoverService.removeByIds(removeCoverIds);
            }

            if (CollUtil.isNotEmpty(covers)) {
                List<CoachCover> coachCovers = covers.stream().map(cover -> new CoachCover(coachPO.getId(), cover, userId)).collect(Collectors.toList());
                coachCoverService.saveBatch(coachCovers, coachCovers.size());
            }

            String key = RedisKeyConstant.COACH_INTERACT_LIST_KEY.replace("${coachId}", coachPO.getId().toString());
            redisTemplate.delete(key);

            return R.ok();
        }

        return R.fail(ResponseConstant.COMMON_OPERATION_FAILED);
    }

    /**
     * 获取教练编辑信息
     *
     * @param id
     * @return
     */
    @Override
    public ResDTO<CoachPO> info(Long id) {
        CoachInfo coachInfo = this.getById(id);
        if (coachInfo == null) {
            return ResDTO.fail("数据不存在");
        }

        CoachPO coachPO = new CoachPO();
        BeanUtil.copyProperties(coachInfo, coachPO);

        SortModel sortModel = new SortModel(ConditionEnum.ASC, "create_time");
        List<CoachImage> list = coachImageService.list("coach_id", ConditionEnum.EQ, id, sortModel, "id", "img_url");
        if (CollUtil.isNotEmpty(list)) {
            List<ImagePO> items = list.stream().map(coachImage -> new ImagePO(coachImage.getId(), coachImage.getImgUrl())).collect(Collectors.toList());
            coachPO.setItems(items);
        }

        List<InteractInfo> interactInfos = coachInteractService.getListByCoachId(id);
        coachPO.setInteractInfos(interactInfos);

        List<CoachCover> coachCovers = coachCoverService.list("coach_id", ConditionEnum.EQ, id, sortModel, "id", "img_url");
        if (CollUtil.isNotEmpty(coachCovers)) {
            List<ImagePO> coverItems = coachCovers.stream().map(coachCover -> new ImagePO(coachCover.getId(), coachCover.getImgUrl())).collect(Collectors.toList());
            coachPO.setCoverItems(coverItems);
        }

        return ResDTO.ok(coachPO);
    }

    /**
     * 显示教练明细
     *
     * @param id
     * @return
     */
    @Override
    public R view(Long id) {
        CoachInfo coachInfo = this.detail(id);
        if (coachInfo == null) {
            return R.fail("数据不存在");
        }

        List<InteractInfo> interactInfos = coachInteractService.getListByCoachId(id);
        coachInfo.setInteractInfos(interactInfos);

        Dict data = new Dict(2);
        data.put("coachInfo", coachInfo);

        SortModel sortModel = new SortModel(ConditionEnum.DESC, "create_time");
        List<CoachImage> list = coachImageService.list("coach_id", ConditionEnum.EQ, id, sortModel, "img_url");
        if (CollUtil.isEmpty(list)) {
            data.put("images", list);
        } else {
            List<String> collect = list.stream().map(CoachImage::getImgUrl).collect(Collectors.toList());
            data.put("images", collect);
        }

        return R.ok(data);
    }

    /**
     * 获取教练封面图
     *
     * @param coachId
     * @return
     */
    @Override
    public R getCoachCoverList(Long coachId) {
        SortModel sortModel = new SortModel(ConditionEnum.ASC, "create_time");
        List<CoachCover> coachCovers = coachCoverService.list("coach_id", ConditionEnum.EQ, coachId, sortModel, "id", "img_url");
        if (CollUtil.isEmpty(coachCovers)) {
            return R.ok(coachCovers);
        }

        List<String> list = coachCovers.stream().map(CoachCover::getImgUrl).collect(Collectors.toList());
        return R.ok(list);
    }

}
