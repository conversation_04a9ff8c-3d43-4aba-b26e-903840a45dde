package com.mrk.yudong.admin.infrastructure.coursepackage.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mrk.yudong.admin.infrastructure.coursepackage.dao.CourseThemeTagDao;
import com.mrk.yudong.admin.infrastructure.coursepackage.mapper.CourseThemeTagRelMapper;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CourseThemeTagRel;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class CourseThemeTagDaoImpl extends BaseServiceImpl<CourseThemeTagRelMapper, CourseThemeTagRel> implements CourseThemeTagDao {
    @Override
    public Boolean batchCreateCourseThemeTag(List<CourseThemeTagRel> courseThemeTagRels) {
        if (CollUtil.isEmpty(courseThemeTagRels)) {
            return Boolean.FALSE;
        }
        return this.saveBatch(courseThemeTagRels);
    }

    @Override
    public Boolean deleteByThemeId(Long themeId) {
        if (ObjectUtil.isNull(themeId)) {
            return Boolean.FALSE;
        }
        LambdaQueryWrapper<CourseThemeTagRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CourseThemeTagRel::getThemeId, themeId);
        return this.remove(queryWrapper);
    }

    @Override
    public List<CourseThemeTagRel> findByThemeIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CourseThemeTagRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CourseThemeTagRel::getThemeId, ids);
        return this.baseMapper.selectList(queryWrapper);
    }
}
