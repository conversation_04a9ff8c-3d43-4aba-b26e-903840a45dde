package com.mrk.yudong.admin.biz.course.service.impl;

import com.mrk.yudong.admin.infrastructure.course.model.CourseStatus;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseStatusMapper;
import com.mrk.yudong.admin.biz.course.service.ICourseStatusService;
import com.mrk.yudong.core.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 课程状态表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
@Service
public class CourseStatusServiceImpl extends BaseServiceImpl<CourseStatusMapper, CourseStatus> implements ICourseStatusService {

    /**
     * 获取最新课程状态信息
     *
     * @param courseId
     * @return
     */
    @Override
    public CourseStatus getTop(Long courseId) {
        return baseMapper.getTop(courseId);
    }

}
