package com.mrk.yudong.admin.infrastructure.user.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mrk.yudong.admin.infrastructure.user.model.TrainDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 训练明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
public interface TrainDetailMapper extends BaseMapper<TrainDetail> {

    /**
     * 根据时间筛选统计训练数据
     *
     * @param time
     * @return
     */
    List<JSONObject> trainSumDate(@Param("time") String time);
}
