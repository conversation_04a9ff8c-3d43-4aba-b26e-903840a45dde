package com.mrk.yudong.admin.infrastructure.user.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 意见反馈附件表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class FeedbackAttachment extends Model<FeedbackAttachment> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 意见反馈ID
     */
    private Long feedbackId;

    /**
     * 文件类型：1-反馈附件，2-日志文件
     */
    private Integer type;

    /**
     * 文件地址
     */
    private String url;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
