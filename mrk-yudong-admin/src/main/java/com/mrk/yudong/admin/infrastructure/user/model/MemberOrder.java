package com.mrk.yudong.admin.infrastructure.user.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 会员订单
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-10
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class MemberOrder extends Model<MemberOrder> {

    private static final long serialVersionUID = 1L;

    /**
     * 开发主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 会员类型：10-VIP，20-SVIP，30-XVIP
     */
    private Integer vipType;

    /**
     * 交易类型：1-金额，2-兑换
     */
    private Integer type;

    /**
     * 开通类型：1-单月，2-单季，3-单年，4-连续包月，5-连续包季，6-连续包年，7-兑换码兑换，8-家庭共享
     */
    private Integer skuType;

    /**
     * 共享主账号用户ID
     */
    private Long shareUserId;

    /**
     * 开通数量
     */
    private Integer num;

    /**
     * 开通天数
     */
    private Integer days;

    /**
     * 支付金额
     */
    private BigDecimal price;

    /**
     * 支付方式：1-微信，2-支付宝，3-Apple内购
     */
    private Integer payType;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 第三方支付流水号
     */
    private String paySerialNo;

    /**
     * 终端：1-Android，2-IOS
     */
    private Integer terminal;

    /**
     * 是否自动扣款：0-否，1-是
     */
    private Integer isMake;

    /**
     * 签约编号
     */
    private String contractNo;

    /**
     * 会员有效期
     */
    private LocalDate memberTime;

    /**
     * 下次扣款金额
     */
    private BigDecimal amount;

    /**
     * 是否为自动续费订单：0-否，1-是
     */
    private Integer isAuto;

    /**
     * 兑换时间
     */
    private LocalDateTime exchangeTime;

    /**
     * 兑换码
     */
    private String exchangeCode;

    /**
     * 兑换类型
     */
    private Integer exchangeType;

    /**
     * 订单状态：0-交易关闭，1-待支付，2-交易完成，3-交易失败
     */
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
