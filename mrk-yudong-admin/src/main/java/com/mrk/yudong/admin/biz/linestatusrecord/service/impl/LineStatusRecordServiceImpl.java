package com.mrk.yudong.admin.biz.linestatusrecord.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mrk.yudong.admin.biz.linestatusrecord.service.LineStatusRecordService;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseMapper;
import com.mrk.yudong.admin.infrastructure.course.mapper.CourseTrainingPlanMapper;
import com.mrk.yudong.admin.infrastructure.course.model.Course;
import com.mrk.yudong.admin.infrastructure.course.model.CourseTrainingPlan;
import com.mrk.yudong.admin.infrastructure.coursepackage.mapper.CourseThemeMapper;
import com.mrk.yudong.admin.infrastructure.coursepackage.model.CourseTheme;
import com.mrk.yudong.admin.infrastructure.linestatus.mapper.LineStatusRecordMapper;
import com.mrk.yudong.admin.infrastructure.linestatus.model.LineStatusRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class LineStatusRecordServiceImpl implements LineStatusRecordService {
    private final LineStatusRecordMapper lineStatusRecordMapper;

    private final CourseMapper courseMapper;

    private final CourseThemeMapper courseThemeMapper;

    private final CourseTrainingPlanMapper courseTrainingPlanMapper;

    @Override
    public Boolean createLineStatusRecord(LineStatusRecord cmd) {
        cmd.setCreateTime(LocalDateTime.now());
        return lineStatusRecordMapper.insert(cmd) > 0;
    }

    @Override
    public void initCourseLineStatusRecord() {
        LambdaQueryWrapper<Course> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Course::getIsTra, 0);
        List<Course> courses = courseMapper.selectList(queryWrapper);
        List<LineStatusRecord> lineStatusRecords = new ArrayList<>();

        for (Course cours : courses) {
            if (null == cours.getEncodeTime()) {
                continue;
            }
            int daysBetweenEncodeAndNow = 1;
            if (cours.getLineStatus() != 1 && null != cours.getLineTime()) {
                daysBetweenEncodeAndNow = 2;
            }
            for (int i = 0; i < daysBetweenEncodeAndNow; i++) {
                LineStatusRecord lineStatusRecord = new LineStatusRecord();
                lineStatusRecord.setBusinessId(cours.getId());
                lineStatusRecord.setBusinessType(1);

                if (i == 0) {
                    lineStatusRecord.setLineStatus(0);
                    lineStatusRecord.setCreateTime(cours.getEncodeTime());
                } else {
                    lineStatusRecord.setLineStatus(1);
                    lineStatusRecord.setCreateTime(cours.getLineTime());
                }

                lineStatusRecords.add(lineStatusRecord);
            }
        }
        lineStatusRecordMapper.batchCreateLineStatusRecord(lineStatusRecords);
    }

    @Override
    public void initCourseThemeLineStatusRecord() {
        List<CourseTheme> CourseThemeS = listCourseTheme();
        List<LineStatusRecord> lineStatusRecords = new ArrayList<>();

        for (CourseTheme courseTheme : CourseThemeS) {
            int daysBetweenEncodeAndNow = 1;
            if (courseTheme.getStatus() != 1 && null != courseTheme.getUpdateTime()) {
                daysBetweenEncodeAndNow = 2;
            }
            for (int i = 0; i < daysBetweenEncodeAndNow; i++) {
                LineStatusRecord lineStatusRecord = new LineStatusRecord();
                lineStatusRecord.setBusinessId(courseTheme.getId());
                lineStatusRecord.setBusinessType(3);
                lineStatusRecord.setCreateTime(courseTheme.getCreateTime());

                if (i == 0) {
                    lineStatusRecord.setLineStatus(0);
                    lineStatusRecord.setCreateTime(courseTheme.getCreateTime());
                } else {
                    if (null == courseTheme.getUpdateTime()) {
                        continue;
                    }
                    lineStatusRecord.setLineStatus(1);
                    lineStatusRecord.setCreateTime(courseTheme.getUpdateTime());
                }

                lineStatusRecords.add(lineStatusRecord);
            }
        }
        lineStatusRecords.forEach(System.out::println);
        lineStatusRecordMapper.batchCreateLineStatusRecord(lineStatusRecords);
    }

    @Override
    public void initPlanLineStatusRecord() {
        List<CourseTrainingPlan> CourseTrainingPlanS = listCourseTrainingPlanDO();
        List<LineStatusRecord> lineStatusRecords = new ArrayList<>();
        for (CourseTrainingPlan courseTrainingPlan : CourseTrainingPlanS) {
            if (courseTrainingPlan.getOnlineStatus() == 1) {
                continue;
            }

            int daysBetweenEncodeAndNow = 1;
            if (courseTrainingPlan.getOnlineStatus() == 3 && null != courseTrainingPlan.getUpTime()) {
                daysBetweenEncodeAndNow = 2;
            }
            for (int i = 0; i < daysBetweenEncodeAndNow; i++) {
                LineStatusRecord lineStatusRecord = new LineStatusRecord();
                lineStatusRecord.setBusinessId(courseTrainingPlan.getId());
                lineStatusRecord.setBusinessType(2);

                if (i == 0) {
                    lineStatusRecord.setLineStatus(0);
                    lineStatusRecord.setCreateTime(courseTrainingPlan.getUpTime());
                } else {
                    LocalDateTime downTime = courseTrainingPlan.getDownTime();
                    if (null == courseTrainingPlan.getDownTime()) {
                        downTime = courseTrainingPlan.getOperationTime();
                    }
                    lineStatusRecord.setCreateTime(downTime);
                    lineStatusRecord.setLineStatus(1);
                }
                lineStatusRecords.add(lineStatusRecord);
            }
        }
        lineStatusRecords.forEach(System.out::println);
        lineStatusRecordMapper.batchCreateLineStatusRecord(lineStatusRecords);
    }

    private List<CourseTheme> listCourseTheme() {
        LambdaQueryWrapper<CourseTheme> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CourseTheme::getIsTra, 0);
        lambdaQueryWrapper.eq(CourseTheme::getType, 1);
        lambdaQueryWrapper.orderByDesc(CourseTheme::getCreateTime);

        return courseThemeMapper.selectList(lambdaQueryWrapper);
    }

    private List<CourseTrainingPlan> listCourseTrainingPlanDO() {
        LambdaQueryWrapper<CourseTrainingPlan> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CourseTrainingPlan::getIsTra, 0);
        lambdaQueryWrapper.eq(CourseTrainingPlan::getUseType, 1);
        lambdaQueryWrapper.orderByDesc(CourseTrainingPlan::getOperationTime);

        return courseTrainingPlanMapper.selectList(lambdaQueryWrapper);
    }
}
