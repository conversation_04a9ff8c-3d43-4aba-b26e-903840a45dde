package com.mrk.yudong.admin.infrastructure.device.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 设备类型
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EquEquipmentType extends Model<EquEquipmentType> {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 设备型号所属编号
     */
    private String code;

    /**
     * 蓝牙广播前缀id
     */
    private Integer prefixId;

    /**
     * 设备类型名称
     */
    private String typeName;

    /**
     * 目录等级
     */
    private Integer level;

    /**
     * 上级id
     */
    private Long parentId;

    /**
     * 展示图片
     */
    private String typeImages;

    /**
     * icon大图（用于首页右侧点击后展示）
     */
    private String bigIconImages;

    /**
     * icon图
     */
    private String iconImages;

    /**
     * 固件型号
     */
    private String typeVersion;

    /**
     * 是否电磁控档位1是0否
     */
    private Integer isElectromagneticControl;

    /**
     * 最高电磁档位
     */
    private Integer maxElectromagneticControl;

    /**
     * 产品类型,1:彩屏，2：蓝牙电子表，3：wifi电子表
     */
    private Integer productType;

    /**
     * 产品说明http
     */
    private String productManual;

    /**
     * 帮助中心http
     */
    private String helpCenter;

    /**
     * 是否显示阻力
     */
    private Integer showResistance;

    /**
     * 是否显示速度，1是0否
     */
    private Integer showSpeed;

    /**
     * 是否显示阻力，1是0否
     */
    private Integer showSlope;

    /**
     * 是否可调节档位，1是0否
     */
    private Integer showGear;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 删除状态0：未删除，1：已删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    private Long updateBy;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
