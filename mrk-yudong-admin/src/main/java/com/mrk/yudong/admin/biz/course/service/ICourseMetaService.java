package com.mrk.yudong.admin.biz.course.service;

import cn.hutool.core.lang.Dict;
import com.mrk.yudong.admin.infrastructure.course.model.CourseMeta;
import com.mrk.yudong.core.service.BaseService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 课程字典表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-18
 */
public interface ICourseMetaService extends BaseService<CourseMeta> {

    boolean isExist(String code, String val);

    List<Dict> option(String code);

    int count(String code, Set<String> cruxSet);

}
