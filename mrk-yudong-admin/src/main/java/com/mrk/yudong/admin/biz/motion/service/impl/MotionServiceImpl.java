package com.mrk.yudong.admin.biz.motion.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.api.motion.dto.qry.MotionPageQry;
import com.mrk.yudong.admin.biz.course.service.ICourseMetaService;
import com.mrk.yudong.admin.biz.motion.bo.CreateMotionBO;
import com.mrk.yudong.admin.biz.motion.bo.MotionBO;
import com.mrk.yudong.admin.biz.motion.bo.MotionCategoryBO;
import com.mrk.yudong.admin.biz.motion.bo.UpdateMotionBO;
import com.mrk.yudong.admin.biz.motion.enums.MotionStatusEnum;
import com.mrk.yudong.admin.biz.motion.service.MotionService;
import com.mrk.yudong.admin.infrastructure.motion.dao.MotionCategoryRelDao;
import com.mrk.yudong.admin.infrastructure.motion.dao.MotionDao;
import com.mrk.yudong.admin.infrastructure.motion.model.Motion;
import com.mrk.yudong.admin.infrastructure.motion.model.MotionCategoryRel;
import com.mrk.yudong.core.exception.MyException;
import com.mrk.yudong.core.model.PageDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class MotionServiceImpl implements MotionService {
    private final MotionDao motionDao;
    private final MotionCategoryRelDao motionCategoryRelDao;
    private final ICourseMetaService courseMetaService;

    @Override
    @Transactional
    public Motion createMotion(CreateMotionBO creation) {
        validateCreateMotion(creation);
        Motion needCreateMotion = BeanUtil.copyProperties(creation, Motion.class);
        needCreateMotion.setStatus(MotionStatusEnum.UNPUBLISH.getStatus());
        Motion created = motionDao.create(needCreateMotion);

        List<MotionCategoryRel> categoryRels = creation.getCategories().stream().map(categoryInfo -> {
            MotionCategoryRel rel = new MotionCategoryRel();
            rel.setMotionId(created.getId());
            rel.setCategoryId(categoryInfo.getId());
            rel.setSubCategoryId(categoryInfo.getSubCategoryId());
            rel.setCreateId(creation.getCreateId());
            rel.setUpdateId(creation.getCreateId());
            return rel;
        }).collect(Collectors.toList());
        motionCategoryRelDao.batchCreate(categoryRels);
        return created;
    }

    @Override
    public Motion updateMotion(UpdateMotionBO update) {
        validateUpdateMotion(update);
        Motion existMotion = motionDao.getById(update.getId());
        if (ObjectUtil.isNull(existMotion)) {
            throw new MyException(400, "动作不存在");
        }

        BeanUtil.copyProperties(update, existMotion);
        existMotion.setStatus(MotionStatusEnum.UNPUBLISH.getStatus());
        Motion updated = motionDao.update(existMotion);

        motionCategoryRelDao.deleteByMotionId(update.getId());
        List<MotionCategoryRel> categoryRels = update.getCategories().stream().map(categoryInfo -> {
            MotionCategoryRel rel = new MotionCategoryRel();
            rel.setMotionId(updated.getId());
            rel.setCategoryId(categoryInfo.getId());
            rel.setSubCategoryId(categoryInfo.getSubCategoryId());
            rel.setCreateId(update.getUpdateId());
            rel.setUpdateId(update.getUpdateId());
            return rel;
        }).collect(Collectors.toList());
        motionCategoryRelDao.batchCreate(categoryRels);
        return updated;
    }

    @Override
    public PageDTO<MotionBO> pageMotions(MotionPageQry pageQry) {
        IPage<Motion> page = motionDao.pageMotions(convertToMotionPageQry(pageQry));
        PageDTO<MotionBO> motions = new PageDTO<>();
        motions.setCurrent(pageQry.getCurrent());
        motions.setSize(pageQry.getSize());
        if (ObjectUtil.isNull(page) || CollectionUtil.isEmpty(page.getRecords())) {
            return motions;
        }

        List<MotionCategoryRel> categoryRels = motionCategoryRelDao.findByMotionIds(page.getRecords().stream().map(Motion::getId).collect(Collectors.toSet()));
        Map<Long, List<MotionCategoryRel>> categoryRelMap = categoryRels.stream().collect(Collectors.groupingBy(MotionCategoryRel::getMotionId));

        List<MotionBO> convertMotions = BeanUtil.copyToList(page.getRecords(), MotionBO.class);
        convertMotions.forEach(motion -> {
            List<MotionCategoryRel> rels = categoryRelMap.getOrDefault(motion.getId(), Collections.emptyList());
            List<MotionCategoryBO> categories = rels.stream().map(rel -> {
                MotionCategoryBO categoryInfo = new MotionCategoryBO();
                categoryInfo.setId(rel.getCategoryId());
                categoryInfo.setSubCategoryId(rel.getSubCategoryId());
                return categoryInfo;
            }).collect(Collectors.toList());
            motion.setCategories(categories);
        });
        motions.setRecords(convertMotions);
        motions.setTotal(page.getTotal());
        return motions;
    }

    @Override
    public Boolean publishMotion(Long id) {
        Motion existMotion = motionDao.getById(id);
        if (ObjectUtil.isNull(existMotion)) {
            throw new MyException(400, "动作不存在");
        }
        if (existMotion.getStatus().equals(MotionStatusEnum.PUBLISH.getStatus())) {
            return Boolean.FALSE;
        }

        existMotion.setStatus(MotionStatusEnum.PUBLISH.getStatus());
        Motion updated = motionDao.update(existMotion);
        return ObjectUtil.equals(updated.getStatus(), MotionStatusEnum.PUBLISH.getStatus());
    }

    @Override
    public Boolean unpublishMotion(Long id) {
        Motion existMotion = motionDao.getById(id);
        if (ObjectUtil.isNull(existMotion)) {
            throw new MyException(400, "动作不存在");
        }
        if (existMotion.getStatus().equals(MotionStatusEnum.UNPUBLISH.getStatus())) {
            return Boolean.FALSE;
        }

        existMotion.setStatus(MotionStatusEnum.UNPUBLISH.getStatus());
        Motion updated = motionDao.update(existMotion);
        return ObjectUtil.equals(updated.getStatus(), MotionStatusEnum.UNPUBLISH.getStatus());
    }

    private void validateCreateMotion(CreateMotionBO creation) {

        if (ObjectUtil.isNotNull(motionDao.getByName(creation.getName()))) {
            throw new MyException(400, "动作名称不能重复");
        }
    }

    private void validateUpdateMotion(UpdateMotionBO update) {
        Motion existMotion = motionDao.getByName(update.getName());
        if (ObjectUtil.isNotNull(existMotion) && !ObjectUtil.equals(existMotion.getId(), update.getId())) {
            throw new MyException(400, "动作名称不能重复");
        }
    }

    private MotionPageQry convertToMotionPageQry(MotionPageQry pageQry) {
        if (ObjectUtil.isNotNull(pageQry.getCategoryId())) {
            Set<Long> motionIds = motionCategoryRelDao.findMotionIdsByCategoryId(pageQry.getCategoryId());
            pageQry.setIds(motionIds);
        }

        if (ObjectUtil.isNotNull(pageQry.getSubCategoryId())) {
            Set<Long> motionIds = motionCategoryRelDao.findMotionIdsBySubCategoryId(pageQry.getSubCategoryId());
            pageQry.setIds(motionIds);
        }
        return pageQry;
    }
}
