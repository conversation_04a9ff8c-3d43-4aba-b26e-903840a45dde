package com.mrk.yudong.admin.biz.content.service;

import com.mrk.yudong.admin.infrastructure.content.model.LiveVideo;
import com.mrk.yudong.core.service.BaseService;
import com.mrk.yudong.share.dto.app.LiveVideoDTO;

import java.util.List;

/**
 * <p>
 * 实景视频 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-05
 */
public interface ILiveVideoService extends BaseService<LiveVideo> {
    /**
     * 修改banner上下线状态
     * @param id
     * @param oper 1上线，2下线
     * @return
     */
    boolean updateOnlineStatus(Long id,Integer oper);

    /**
     * 根据id查实景视频
     * @param id
     * @return
     */
    LiveVideoDTO queryLiveVideoById(Long id);
}
