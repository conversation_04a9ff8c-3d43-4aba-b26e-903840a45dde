package com.mrk.yudong.admin.biz.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mrk.yudong.admin.infrastructure.user.model.MemberExchangeConfig;
import com.mrk.yudong.admin.api.user.vo.MemeberExchangeQueryVO;
import com.mrk.yudong.core.service.BaseService;

/**
 * <p>
 * 会员兑换规则配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
public interface IMemberExchangeConfigService extends BaseService<MemberExchangeConfig> {

    IPage<MemberExchangeConfig> query(Page<MemberExchangeConfig> page, MemeberExchangeQueryVO memeberExchangeQueryVO);

}
