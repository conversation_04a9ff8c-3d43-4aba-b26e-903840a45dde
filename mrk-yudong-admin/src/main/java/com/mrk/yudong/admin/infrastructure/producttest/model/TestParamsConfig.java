package com.mrk.yudong.admin.infrastructure.producttest.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TestParamsConfig extends Model<TestParamsConfig> {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 名称
     */
    @NotNull(message = "请填写功能参数")
    private String name;

    /**
     * 指令值
     */
    @NotNull(message = "请填写值信息")
    private String value;
    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 设备分类id
     */
    @NotNull(message = "请选择设备分类")
    private Long typeId;

    /**
     * 参数分类，1：功能参数，2：设备参数
     */
    private Integer paramsType;

    /**
     * 排序
     */
    @Range(min = 0, max = 999, message = "排序最大值为999")
    private Integer sort;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    private Long updateBy;

    /**
     * 修改人名称
     */
    @TableField(exist = false)
    private String updateName;
    /**
     * 设备名称
     */
    @TableField(exist = false)
    private String equipName;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
