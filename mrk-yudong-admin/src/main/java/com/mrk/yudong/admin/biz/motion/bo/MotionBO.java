package com.mrk.yudong.admin.biz.motion.bo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class MotionBO {
    private Long id;

    private Integer status;

    private String name;

    private Integer type;

    private String part;

    private String nameAudio;

    private Long productId;

    private String cover;

    private Long coachId;

    private Long introduceVideoId;

    private String introduceVideo;

    private Long followAloneVideoId;

    private String followAloneVideo;

    private Integer motionCount;

    private Integer oneMotionTime;

    private Integer secondPerTime;

    private Double kcalPerTime;

    private Integer broadcastRequired;

    private Integer broadcastMode;

    private Integer broadcastSound;

    private String introduce;

    private List<MotionCategoryBO> categories;

    private Integer duration;

    private Integer grade;

    private Long createId;

    private Long updateId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
