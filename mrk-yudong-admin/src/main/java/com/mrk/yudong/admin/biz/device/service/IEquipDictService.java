package com.mrk.yudong.admin.biz.device.service;

import cn.hutool.core.lang.Dict;
import com.mrk.yudong.admin.infrastructure.device.model.EquipDict;
import com.mrk.yudong.core.service.BaseService;

import java.util.List;

/**
 * <p>
 * 设备字典表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-09
 */
public interface IEquipDictService extends BaseService<EquipDict> {

    boolean isExist(String code, String val);

    List<Dict> option(String code);

}
