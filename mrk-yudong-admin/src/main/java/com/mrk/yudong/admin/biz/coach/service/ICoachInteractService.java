package com.mrk.yudong.admin.biz.coach.service;

import com.mrk.yudong.admin.infrastructure.coach.model.CoachInteract;
import com.mrk.yudong.admin.infrastructure.course.model.InteractInfo;
import com.mrk.yudong.core.service.BaseService;

import java.util.List;

/**
 * <p>
 * 教练互动词表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
public interface ICoachInteractService extends BaseService<CoachInteract> {

    /**
     * 根据教练ID获取互动词
     *
     * @param coachId
     * @return
     */
    List<InteractInfo> getListByCoachId(Long coachId);

}
