package com.mrk.yudong.admin.biz.coursepackage.enums;

import com.mrk.yudong.share.vo.common.OptionVo;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum CourseThemeDisplayStatusEnum {
    UN_PUBLISH(1, "未上线"),
    ONGOING(2, "进行中"),
    OFFLINE(3, "已下线"),
    ;
    private final Integer status;
    private final String desc;

    public static List<OptionVo> findAllDispalyStatus() {
        CourseThemeDisplayStatusEnum[] enumValues = CourseThemeDisplayStatusEnum.values();
        List<OptionVo> enumItemList = new ArrayList<>();

        for (CourseThemeDisplayStatusEnum enumValue : enumValues) {
            enumItemList.add(new OptionVo(enumValue.getDesc(), enumValue.getStatus().toString()));
        }
        return enumItemList;
    }
}
