package com.mrk.yudong.admin.infrastructure.lay.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mrk.yudong.share.constant.BaseConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 机器人表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AppImageInfo extends Model<AppImageInfo> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 名称
     */
    @Size(min = 1, max = 15, message = "请输入1-15个字符，支持中英文、数字组成")
    @NotBlank(message = "请填写名称")
    private String name;

    /**
     * 图片地址
     */
    @NotBlank(message = "请上传图片")
    private String profilePhoto;

    /**
     * 操作时间
     */
    @TableField(fill = FieldFill.UPDATE)
    @JsonFormat(pattern = BaseConstant.DATE_TIME_FORMAT)
    private LocalDateTime operationTime;
    /**
     * 操作人id
     */
    private Long operationBy;
    /**
     * 操作人名称
     */
    @TableField(exist = false)
    private String operationName;

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
