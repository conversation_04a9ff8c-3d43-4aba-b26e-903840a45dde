package com.mrk.yudong.admin.infrastructure.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mrk.yudong.admin.infrastructure.course.model.CourseHotword;
import com.mrk.yudong.core.model.PageDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 课程热词表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
public interface CourseHotwordMapper extends BaseMapper<CourseHotword> {

    IPage<CourseHotword> query(PageDTO<CourseHotword> pageDTO, @Param("name") String name, @Param("equipmentId") Long equipmentId, @Param("stage") Integer stage, @Param("isTra") Integer isTra);

    IPage<CourseHotword> queryList(@Param("name") String name, @Param("equipmentId") Long equipmentId);

}
