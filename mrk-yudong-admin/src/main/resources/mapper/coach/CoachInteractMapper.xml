<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.coach.mapper.CoachInteractMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.coach.model.CoachInteract">
        <id column="id" property="id" />
        <result column="coach_id" property="coachId" />
        <result column="interact_id" property="interactId" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="getListByCoachId" resultType="com.mrk.yudong.admin.infrastructure.course.model.InteractInfo">
        SELECT * FROM interact_info WHERE id IN (SELECT interact_id FROM coach_interact WHERE coach_id = #{coachId})
    </select>

</mapper>
