<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.coach.mapper.CoachInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.coach.model.CoachInfo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="title" property="title"/>
        <result column="mobile" property="mobile"/>
        <result column="sys_user_id" property="sysUserId"/>
        <result column="avatar" property="avatar"/>
        <result column="cover" property="cover"/>
        <result column="introduce" property="introduce"/>
        <result column="num" property="num"/>
        <result column="sort" property="sort"/>
        <result column="sex" property="sex"/>
        <result column="status" property="status"/>
        <result column="status_desc" property="statusDesc"/>
        <result column="is_tra" property="isTra"/>
        <result column="create_id" property="createId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 查询教练列表 -->
    <select id="query" parameterType="com.mrk.yudong.admin.api.coach.vo.QueryConditionVO" resultMap="BaseResultMap">
        SELECT * FROM (SELECT a.*, ( SELECT COUNT(*) FROM course WHERE coach_id = a.id AND `status` >= 30 AND
        line_status = 1 ) num, ( SELECT `name` FROM sys_meta WHERE `code` = 'SYS_STATUS' AND val = a.`status` )
        status_desc FROM coach_info a) ci WHERE 1 = 1
        <if test="queryConditionVO.name != null and queryConditionVO.name != ''">
            AND ci.`name` LIKE CONCAT( '%', #{queryConditionVO.name}, '%' )
        </if>
        <if test="queryConditionVO.mobile != null and queryConditionVO.mobile != ''">
            AND ci.mobile LIKE CONCAT( '%', #{queryConditionVO.mobile}, '%' )
        </if>
        <if test="queryConditionVO.minNum != null">
            AND ci.num &gt;= #{queryConditionVO.minNum}
        </if>
        <if test="queryConditionVO.maxNum != null">
            AND ci.num &lt;= #{queryConditionVO.maxNum}
        </if>
        <if test="queryConditionVO.status != null">
            AND ci.`status` = #{queryConditionVO.status}
        </if>
        <if test="queryConditionVO.isTra != null">
            AND ci.is_tra &lt;= #{queryConditionVO.isTra}
        </if>
        ORDER BY IFNULL(ci.update_time, ci.create_time) DESC
    </select>

    <!-- 教练详情信息 -->
    <select id="detail" resultMap="BaseResultMap">
        SELECT ci.*, (SELECT `name` FROM sys_meta WHERE `code` = 'SYS_STATUS' AND val = ci.`status`) status_desc
        FROM coach_info ci
        WHERE ci.id = #{id}
    </select>

    <!-- 导出教练列表 -->
    <select id="queryMap" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        ci.id, ci.`name`, ci.title, ci.mobile, ci.introduce, ci.num, DATE_FORMAT(ci.update_time, '%Y-%m-%d %H:%i:%s')
        update_time, ci.status_desc
        FROM (SELECT a.*, ( SELECT COUNT(*) FROM course WHERE coach_id = a.id AND `status` >= 30 AND line_status = 1 )
        num, ( SELECT `name` FROM sys_meta WHERE `code` = 'SYS_STATUS' AND val = a.`status` ) status_desc FROM
        coach_info a) ci WHERE 1 = 1
        <if test="queryConditionVO.name != null and queryConditionVO.name != ''">
            AND ci.`name` LIKE CONCAT( '%', #{queryConditionVO.name}, '%' )
        </if>
        <if test="queryConditionVO.mobile != null and queryConditionVO.mobile != ''">
            AND ci.mobile LIKE CONCAT( '%', #{queryConditionVO.mobile}, '%' )
        </if>
        <if test="queryConditionVO.minNum != null">
            AND ci.num &gt;= #{queryConditionVO.minNum}
        </if>
        <if test="queryConditionVO.maxNum != null">
            AND ci.num &lt;= #{queryConditionVO.maxNum}
        </if>
        <if test="queryConditionVO.status != null">
            AND ci.`status` = #{queryConditionVO.status}
        </if>
        <if test="queryConditionVO.isTra != null">
            AND ci.is_tra &lt;= #{queryConditionVO.isTra}
        </if>
        ORDER BY IFNULL(ci.update_time, ci.create_time) DESC
    </select>

</mapper>
