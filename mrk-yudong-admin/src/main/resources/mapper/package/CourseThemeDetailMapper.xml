<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.coursepackage.mapper.CourseThemeDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.coursepackage.model.CourseThemeDetail">
        <id column="id" property="id" />
        <result column="course_id" property="courseId" />
        <result column="theme_id" property="themeId" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="queryCourse" resultType="com.alibaba.fastjson.JSONObject">
        SELECT c.id,
               c.`name`,
               (SELECT `name` FROM coach_info WHERE id = c.coach_id) coachName,
               (SELECT `name`
                FROM course_meta
                WHERE (CASE WHEN c.type = 1 THEN `code` = 'COURSE_LEVEL' ELSE `code` = 'COURSE_MERIT_LEVEL' END)
                  AND val = c.grade)                                 gradeDesc,
               c.course_time                                         courseTime
        FROM course_theme_detail ctd
                 LEFT JOIN course c ON ctd.course_id = c.id
        WHERE ctd.theme_id = #{themeId}
        ORDER BY ctd.id
    </select>

</mapper>
