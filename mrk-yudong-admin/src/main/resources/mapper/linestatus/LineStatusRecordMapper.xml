<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.linestatus.mapper.LineStatusRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.linestatus.model.LineStatusRecord">
        <id column="id" property="id" />
        <result column="business_id" property="businessId" />
        <result column="business_type" property="businessType" />
        <result column="create_time" property="createTime" />
        <result column="line_status" property="lineStatus" />
    </resultMap>
    <insert id="batchCreateLineStatusRecord">
        insert into line_status_record (business_id, business_type,line_status, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.businessId}, #{item.businessType},#{item.lineStatus} ,#{item.createTime})
        </foreach>
    </insert>

</mapper>
