<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.user.mapper.ExchangeCodeConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.user.model.ExchangeCodeConfig">
        <id column="id" property="id"/>
        <result column="vip_classify" property="vipClassify"/>
        <result column="vip_type" property="vipType"/>
        <result column="username" property="username"/>
        <result column="mobile" property="mobile"/>
        <result column="order_no" property="orderNo"/>
        <result column="shop" property="shop"/>
        <result column="remark" property="remark"/>
        <result column="num" property="num"/>
        <result column="type" property="type"/>
        <result column="type_desc" property="typeDesc"/>
        <result column="exchange_code" property="exchangeCode"/>
        <result column="exchange_time" property="exchangeTime"/>
        <result column="exchange_status" property="exchangeStatus" />
        <result column="exchange_status_desc" property="exchangeStatusDesc" />
        <result column="effective_time" property="effectiveTime" />
        <result column="is_tra" property="isTra" />
        <result column="status" property="status" />
        <result column="create_id" property="createId" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="update_id" property="updateId" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="query" parameterType="com.mrk.yudong.admin.api.user.vo.ExchangeCodeConfigQueryVO" resultMap="BaseResultMap">
        SELECT
        ecc.id,
        ecc.vip_classify,
        ecc.vip_type,
        ecc.username,
        ecc.mobile,
        ecc.order_no,
        ecc.shop,
        ecc.num,
        ecc.type,
        ( SELECT `name` FROM user_meta WHERE `code` = 'PRODUCT_TYPE' AND val = ecc.type ) type_desc,
        ecc.remark,
        ecc.exchange_code,
        ecc.exchange_time,
            ecc.exchange_status,
            ( SELECT `name` FROM user_meta WHERE `code` = 'EXCHANGE_STATUS' AND val = ecc.exchange_status ) exchange_status_desc,
            ecc.`status`,
            ( SELECT nick_name FROM sys_user WHERE id = ecc.create_id) create_name,
            ecc.create_time,
            ecc.effective_time
        FROM
            exchange_code_config ecc WHERE 1 = 1
        <if test="exchangeCodeConfigQueryVO.username != null and exchangeCodeConfigQueryVO.username != ''">
            AND ecc.username LIKE CONCAT('%', #{exchangeCodeConfigQueryVO.username}, '%')
        </if>
        <if test="exchangeCodeConfigQueryVO.mobile != null and exchangeCodeConfigQueryVO.mobile != ''">
            AND ecc.mobile LIKE CONCAT('%', #{exchangeCodeConfigQueryVO.mobile}, '%')
        </if>
        <if test="exchangeCodeConfigQueryVO.orderNo != null and exchangeCodeConfigQueryVO.orderNo != ''">
            AND ecc.order_no LIKE CONCAT('%', #{exchangeCodeConfigQueryVO.orderNo}, '%')
        </if>
        <if test="exchangeCodeConfigQueryVO.shop != null and exchangeCodeConfigQueryVO.shop != ''">
            AND ecc.shop = #{exchangeCodeConfigQueryVO.shop}
        </if>
        <if test="exchangeCodeConfigQueryVO.type != null">
            AND ecc.type = #{exchangeCodeConfigQueryVO.type}
        </if>
        <if test="exchangeCodeConfigQueryVO.vipClassify != null">
            AND ecc.vip_classify = #{exchangeCodeConfigQueryVO.vipClassify}
        </if>
        <if test="exchangeCodeConfigQueryVO.vipClassify != null">
            AND ecc.vip_type = #{exchangeCodeConfigQueryVO.vipType}
        </if>
        <if test="exchangeCodeConfigQueryVO.exchangeStatus != null and exchangeCodeConfigQueryVO.exchangeStatus != 2">
            AND ecc.exchange_status = #{exchangeCodeConfigQueryVO.exchangeStatus}
            AND (ecc.effective_time IS NULL OR date_format(now(),'%y%m%d') <![CDATA[ <= ]]>
            date_format(ecc.effective_time,'%y%m%d'))
        </if>
        <if test="exchangeCodeConfigQueryVO.exchangeStatus != null and exchangeCodeConfigQueryVO.exchangeStatus == 2">
            AND ecc.exchange_status = 0
            AND (ecc.effective_time IS NOT NULL AND date_format(now(),'%y%m%d') >
            date_format(ecc.effective_time,'%y%m%d'))
        </if>
        <if test="exchangeCodeConfigQueryVO.createId != null">
            AND ecc.create_id = #{exchangeCodeConfigQueryVO.createId}
        </if>
        <if test="exchangeCodeConfigQueryVO.isTra != null">
            AND ecc.is_tra &lt;= #{exchangeCodeConfigQueryVO.isTra}
        </if>
        ORDER BY ecc.create_time DESC
    </select>

    <select id="detail" resultMap="BaseResultMap">
        SELECT ecc.id,
               ecc.vip_classify,
               ecc.vip_type,
               ecc.username,
               ecc.mobile,
               ecc.order_no,
               ecc.shop,
               ecc.num,
               ecc.type,
               (SELECT `name` FROM user_meta WHERE `code` = 'PRODUCT_TYPE' AND val = ecc.type)              type_desc,
               ecc.remark,
               ecc.exchange_code,
               ecc.exchange_time,
            ecc.exchange_status,
            ( SELECT `name` FROM user_meta WHERE `code` = 'EXCHANGE_STATUS' AND val = ecc.exchange_status ) exchange_status_desc,
            ecc.`status`,
            ( SELECT nick_name FROM sys_user WHERE id = ecc.create_id) create_name,
            ecc.create_time,
            ecc.effective_time
        FROM
            exchange_code_config ecc WHERE id = #{id}
    </select>

    <!-- 导出 -->
    <select id="queryMap" parameterType="com.mrk.yudong.admin.api.user.vo.ExchangeCodeConfigQueryVO" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        ecc.id,
        ecc.vip_classify,
        ecc.vip_type,
        ecc.username,
        ecc.mobile,
        ecc.order_no,
        ecc.shop,
        ecc.num,
        ( SELECT `name` FROM user_meta WHERE `code` = 'PRODUCT_TYPE' AND val = ecc.type ) type_desc,
        ecc.remark,
        ecc.exchange_status,
        ( SELECT `name` FROM user_meta WHERE `code` = 'EXCHANGE_STATUS' AND val = ecc.exchange_status )
        exchange_status_desc,
        ( SELECT nick_name FROM sys_user WHERE id = ecc.create_id) create_name,
            DATE_FORMAT(ecc.create_time, '%Y-%m-%d %H:%i:%s') create_time,
            DATE_FORMAT(ecc.effective_time, '%Y-%m-%d') effective_time
        FROM
            exchange_code_config ecc WHERE 1 = 1
        <if test="exchangeCodeConfigQueryVO.username != null and exchangeCodeConfigQueryVO.username != ''">
            AND ecc.username LIKE CONCAT('%', #{exchangeCodeConfigQueryVO.username}, '%')
        </if>
        <if test="exchangeCodeConfigQueryVO.mobile != null and exchangeCodeConfigQueryVO.mobile != ''">
            AND ecc.mobile LIKE CONCAT('%', #{exchangeCodeConfigQueryVO.mobile}, '%')
        </if>
        <if test="exchangeCodeConfigQueryVO.orderNo != null and exchangeCodeConfigQueryVO.orderNo != ''">
            AND ecc.order_no LIKE CONCAT('%', #{exchangeCodeConfigQueryVO.orderNo}, '%')
        </if>
        <if test="exchangeCodeConfigQueryVO.shop != null and exchangeCodeConfigQueryVO.shop != ''">
            AND ecc.shop = #{exchangeCodeConfigQueryVO.shop}
        </if>
        <if test="exchangeCodeConfigQueryVO.type != null">
            AND ecc.type = #{exchangeCodeConfigQueryVO.type}
        </if>
        <if test="exchangeCodeConfigQueryVO.vipClassify != null">
            AND ecc.vip_classify = #{exchangeCodeConfigQueryVO.vipClassify}
        </if>
        <if test="exchangeCodeConfigQueryVO.vipClassify != null">
            AND ecc.vip_type = #{exchangeCodeConfigQueryVO.vipType}
        </if>
        <if test="exchangeCodeConfigQueryVO.exchangeStatus != null and exchangeCodeConfigQueryVO.exchangeStatus != 2">
            AND ecc.exchange_status = #{exchangeCodeConfigQueryVO.exchangeStatus}
            AND (ecc.effective_time IS NULL OR date_format(now(),'%y%m%d') <![CDATA[ <= ]]>
            date_format(ecc.effective_time,'%y%m%d'))
        </if>
        <if test="exchangeCodeConfigQueryVO.exchangeStatus != null and exchangeCodeConfigQueryVO.exchangeStatus == 2">
            AND ecc.exchange_status = 0
            AND (ecc.effective_time IS NOT NULL AND date_format(now(),'%y%m%d') >
            date_format(ecc.effective_time,'%y%m%d'))
        </if>
        <if test="exchangeCodeConfigQueryVO.createId != null">
            AND EXISTS ( SELECT 1 FROM sys_user WHERE id = ecc.create_id AND id = #{exchangeCodeConfigQueryVO.createId}
            )
        </if>
        <if test="exchangeCodeConfigQueryVO.isTra != null">
            AND ecc.is_tra &lt;= #{exchangeCodeConfigQueryVO.isTra}
        </if>
        ORDER BY ecc.create_time DESC
    </select>

    <select id="createOption" resultType="com.alibaba.fastjson.JSONObject">
        SELECT ( SELECT nick_name FROM sys_user WHERE id = ecc.create_id ) `name`, ecc.create_id `value` FROM exchange_code_config ecc GROUP BY ecc.create_id ORDER BY ecc.id ASC
    </select>

</mapper>
