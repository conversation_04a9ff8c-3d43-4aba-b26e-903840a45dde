<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.user.mapper.LogUserLoginMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.user.model.LogUserLogin">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="login_ip" property="loginIp" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="region" property="region" />
        <result column="address" property="address" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="terminal" property="terminal" />
        <result column="terminal_desc" property="terminalDesc" />
        <result column="device_name" property="deviceName" />
        <result column="device_no" property="deviceNo" />
        <result column="device_os" property="deviceOs" />
        <result column="app_version" property="appVersion" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 查询用户登录日志 -->
    <select id="query" resultMap="BaseResultMap">
        SELECT lul.*, (SELECT `name` FROM user_meta WHERE `code` = 'TERMINAL' AND val = lul.terminal) terminal_desc FROM log_user_login lul WHERE lul.user_id = #{userId} ORDER BY lul.create_time DESC
    </select>

    <!-- 根据日期获取登录人数 -->
    <select id="countDate" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT user_id) FROM log_user_login WHERE create_time &gt;= CONCAT(#{beginDate}, ' 00:00:00') AND create_time &lt;= CONCAT(#{endDate}, ' 23:59:59')
    </select>

</mapper>
