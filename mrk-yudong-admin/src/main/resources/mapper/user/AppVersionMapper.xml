<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.user.mapper.AppVersionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.user.model.AppVersion">
        <id column="id" property="id" />
        <result column="terminal" property="terminal" />
        <result column="terminal_desc" property="terminalDesc" />
        <result column="name" property="name" />
        <result column="version" property="version" />
        <result column="remark" property="remark" />
        <result column="type" property="type" />
        <result column="type_desc" property="typeDesc" />
        <result column="url" property="url" />
        <result column="is_member_code" property="isMemberCode" />
        <result column="is_open" property="isOpen" />
        <result column="is_tra" property="isTra" />
        <result column="create_id" property="createId" />
        <result column="create_time" property="createTime" />
        <result column="update_id" property="updateId" />
        <result column="update_name" property="updateName" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 获取选择数据字典 -->
    <select id="option" resultType="com.alibaba.fastjson.JSONObject">
        SELECT av.update_id `value`, ( SELECT nick_name FROM sys_user WHERE id = av.update_id ) `name` FROM app_version av WHERE is_tra &lt;= #{isTra} GROUP BY av.update_id
    </select>

</mapper>