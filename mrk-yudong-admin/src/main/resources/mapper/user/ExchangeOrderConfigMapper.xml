<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.user.mapper.ExchangeOrderConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.user.model.ExchangeOrderConfig">
        <id column="id" property="id"/>
        <result column="vip_classify" property="vipClassify"/>
        <result column="vip_type" property="vipType"/>
        <result column="username" property="username"/>
        <result column="order_no" property="orderNo"/>
        <result column="shop_code" property="shopCode"/>
        <result column="shop_name" property="shopName"/>
        <result column="pay_price" property="payPrice"/>
        <result column="pay_time" property="payTime"/>
        <result column="sku_code" property="skuCode"/>
        <result column="exchange_time" property="exchangeTime"/>
        <result column="effective_time" property="effectiveTime"/>
        <result column="status" property="status"/>
        <result column="status_desc" property="statusDesc" />
        <result column="is_tra" property="isTra" />
        <result column="create_id" property="createId" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="update_id" property="updateId" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 查询配置列表 -->
    <select id="query" parameterType="com.mrk.yudong.admin.api.user.vo.ExchangeCodeConfigQueryVO" resultMap="BaseResultMap">
        SELECT
        eoc.id,
        eoc.vip_classify,
        eoc.vip_type,
        eoc.username,
        eoc.order_no,
        eoc.shop_code,
        eoc.shop_name,
        eoc.pay_price,
        eoc.pay_time,
        eoc.`status`,
        ( SELECT `name` FROM user_meta WHERE `code` = 'EXCHANGE_STATUS' AND val = eoc.`status` ) status_desc,
        ( SELECT nick_name FROM sys_user WHERE id = eoc.create_id ) create_name,
        eoc.create_time,
            eoc.effective_time
        FROM
        exchange_order_config eoc WHERE 1 = 1
        <if test="exchangeCodeConfigQueryVO.username != null and exchangeCodeConfigQueryVO.username != ''">
            AND eoc.username LIKE CONCAT('%', #{exchangeCodeConfigQueryVO.username}, '%')
        </if>
        <if test="exchangeCodeConfigQueryVO.orderNo != null and exchangeCodeConfigQueryVO.orderNo != ''">
            AND eoc.order_no LIKE CONCAT('%', #{exchangeCodeConfigQueryVO.orderNo}, '%')
        </if>
        <if test="exchangeCodeConfigQueryVO.shop != null and exchangeCodeConfigQueryVO.shop != ''">
            AND eoc.shop_code = #{exchangeCodeConfigQueryVO.shop}
        </if>
        <if test="exchangeCodeConfigQueryVO.vipClassify != null">
            AND eoc.vip_classify = #{exchangeCodeConfigQueryVO.vipClassify}
        </if>
        <if test="exchangeCodeConfigQueryVO.vipClassify != null">
            AND eoc.vip_type = #{exchangeCodeConfigQueryVO.vipType}
        </if>
        <if test="exchangeCodeConfigQueryVO.status != null and exchangeCodeConfigQueryVO.status != 2">
            AND eoc.`status` = #{exchangeCodeConfigQueryVO.status}
            AND (eoc.effective_time IS NULL OR date_format(now(),'%y%m%d')  &lt;= date_format(eoc.effective_time,'%y%m%d'))
        </if>
        <if test="exchangeCodeConfigQueryVO.status != null and exchangeCodeConfigQueryVO.status == 2">
            AND eoc.`status` = 0
            AND (eoc.effective_time IS NOT NULL AND date_format(now(),'%y%m%d') > date_format(eoc.effective_time,'%y%m%d'))
        </if>
        <if test="exchangeCodeConfigQueryVO.isTra != null">
            AND eoc.is_tra &lt;= #{exchangeCodeConfigQueryVO.isTra}
        </if>
        ORDER BY eoc.create_time DESC
    </select>

    <select id="detail" resultMap="BaseResultMap">
        SELECT eoc.id,
               eoc.vip_classify,
               eoc.vip_type,
               eoc.username,
               eoc.order_no,
               eoc.shop_code,
               eoc.shop_name,
               eoc.pay_price,
               eoc.pay_time,
               eoc.sku_code,
               eoc.`status`,
               (SELECT `name` FROM user_meta WHERE `code` = 'EXCHANGE_STATUS' AND val = eoc.`status`) status_desc,
               (SELECT nick_name FROM sys_user WHERE id = eoc.create_id)                              create_name,
            eoc.create_time,
            eoc.effective_time
        FROM exchange_order_config eoc WHERE eoc.id = #{id}
    </select>

</mapper>
