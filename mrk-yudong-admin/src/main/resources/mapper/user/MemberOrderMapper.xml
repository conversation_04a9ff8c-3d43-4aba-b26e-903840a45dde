<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.user.mapper.MemberOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.user.model.MemberOrder">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="type" property="type"/>
        <result column="sku_type" property="skuType"/>
        <result column="share_user_id" property="shareUserId"/>
        <result column="num" property="num"/>
        <result column="price" property="price"/>
        <result column="pay_type" property="payType"/>
        <result column="pay_time" property="payTime"/>
        <result column="pay_serial_no" property="paySerialNo"/>
        <result column="terminal" property="terminal"/>
        <result column="is_make" property="isMake"/>
        <result column="contract_no" property="contractNo"/>
        <result column="member_time" property="memberTime"/>
        <result column="amount" property="amount"/>
        <result column="is_auto" property="isAuto"/>
        <result column="exchange_time" property="exchangeTime"/>
        <result column="exchange_code" property="exchangeCode"/>
        <result column="exchange_type" property="exchangeType"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 会员订单列表映射结果 -->
    <resultMap id="MemberOrderPOResultMap" type="com.mrk.yudong.admin.api.user.po.MemberOrderPO">
        <id column="id" property="userId"/>
        <result column="nick_name" property="nickName"/>
        <result column="mobile" property="mobile"/>
        <result column="type" property="type"/>
        <result column="order_id" property="orderId"/>
        <result column="price" property="price"/>
        <result column="sku_type" property="skuType"/>
        <result column="product_name" property="productName"/>
        <result column="sku_type_desc" property="skuTypeDesc"/>
        <result column="terminal_desc" property="terminalDesc"/>
        <result column="pay_type_desc" property="payTypeDesc"/>
        <result column="pay_time" property="payTime"/>
        <result column="num" property="num"/>
        <result column="exchange_code" property="exchangeCode"/>
        <result column="exchange_time" property="exchangeTime"/>
        <result column="status" property="status"/>
        <result column="status_desc" property="statusDesc"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 导出订单列表 -->
    <select id="queryMap" parameterType="com.mrk.yudong.admin.api.user.vo.MemberOrderQueryVO"
            resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        mo.user_id id,
        mo.id order_id,
        mo.type,
        mo.price,
        mo.sku_type,
        mo.product_name,
        ( CASE WHEN mo.type = 1 THEN ( SELECT `name` FROM user_meta WHERE `code` = 'SKU_TYPE' AND val = mo.sku_type )
        WHEN mo.type = 2 THEN ( SELECT `name` FROM user_meta WHERE `code` = 'EXCHANGE_TYPE' AND val = mo.exchange_type )
        ELSE ( SELECT `name` FROM user_meta WHERE `code` = 'ORDER_TYPE' AND val = mo.type ) END ) sku_type_desc,
        ( SELECT `name` FROM user_meta WHERE `code` = 'TERMINAL' AND val = mo.terminal ) terminal_desc,
        ( SELECT `name` FROM user_meta WHERE `code` = 'PAY_TYPE' AND val = mo.pay_type ) pay_type_desc,
        DATE_FORMAT(mo.pay_time, '%Y-%m-%d %H:%i:%s') pay_time,
        mo.num,
        mo.exchange_code,
        DATE_FORMAT(mo.exchange_time, '%Y-%m-%d %H:%i:%s') exchange_time,
        mo.`status`,
        ( SELECT `name` FROM user_meta WHERE `code` = 'ORDER_STATUS' AND val = mo.`status` ) status_desc,
        DATE_FORMAT(mo.create_time, '%Y-%m-%d %H:%i:%s') create_time
        FROM
        member_order mo WHERE 1 = 1
        <if test="memberOrderQueryVO.status != null">
            AND mo.`status` = #{memberOrderQueryVO.status}
        </if>
        <if test="memberOrderQueryVO.type != null">
            <if test="memberOrderQueryVO.type == 1">
                AND mo.type = #{memberOrderQueryVO.type}
            </if>
            <if test="memberOrderQueryVO.type == 2">
                AND mo.type &gt;= #{memberOrderQueryVO.type}
            </if>
        </if>
        <if test="memberOrderQueryVO.userId != null">
            AND mo.user_id = #{memberOrderQueryVO.userId}
        </if>
        <if test="memberOrderQueryVO.exchangeCode != null">
            AND (mo.exchange_code LIKE CONCAT('%', #{memberOrderQueryVO.exchangeCode}, '%') OR mo.id LIKE CONCAT('%',
            #{memberOrderQueryVO.exchangeCode}, '%'))
        </if>
        <if test="memberOrderQueryVO.skuType != null">
            AND mo.sku_type = #{memberOrderQueryVO.skuType}
        </if>
        <if test="memberOrderQueryVO.terminal != null">
            AND mo.terminal = #{memberOrderQueryVO.terminal}
        </if>
        <if test="memberOrderQueryVO.payType != null">
            AND mo.pay_type = #{memberOrderQueryVO.payType}
        </if>
        <if test="memberOrderQueryVO.type == 1 and memberOrderQueryVO.beginTime != null">
            AND mo.pay_time &gt;= #{memberOrderQueryVO.beginTime}
        </if>
        <if test="memberOrderQueryVO.type == 1 and memberOrderQueryVO.endTime != null">
            AND mo.pay_time &lt;= #{memberOrderQueryVO.endTime}
        </if>
        <if test="memberOrderQueryVO.type == 2 and memberOrderQueryVO.beginTime != null">
            AND mo.create_time &gt;= #{memberOrderQueryVO.beginTime}
        </if>
        <if test="memberOrderQueryVO.type == 2 and memberOrderQueryVO.endTime != null">
            AND mo.create_time &lt;= #{memberOrderQueryVO.endTime}
        </if>
        <if test="memberOrderQueryVO.type == 2 and memberOrderQueryVO.exchangeType != null and memberOrderQueryVO.exchangeType lte 2">
            AND mo.exchange_type = #{memberOrderQueryVO.exchangeType}
        </if>
        <if test="memberOrderQueryVO.type == 2 and memberOrderQueryVO.exchangeType != null and memberOrderQueryVO.exchangeType == 3">
            AND mo.type = #{memberOrderQueryVO.exchangeType}
        </if>
        <if test="memberOrderQueryVO.type == 1 and memberOrderQueryVO.isTra != null">
            AND EXISTS( SELECT 1 FROM product WHERE id = mo.product_id AND is_tra &lt;= #{memberOrderQueryVO.isTra} )
        </if>
        ORDER BY mo.create_time DESC
    </select>

    <!-- 分页查询用户会员开通记录 -->
    <select id="pageRecord" resultMap="MemberOrderPOResultMap">
        SELECT
        mo.user_id id,
        mo.id order_id,
        mo.type,
        mo.price,
        mo.sku_type,
        mo.product_name,
        ( CASE WHEN mo.type = 1 THEN ( SELECT `name` FROM user_meta WHERE `code` = 'SKU_TYPE' AND val = mo.sku_type )
        WHEN mo.type = 2 THEN ( SELECT `name` FROM user_meta WHERE `code` = 'EXCHANGE_TYPE' AND val = mo.exchange_type )
        ELSE ( SELECT `name` FROM user_meta WHERE `code` = 'ORDER_TYPE' AND val = mo.type ) END ) sku_type_desc,
        ( SELECT `name` FROM user_meta WHERE `code` = 'TERMINAL' AND val = mo.terminal ) terminal_desc,
        ( SELECT `name` FROM user_meta WHERE `code` = 'PAY_TYPE' AND val = mo.pay_type ) pay_type_desc,
        mo.pay_time,
        mo.num,
        mo.exchange_code,
        mo.exchange_time,
        mo.`status`,
        ( SELECT `name` FROM user_meta WHERE `code` = 'ORDER_STATUS' AND val = mo.`status` ) status_desc,
        mo.create_time
        FROM
        member_order mo WHERE 1 = 1
        <if test="memberOrderQueryVO.status != null">
            AND mo.`status` = #{memberOrderQueryVO.status}
        </if>
        <if test="memberOrderQueryVO.type != null">
            <if test="memberOrderQueryVO.type == 1">
                AND mo.type = #{memberOrderQueryVO.type}
            </if>
            <if test="memberOrderQueryVO.type == 2">
                AND mo.type &gt;= #{memberOrderQueryVO.type}
            </if>
        </if>
        <if test="memberOrderQueryVO.userId != null">
            AND mo.user_id = #{memberOrderQueryVO.userId}
        </if>
        <if test="memberOrderQueryVO.exchangeCode != null">
            AND (mo.exchange_code LIKE CONCAT('%', #{memberOrderQueryVO.exchangeCode}, '%') OR mo.id LIKE CONCAT('%',
            #{memberOrderQueryVO.exchangeCode}, '%'))
        </if>
        <if test="memberOrderQueryVO.skuType != null">
            AND mo.sku_type = #{memberOrderQueryVO.skuType}
        </if>
        <if test="memberOrderQueryVO.terminal != null">
            AND mo.terminal = #{memberOrderQueryVO.terminal}
        </if>
        <if test="memberOrderQueryVO.payType != null">
            AND mo.pay_type = #{memberOrderQueryVO.payType}
        </if>
        <if test="memberOrderQueryVO.type == 1 and memberOrderQueryVO.beginTime != null">
            AND mo.pay_time &gt;= #{memberOrderQueryVO.beginTime}
        </if>
        <if test="memberOrderQueryVO.type == 1 and memberOrderQueryVO.endTime != null">
            AND mo.pay_time &lt;= #{memberOrderQueryVO.endTime}
        </if>
        <if test="memberOrderQueryVO.type == 2 and memberOrderQueryVO.beginTime != null">
            AND mo.create_time &gt;= #{memberOrderQueryVO.beginTime}
        </if>
        <if test="memberOrderQueryVO.type == 2 and memberOrderQueryVO.endTime != null">
            AND mo.create_time &lt;= #{memberOrderQueryVO.endTime}
        </if>
        <if test="memberOrderQueryVO.type == 2 and memberOrderQueryVO.exchangeType != null and memberOrderQueryVO.exchangeType lte 2">
            AND mo.exchange_type = #{memberOrderQueryVO.exchangeType}
        </if>
        <if test="memberOrderQueryVO.type == 2 and memberOrderQueryVO.exchangeType != null and memberOrderQueryVO.exchangeType == 3">
            AND mo.type = #{memberOrderQueryVO.exchangeType}
        </if>
        <if test="memberOrderQueryVO.type == 1 and memberOrderQueryVO.isTra != null">
            AND EXISTS( SELECT 1 FROM product WHERE id = mo.product_id AND is_tra &lt;= #{memberOrderQueryVO.isTra} )
        </if>
        ORDER BY mo.create_time DESC
    </select>

</mapper>
