<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.user.mapper.TrainDetailMapper">

    <select id="trainSumDate" resultType="com.alibaba.fastjson.JSONObject">
        SELECT equipment_id                equipmentId,
               COUNT(DISTINCT user_id)     userNum,
               COALESCE(SUM(take_time), 0) takeTime,
               COALESCE(SUM(kcal), 0)      kcal
        FROM train_detail
        WHERE create_time &gt;= CONCAT(#{time}, ' 00:00:00')
          AND create_time &lt;= CONCAT(#{time}, ' 23:59:59')
          and status &gt;= 10
        GROUP BY equipment_id
    </select>

</mapper>
