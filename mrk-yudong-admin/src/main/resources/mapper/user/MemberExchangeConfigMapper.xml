<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.user.mapper.MemberExchangeConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.user.model.MemberExchangeConfig">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="shop_code" property="shopCode" />
        <result column="is_set_time" property="isSetTime" />
        <result column="begin_time" property="beginTime" />
        <result column="end_time" property="endTime" />
        <result column="is_set_sku" property="isSetSku" />
        <result column="sku_code" property="skuCode" />
        <result column="num" property="num" />
        <result column="type" property="type" />
        <result column="type_desc" property="typeDesc" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="status_desc" property="statusDesc" />
        <result column="create_id" property="createId" />
        <result column="create_time" property="createTime" />
        <result column="update_id" property="updateId" />
        <result column="update_name" property="updateName" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="query" parameterType="com.mrk.yudong.admin.api.user.vo.MemeberExchangeQueryVO" resultMap="BaseResultMap">
        SELECT
            mec.*,
            ( SELECT `name` FROM user_meta WHERE `code` = 'PRODUCT_TYPE' AND val = mec.type ) type_desc,
            ( SELECT `name` FROM user_meta WHERE `code` = 'PRODUCT_STATUS' AND val = mec.`status` ) status_desc,
            ( SELECT nick_name FROM sys_user WHERE id = IFNULL( mec.create_id, mec.update_id ) ) update_name
        FROM
            member_exchange_config mec WHERE 1 = 1
        <if test="memeberExchangeQueryVO.id != null">
            AND mec.id = #{memeberExchangeQueryVO.id}
        </if>
        <if test="memeberExchangeQueryVO.name != null and memeberExchangeQueryVO.name != ''">
            AND mec.`name` LIKE CONCAT('%', #{memeberExchangeQueryVO.name}, '%')
        </if>
        <if test="memeberExchangeQueryVO.shopCode != null and memeberExchangeQueryVO.shopCode != ''">
            AND mec.shop_code LIKE CONCAT('%', #{memeberExchangeQueryVO.shopCode}, '%')
        </if>
        <if test="memeberExchangeQueryVO.skuCode != null and memeberExchangeQueryVO.skuCode != ''">
            AND mec.sku_code LIKE CONCAT('%', #{memeberExchangeQueryVO.skuCode}, '%')
        </if>
        <if test="memeberExchangeQueryVO.time != null">
            AND mec.begin_time &lt;= #{memeberExchangeQueryVO.time} AND mec.end_time &gt;= #{memeberExchangeQueryVO.time}
        </if>
        <if test="memeberExchangeQueryVO.type != null">
            AND mec.type = #{memeberExchangeQueryVO.type}
        </if>
        <if test="memeberExchangeQueryVO.status != null">
            AND mec.`status` = #{memeberExchangeQueryVO.status}
        </if>
        ORDER BY IFNULL(update_time, create_time) DESC
    </select>

</mapper>
