<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.lay.mapper.AppImageInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.lay.model.AppImageInfo">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="profile_photo" property="profilePhoto" />
    </resultMap>
    <update id="randRobot">
        SELECT * FROM app_image_info ORDER BY RAND() LIMIT 1
    </update>

</mapper>
