<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.sys.mapper.SysRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.sys.model.SysRole">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="status_desc" property="statusDesc" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="query" resultMap="BaseResultMap">
        SELECT
            sr.*,
            ( SELECT `name` FROM sys_meta WHERE `code` = 'SYS_STATUS' AND val = sr.`status` ) status_desc,
            ( SELECT nick_name FROM sys_user WHERE id = sr.update_by ) update_name
        FROM
            sys_role sr WHERE 1 = 1
        <if test="param.name != null and param.name != ''">
            AND sr.`name` LIKE CONCAT('%', #{param.name}, '%')
        </if>
        ORDER BY sr.create_time DESC
    </select>

</mapper>
