<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.producttest.mapper.TestEquFirmwareVersionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.producttest.model.TestEquFirmwareVersion">
        <id column="id" property="id" />
        <result column="equip_type_id" property="equipTypeId" />
        <result column="equip_model_id" property="equipModelId" />
        <result column="code" property="code" />
        <result column="version" property="version" />
        <result column="version_address" property="versionAddress" />
        <result column="update_log" property="updateLog" />
        <result column="ota_type" property="otaType" />
        <result column="update_type" property="updateType" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <select id="getPage" resultMap="BaseResultMap" parameterType="com.mrk.yudong.admin.infrastructure.producttest.model.TestEquFirmwareVersion">
        select b.* from (
        SELECT
        DISTINCT equip_model_id,
        id,
        equip_type_id,
        CODE,
        ota_type,
        update_type,
        version,
        version_address,
        update_log,
        is_delete,
        create_time,
        create_by,
        update_time,
        update_by
        FROM
        test_equ_firmware_version
        WHERE
        is_delete = 0
        <if test="equipTypeId != null and equipTypeId != ''">
            and equip_type_id = #{equipTypeId}
        </if>
        <if test="agreement != null and agreement != ''">
            and ota_type = #{agreement}
        </if>
        <if test="isTra == null or isTra==0">
            and is_tra = 0
        </if>
        ORDER BY
        update_time DESC
        ) b
        GROUP BY
        b.equip_model_id
    </select>
    <select id="getNewFirmwareVersion" resultType="com.mrk.yudong.admin.infrastructure.producttest.model.TestEquFirmwareVersion">
        SELECT
            *
        FROM
            test_equ_firmware_version
        WHERE
            equip_model_id = #{modelId}
          AND `code` = #{code}
          AND version = (
            SELECT
                MAX( version ) version
            FROM
                test_equ_firmware_version
            WHERE
                equip_model_id = #{modelId}
              AND `code` = #{code}
            ORDER BY
                version DESC
        )
    </select>
</mapper>
