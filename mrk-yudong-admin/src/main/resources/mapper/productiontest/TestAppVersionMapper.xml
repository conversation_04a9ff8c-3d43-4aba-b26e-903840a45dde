<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.producttest.mapper.TestAppVersionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.producttest.model.TestAppVersion">
        <id column="id" property="id" />
        <result column="terminal" property="terminal" />
        <result column="name" property="name" />
        <result column="version" property="version" />
        <result column="remark" property="remark" />
        <result column="type" property="type" />
        <result column="url" property="url" />
        <result column="is_member_code" property="isMemberCode" />
        <result column="is_open" property="isOpen" />
        <result column="create_id" property="createId" />
        <result column="create_time" property="createTime" />
        <result column="update_id" property="updateId" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <!-- 根据终端获取最新版本信息 -->
    <select id="getTop" resultMap="BaseResultMap">
        SELECT * FROM test_app_version WHERE terminal = #{terminal} ORDER BY create_time DESC LIMIT 1
    </select>

    <!-- 查询版本列表 -->
    <select id="query" parameterType="com.mrk.yudong.admin.infrastructure.producttest.vo.AppVersionQueryVO" resultMap="BaseResultMap">
        SELECT
        av.*,
        ( SELECT `name` FROM user_meta WHERE `code` = 'TERMINAL' AND val = av.terminal ) terminal_desc,
        ( SELECT `name` FROM user_meta WHERE `code` = 'VERSION_TYPE' AND val = av.type ) type_desc,
        ( SELECT nick_name FROM sys_user WHERE id = av.update_id ) update_name
        FROM
        test_app_version av WHERE 1=1
        <if test="appVersionQueryVO.name != null and appVersionQueryVO.name != ''">
            AND av.`name` LIKE CONCAT('%', #{appVersionQueryVO.name}, '%')
        </if>
        <if test="appVersionQueryVO.terminal != null">
            AND av.terminal = #{appVersionQueryVO.terminal}
        </if>
        <if test="appVersionQueryVO.type != null">
            AND av.type = #{appVersionQueryVO.type}
        </if>
        <if test="appVersionQueryVO.sysUserId != null and appVersionQueryVO.sysUserId != ''">
            AND av.update_id = #{appVersionQueryVO.sysUserId}
        </if>
        <if test="appVersionQueryVO.beginTime != null">
            AND av.update_time &gt;= #{appVersionQueryVO.beginTime}
        </if>
        <if test="appVersionQueryVO.endTime != null">
            AND av.update_time &lt;= #{appVersionQueryVO.endTime}
        </if>
        ORDER BY av.id DESC
    </select>

    <!-- 获取选择数据字典 -->
    <select id="option" resultType="com.alibaba.fastjson.JSONObject">
        SELECT av.update_id `value`, ( SELECT nick_name FROM sys_user WHERE id = av.update_id ) `name` FROM test_app_version av GROUP BY av.update_id
    </select>

</mapper>
