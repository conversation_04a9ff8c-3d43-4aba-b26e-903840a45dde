<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.sys.mapper.SysResourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.sys.model.SysResource">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="url" property="url" />
        <result column="method" property="method" />
        <result column="type" property="type" />
        <result column="type_desc" property="typeDesc" />
        <result column="sort" property="sort" />
        <result column="parent_id" property="parentId" />
        <result column="status" property="status" />
        <result column="status_desc" property="statusDesc" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="query" resultMap="BaseResultMap">
        SELECT
            sr.*,
            ( SELECT `name` FROM sys_meta WHERE `code` = 'RESOURCE_TYPE' AND val = sr.type ) type_desc,
            ( SELECT `name` FROM sys_meta WHERE `code` = 'SYS_STATUS' AND val = sr.`status` ) status_desc
        FROM
            sys_resource sr WHERE 1 = 1
        <if test="param.name != null and param.name != ''">
            AND sr.`name` LIKE CONCAT( '%', #{param.name}, '%' )
        </if>
        <if test="param.code != null and param.code != ''">
            AND sr.`code` LIKE CONCAT( '%', #{param.code}, '%' )
        </if>
        <if test="param.type != null">
            AND sr.type = #{param.type}
        </if>
        ORDER BY sr.sort, IFNULL( sr.update_time, sr.create_time ) DESC
    </select>

</mapper>
