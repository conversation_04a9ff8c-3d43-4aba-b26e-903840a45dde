<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.CourseHotwordMapper">

    <select id="query" resultType="com.mrk.yudong.admin.infrastructure.course.model.CourseHotword">
        SELECT ch.*, ( SELECT nick_name FROM sys_user WHERE id = IFNULL(ch.update_id, ch.create_id) ) update_name, IFNULL(ch.update_time, ch.create_time) `time` FROM course_hotword ch WHERE 1 = 1
        <if test="name != null and name != ''">
            AND ch.`name` LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="equipmentId != null">
            AND ch.equipment_id LIKE CONCAT('%', #{equipmentId}, '%')
        </if>
        <if test="stage != null">
            AND ch.stage LIKE CONCAT('%', #{stage}, '%')
        </if>
        <if test="isTra != null">
            AND ch.is_tra &lt;= #{isTra}
        </if>
        ORDER BY ch.id DESC
    </select>

    <select id="queryList" resultType="com.mrk.yudong.admin.infrastructure.course.model.CourseHotword">
        SELECT ch.*, ( SELECT nick_name FROM sys_user WHERE id = IFNULL(ch.update_id, ch.create_id) ) update_name, IFNULL(ch.update_time, ch.create_time) `time` FROM course_hotword ch WHERE 1 = 1
        <if test="name != null and name != ''">
            AND ch.`name` LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="equipmentId != null">
            AND ch.equipment_id LIKE CONCAT('%', #{equipmentId}, '%')
        </if>
        ORDER BY ch.id DESC
    </select>

</mapper>
