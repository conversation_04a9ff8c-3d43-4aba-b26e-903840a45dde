<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.CourseStatusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.course.model.CourseStatus">
        <id column="id" property="id" />
        <result column="course_id" property="courseId" />
        <result column="pre_status" property="preStatus" />
        <result column="status" property="status" />
        <result column="sys_user_id" property="sysUserId" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 获取最新课程状态信息 -->
    <select id="getTop" resultMap="BaseResultMap">
        SELECT * FROM course_status WHERE course_id = #{courseId} ORDER BY create_time DESC LIMIT 1
    </select>

</mapper>
