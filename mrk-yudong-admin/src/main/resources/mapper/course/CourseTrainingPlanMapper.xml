<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.CourseTrainingPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.course.model.CourseTrainingPlan">
        <id column="id" property="id" />
        <result column="equip_type_id" property="equipTypeId" />
        <result column="title" property="title" />
        <result column="subtitle" property="subtitle" />
        <result column="tag_id" property="tagId" />
        <result column="difficulty_id" property="difficultyId" />
        <result column="main_figure" property="mainFigure" />
        <result column="introduction" property="introduction" />
        <result column="advice" property="advice" />
        <result column="graphic_details" property="graphicDetails" />
        <result column="up_time" property="upTime" />
        <result column="down_time" property="downTime" />
        <result column="online_status" property="onlineStatus" />
        <result column="is_long_time" property="isLongTime" />
        <result column="sort" property="sort" />
        <result column="planning_cycle" property="planningCycle" />
        <result column="create_by" property="createBy" />
        <result column="operation_time" property="operationTime" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <resultMap id="countMap" type="com.mrk.yudong.admin.api.course.vo.CourseTrainingPlanVO">
        <result column="online_status" property="onlineStatus" />
        <result column="count" property="count" />
    </resultMap>

    <update id="updateOnlineStatus" >
        update course_training_plan SET online_status = #{oper} where id=#{id}
    </update>

    <select id="getCountList" resultMap="countMap">
        SELECT COUNT( id ) count ,IFNULL(online_status,1) as  online_status FROM course_training_plan WHERE online_status =1
        <if test="isTra != null  and isTra == 0">
            AND is_tra = #{isTra}
        </if>
        UNION ALL
        SELECT COUNT( id ) count ,IFNULL(online_status,2) as  online_status FROM course_training_plan WHERE online_status =2
        <if test="isTra != null  and isTra == 0">
            AND is_tra = #{isTra}
        </if>
        UNION ALL
        SELECT COUNT( id ) count ,IFNULL(online_status,3) as  online_status FROM course_training_plan WHERE online_status =3
        <if test="isTra != null  and isTra == 0">
            AND is_tra = #{isTra}
        </if>
    </select>

</mapper>
