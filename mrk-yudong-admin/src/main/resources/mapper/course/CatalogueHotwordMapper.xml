<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.CatalogueHotwordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.course.model.CatalogueHotword">
        <id column="id" property="id" />
        <result column="course_id" property="courseId" />
        <result column="catalogue_id" property="catalogueId" />
        <result column="hotword_id" property="hotwordId" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="getHotWordList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT ch.id, ch.`name` FROM course_hotword ch WHERE EXISTS ( SELECT 1 FROM catalogue_hotword WHERE hotword_id = ch.id AND catalogue_id = #{courseCatalogueId} )
    </select>

</mapper>
