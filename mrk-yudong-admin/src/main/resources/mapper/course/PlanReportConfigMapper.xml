<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.PlanReportConfigMapper">

    <!-- 分页查询配置列表 -->
    <select id="query" resultType="com.mrk.yudong.admin.infrastructure.course.model.PlanReportConfig">
        SELECT
            prc.*,
            ( SELECT nick_name FROM sys_user WHERE id = prc.update_id ) update_name,
            ( SELECT `name` FROM sys_meta WHERE `code` = 'SYS_STATUS' AND val = prc.`status` ) status_desc
        FROM
            plan_report_config prc WHERE prc.type = #{type}
        ORDER BY prc.id DESC
    </select>

    <!-- 获取数量 -->
    <select id="getNum" resultType="com.alibaba.fastjson.JSONObject">
        SELECT type, COUNT(*) num FROM plan_report_config GROUP BY type
    </select>

</mapper>
