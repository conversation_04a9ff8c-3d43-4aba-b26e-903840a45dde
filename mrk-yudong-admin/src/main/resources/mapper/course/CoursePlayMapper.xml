<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.CoursePlayMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.course.model.CoursePlay">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="course_id" property="courseId" />
        <result column="play_status" property="playStatus" />
        <result column="play_time" property="playTime" />
        <result column="type" property="type" />
        <result column="activity_id" property="activityId" />
        <result column="plan_user_id" property="planUserId" />
        <result column="plan_id" property="planId" />
        <result column="video_type" property="videoType" />
        <result column="course_time" property="courseTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>


    <!-- 根据日期统计总数量 -->
    <select id="sumByDate" resultType="com.alibaba.fastjson.JSONObject">
        SELECT cp.equipment_id equipmentId, COUNT( DISTINCT cp.user_id ) num FROM course_play cp WHERE cp.video_type = 1
        <if test="playerQueryVO.playStatus != null">
            AND cp.play_status = #{playerQueryVO.playStatus}
        </if>
        <if test="playerQueryVO.activityId != null">
            AND cp.activity_id = #{playerQueryVO.activityId}
        </if>
        <if test="playerQueryVO.planId != null">
            AND cp.type = 3 AND cp.plan_id = #{playerQueryVO.planId}
        </if>
        <if test="playerQueryVO.courseType != null">
            AND EXISTS ( SELECT 1 FROM course WHERE id = cp.course_id AND is_tra &lt;= #{playerQueryVO.isTra} AND `type` = #{playerQueryVO.courseType} )
        </if>
        <if test="playerQueryVO.courseId != null">
            AND cp.course_id = #{playerQueryVO.courseId}
        </if>
        <if test="playerQueryVO.coachId != null">
            AND EXISTS ( SELECT 1 FROM course WHERE id = cp.course_id AND is_tra &lt;= #{playerQueryVO.isTra} AND coach_id = #{playerQueryVO.coachId} )
        </if>
        <if test="playerQueryVO.beginDate != null and playerQueryVO.beginDate != ''">
            AND cp.create_time &gt;= CONCAT(#{playerQueryVO.beginDate}, ' 00:00:00')
        </if>
        <if test="playerQueryVO.endDate != null and playerQueryVO.endDate != ''">
            AND cp.create_time &lt;= CONCAT(#{playerQueryVO.endDate}, ' 23:59:59' )
        </if>
        GROUP BY cp.equipment_id
    </select>
</mapper>
