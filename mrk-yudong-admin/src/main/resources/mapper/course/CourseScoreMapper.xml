<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.CourseScoreMapper">

    <!-- 获取课程评分统计 -->
    <select id="getScoreList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT ${column} score, COUNT(*) num FROM course_score WHERE course_id = #{courseId} GROUP BY ${column} ORDER BY score ASC
    </select>

</mapper>
