<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.CourseTagMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.course.model.CourseTag">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="parent_id" property="parentId" />
        <result column="num" property="num" />
        <result column="is_tra" property="isTra" />
        <result column="create_id" property="createId" />
        <result column="create_time" property="createTime" />
        <result column="update_id" property="updateId" />
        <result column="update_name" property="updateName" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 根据父级ID查询标签 -->
    <select id="query" resultMap="BaseResultMap">
        SELECT
            ct.*,
            ( SELECT nick_name FROM sys_user WHERE id = ct.update_id ) update_name,
            ( (SELECT COUNT(*) FROM course_tag_detail WHERE tag_id = ct.id) + (SELECT COUNT(*) FROM course_training_plan WHERE FIND_IN_SET(tag_id, ct.id)) ) num
        FROM
            course_tag ct WHERE ct.parent_id = #{parentId} AND ct.is_tra &lt;= #{isTra} ORDER BY ct.id DESC
    </select>

</mapper>
