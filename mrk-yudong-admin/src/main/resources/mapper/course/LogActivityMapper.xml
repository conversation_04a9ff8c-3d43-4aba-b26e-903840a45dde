<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.LogActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.course.model.LogActivity">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="activity_id" property="activityId" />
        <result column="type" property="type" />
        <result column="channel" property="channel" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 获取活动PV总统计数据 -->
    <select id="getActivitySumPV" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            COUNT(*) sumNum,
            IFNULL(SUM( CASE WHEN la.channel = 1 THEN 1 ELSE 0 END ), 0) homeNum,
            IFNULL(SUM( CASE WHEN la.channel = 2 THEN 1 ELSE 0 END ), 0) appNum,
            IFNULL(SUM( CASE WHEN la.channel = 3 THEN 1 ELSE 0 END ), 0) bannerNum
        FROM log_activity la WHERE la.type = 2 AND la.activity_id = #{activityId}
        <if test="beginDate != null">
            AND la.create_time &gt;= CONCAT(#{beginDate}, ' 00:00:00')
        </if>
        <if test="endDate != null">
            AND la.create_time &lt;= CONCAT(#{endDate}, ' 23:59:59')
        </if>
    </select>

    <!-- 获取活动UV总统计数据 -->
    <select id="getActivitySumUV" resultType="com.alibaba.fastjson.JSONObject">
        SELECT COUNT( DISTINCT user_id ) FROM log_activity la WHERE la.type = 2 AND la.activity_id = #{activityId}
        <if test="beginDate != null">
            AND la.create_time &gt;= CONCAT(#{beginDate}, ' 00:00:00')
        </if>
        <if test="endDate != null">
            AND la.create_time &lt;= CONCAT(#{endDate}, ' 23:59:59')
        </if>
    </select>

    <!-- 获取列表分类列表 -->
    <select id="getPVList" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            DATE_FORMAT(la.create_time, '%Y-%m-%d') joinTime,
            IFNULL(SUM( CASE WHEN la.channel = 1 THEN 1 ELSE 0 END ), 0) homeNum,
            IFNULL(SUM( CASE WHEN la.channel = 2 THEN 1 ELSE 0 END ), 0) appNum,
            IFNULL(SUM( CASE WHEN la.channel = 3 THEN 1 ELSE 0 END ), 0) bannerNum
        FROM log_activity la WHERE la.type = 2 AND la.activity_id = #{activityId}
        <if test="beginDate != null">
            AND la.create_time &gt;= CONCAT(#{beginDate}, ' 00:00:00')
        </if>
        <if test="endDate != null">
            AND la.create_time &lt;= CONCAT(#{endDate}, ' 23:59:59')
        </if>
        GROUP BY joinTime ORDER BY joinTime DESC
    </select>

</mapper>
