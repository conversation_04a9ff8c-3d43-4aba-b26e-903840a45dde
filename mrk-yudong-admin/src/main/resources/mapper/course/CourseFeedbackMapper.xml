<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.CourseFeedbackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.course.model.CourseFeedback">
        <id column="id" property="id" />
        <result column="course_id" property="courseId" />
        <result column="user_id" property="userId" />
        <result column="type" property="type" />
        <result column="play_status" property="playStatus" />
        <result column="content" property="content" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 查询课程反馈列表 -->
    <select id="query" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
            ( SELECT `name` FROM course_meta WHERE `code` = 'FEED_BACK_TYPE' AND val = cf.type ) typeDesc,
            cf.content,
            COUNT(*) num
        FROM
            course_feedback cf WHERE cf.course_id = #{courseId}
        <if test="type != null">
            AND cf.type = #{type}
        </if>
        GROUP BY cf.type, cf.content ORDER BY cf.create_time DESC
    </select>

</mapper>
