<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.CourseLinkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.course.model.CourseLink">
        <id column="id" property="id"/>
        <result column="course_id" property="courseId"/>
        <result column="catalogue_id" property="catalogueId"/>
        <result column="name" property="name"/>
        <result column="crux" property="crux"/>
        <result column="begin_desc" property="beginDesc"/>
        <result column="begin_time" property="beginTime"/>
        <result column="sustain_time" property="sustainTime"/>
        <result column="end_time" property="endTime"/>
        <result column="min_num" property="minNum"/>
        <result column="max_num" property="maxNum"/>
        <result column="advise_num" property="adviseNum"/>
        <result column="slope_num" property="slopeNum"/>
        <result column="distance" property="distance"/>
        <result column="kcal" property="kcal"/>
        <result column="music_name" property="musicName"/>
        <result column="music_time" property="musicTime"/>
        <result column="sort" property="sort"/>
        <result column="create_id" property="createId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="CourseLinkPOResultMap" type="com.mrk.yudong.share.po.CourseLinkPO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="crux" property="crux"/>
        <result column="begin_desc" property="beginDesc"/>
        <result column="sustain_time" property="sustainTime"/>
        <result column="min_num" property="minNum"/>
        <result column="max_num" property="maxNum"/>
        <result column="advise_num" property="adviseNum"/>
        <result column="slope_num" property="slopeNum"/>
        <result column="distance" property="distance"/>
        <result column="kcal" property="kcal"/>
        <result column="music_name" property="musicName"/>
        <result column="music_time" property="musicTime"/>
    </resultMap>

    <!-- 根据课程ID和环节ID获取课程小节信息列表 -->
    <select id="list" resultMap="CourseLinkPOResultMap">
        SELECT id,
               `name`,
               crux,
               begin_desc,
               sustain_time,
               min_num,
               max_num,
               advise_num,
               slope_num,
               distance,
               kcal,
               music_name,
               music_time
        FROM course_link
        WHERE course_id = #{courseId}
          AND catalogue_id = #{catalogueId}
        ORDER BY sort ASC
    </select>

</mapper>
