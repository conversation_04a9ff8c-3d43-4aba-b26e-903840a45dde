<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.CourseCatalogueMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.course.model.CourseCatalogue">
        <id column="id" property="id"/>
        <result column="course_id" property="courseId"/>
        <result column="title" property="title"/>
        <result column="name" property="name"/>
        <result column="kcal" property="kcal"/>
        <result column="begin_time" property="beginTime"/>
        <result column="end_time" property="endTime"/>
        <result column="sort" property="sort"/>
        <result column="create_id" property="createId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="CourseCataloguePOResultMap" type="com.mrk.yudong.share.po.CourseCataloguePO">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="name" property="name"/>
        <result column="kcal" property="kcal"/>
        <result column="describe_info" property="describeInfo"/>
    </resultMap>

    <!-- 根据课程ID获取课程教案环节信息列表 -->
    <select id="listByCourseId" resultMap="CourseCataloguePOResultMap">
        SELECT id, title, `name`, kcal, describe_info
        FROM course_catalogue
        WHERE course_id = #{courseId}
        ORDER BY sort ASC
    </select>

</mapper>
