<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.CourseMapper">

    <resultMap id="CoursePOResultMap" type="com.mrk.yudong.admin.api.course.po.CoursePO">
        <id column="id" property="courseId"/>
        <result column="name" property="name"/>
        <result column="is_vip" property="isVip"/>
        <result column="type_desc" property="typeDesc"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="model_name" property="modelName"/>
        <result column="cover" property="cover"/>
        <result column="coach_id" property="coachId"/>
        <result column="coach_name" property="coachName"/>
        <result column="live_time" property="liveTime"/>
        <result column="course_time" property="courseTime"/>
        <result column="grade_desc" property="gradeDesc"/>
        <result column="kcal" property="kcal"/>
        <result column="review_remark" property="reviewRemark"/>
        <result column="status" property="status"/>
        <result column="status_desc" property="statusDesc"/>
        <result column="make_num" property="makeNum"/>
        <result column="line_status" property="lineStatus"/>
        <result column="line_status_desc" property="lineStatusDesc"/>
        <result column="line_msg" property="lineMsg"/>
        <result column="collect_num" property="collectNum"/>
        <result column="share_num" property="shareNum"/>
        <result column="play_num" property="playNum"/>
        <result column="course_num" property="courseNum"/>
        <result column="live_play_num" property="livePlayNum"/>
        <result column="pv_num" property="pvNum"/>
        <result column="uv_num" property="uvNum"/>
        <result column="update_name" property="updateName"/>
        <result column="update_time" property="updateTime"/>
        <result column="distance" property="distance"/>
        <result column="category_id" property="categoryId"/>
    </resultMap>

    <resultMap id="CourseDetailPOResultMap" type="com.mrk.yudong.admin.api.course.po.CourseDetailPO">
        <id column="id" property="courseId"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="brand_type" property="brandType"/>
        <result column="type_desc" property="typeDesc"/>
        <result column="is_vip" property="isVip"/>
        <result column="vip_type" property="vipType"/>
        <result column="equipment_type" property="equipmentType"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="model_id" property="modelId"/>
        <result column="model_name" property="modelName"/>
        <result column="cover" property="cover"/>
        <result column="introduce" property="introduce"/>
        <result column="part" property="part"/>
        <result column="crowd" property="crowd"/>
        <result column="taboo" property="taboo"/>
        <result column="coach_id" property="coachId"/>
        <result column="coach_name" property="coachName"/>
        <result column="live_time" property="liveTime"/>
        <result column="is_free" property="isFree"/>
        <result column="free_time" property="freeTime"/>
        <result column="course_time" property="courseTime"/>
        <result column="hot_word" property="hotWord"/>
        <result column="grade" property="grade"/>
        <result column="grade_desc" property="gradeDesc"/>
        <result column="status" property="status"/>
        <result column="status_desc" property="statusDesc"/>
        <result column="kcal" property="kcal"/>
        <result column="distance" property="distance"/>
        <result column="speed" property="speed"/>
        <result column="review_remark" property="reviewRemark"/>
        <result column="video_id" property="videoId"/>
        <result column="video_media_type" property="videoMediaType"/>
        <result column="music_name" property="musicName"/>
        <result column="sort" property="sort"/>
        <result column="channel" property="channel"/>
        <result column="race_target" property="raceTarget"/>
        <result column="race_target_tip" property="raceTargetTip"/>
        <result column="preview_video_generation_type" property="previewVideoGenerationType"/>
        <result column="preview_video" property="previewVideo"/>
        <result column="category_id" property="categoryId"/>
        <result column="is_tra" property="isTra"/>
    </resultMap>

    <resultMap id="TodayLivePOResultMap" type="com.mrk.yudong.admin.api.course.po.TodayLivePO">
        <id column="id" property="courseId"/>
        <result column="name" property="name"/>
        <result column="cover" property="cover"/>
        <result column="is_vip" property="isVip"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="icon" property="icon"/>
        <result column="tag_icon" property="tagIcon"/>
        <result column="live_icon" property="liveIcon"/>
        <result column="coach_name" property="coachName"/>
        <result column="live_time" property="liveTime"/>
        <result column="course_time" property="courseTime"/>
        <result column="grade_desc" property="gradeDesc"/>
        <result column="kcal" property="kcal"/>
        <result column="status" property="status"/>
    </resultMap>

    <resultMap id="PlanCoursePOResultMap" type="com.mrk.yudong.admin.api.course.po.PlanCoursePO">
        <id column="id" property="courseId"/>
        <result column="name" property="name"/>
        <result column="cover" property="cover"/>
        <result column="type_name" property="typeName"/>
        <result column="coach_name" property="coachName"/>
        <result column="live_time" property="liveTime"/>
        <result column="grade_desc" property="gradeDesc"/>
        <result column="status_desc" property="statusDesc"/>
        <result column="vip_desc" property="vipDesc"/>
    </resultMap>

    <resultMap id="CourseOptionBO" type="com.mrk.yudong.admin.biz.course.bo.CourseOptionBO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="cover" property="cover"/>
        <result column="coach_id" property="coachId"/>
        <result column="coach_name" property="coachName"/>
        <result column="course_time" property="courseTime"/>
        <result column="equipment_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="grade" property="grade"/>
        <result column="grade_desc" property="gradeDesc"/>
        <result column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
    </resultMap>

    <update id="updateActivityId">
        UPDATE course
        SET activity_id=null
        WHERE
        id in(
        <foreach collection="ids" separator="," item="item">
            #{item}
        </foreach>
        );
    </update>

    <!-- 重新开播 -->
    <update id="updateCourseStatus">
        UPDATE course
        SET `status`         = #{status},
            live_time        = #{liveTime},
            actual_live_time = NULL,
            race_status      = 0
        WHERE id = #{courseId}
    </update>

    <!-- 分页查询课程列表 -->
    <select id="query" parameterType="com.mrk.yudong.admin.api.course.vo.CourseQueryVO" resultMap="CoursePOResultMap">
        SELECT
        tmp.id,
        tmp.`name`,
        tmp.type,
        tmp.cover,
        tmp.is_vip,
        tmp.coach_id,
        tmp.live_time,
        tmp.course_time,
        tmp.kcal,
        tmp.review_start_time,
        tmp.review_remark,
        tmp.review_end_time,
        tmp.`status`,
        tmp.make_num,
        ( SELECT `name` FROM course_meta WHERE `code` = 'COURSE_TYPE' AND val = tmp.type ) type_desc,
        tmp.equipment_id,
        ( SELECT type_name FROM equ_equipment_type WHERE id = tmp.equipment_id ) equipment_name,
        ( SELECT type_name FROM equ_equipment_type WHERE id = tmp.model_id ) model_name,
        ( SELECT `name` FROM coach_info WHERE id = tmp.coach_id ) coach_name,
        ( SELECT `name` FROM course_meta WHERE (CASE WHEN tmp.type = 1 THEN `code` = 'COURSE_LEVEL' ELSE `code` =
        'COURSE_MERIT_LEVEL' END) AND val = tmp.grade ) grade_desc,
        ( CASE WHEN tmp.line_status = 1 THEN ( SELECT `name` FROM course_meta WHERE `code` = 'COURSE_STATUS' AND val =
        tmp.`status` ) ELSE ( SELECT `name` FROM course_meta WHERE `code` = 'COURSE_LINE_STATUS' AND val =
        tmp.line_status ) END ) status_desc,
        tmp.line_status,
        ( SELECT `name` FROM course_meta WHERE `code` = 'COURSE_LINE_STATUS' AND val = tmp.line_status )
        line_status_desc,
        tmp.line_msg,
        tmp.share_num,
        tmp.live_play_num,
        ( SELECT nick_name FROM sys_user WHERE id = tmp.update_id ) update_name,
        tmp.update_time,
        tmp.sort,
        tmp.distance,
        tmp.category_id
        FROM ( WITH a AS ( SELECT c.*, ( SELECT COUNT(*) FROM course_make WHERE course_id = c.id ) make_num FROM course
        c ) SELECT * FROM a WHERE 1 = 1
        <if test="courseQueryVO.isTra != null">
            AND a.is_tra &lt;= #{courseQueryVO.isTra}
        </if>
        <if test="courseQueryVO.channel != null">
            AND a.channel = #{courseQueryVO.channel}
        </if>
        <if test="courseQueryVO.minCourseTime != null">
            AND a.course_time &gt;= #{courseQueryVO.minCourseTime}
        </if>
        <if test="courseQueryVO.maxCourseTime != null">
            AND a.course_time &lt;= #{courseQueryVO.maxCourseTime}
        </if>
        <choose>
            <when test="courseQueryVO.searchType == 3 or courseQueryVO.searchType == 5">
                <if test="courseQueryVO.channel != null and courseQueryVO.channel == 1">
                    AND a.`status` NOT IN (0, 10, 20)
                </if>
                <choose>
                    <when test="courseQueryVO.status != null and courseQueryVO.status == 50">
                        AND a.`status` &gt;= #{courseQueryVO.status}
                    </when>
                    <when test="courseQueryVO.status != null and courseQueryVO.status == 40">
                        AND a.line_status = 1 AND a.`status` &gt;= 35 AND a.`status` &lt;= #{courseQueryVO.status}
                    </when>
                    <otherwise>
                        <if test="courseQueryVO.status != null">
                            AND a.line_status = 1 AND a.`status` = #{courseQueryVO.status}
                        </if>
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                <if test="courseQueryVO.searchType == 1">
                    AND a.`status` &lt;= 20
                </if>
                <if test="courseQueryVO.searchType == 4">
                    AND a.`status` &gt;= 30
                </if>
                <if test="courseQueryVO.status != null">
                    AND a.`status` = #{courseQueryVO.status}
                </if>
            </otherwise>
        </choose>
        <choose>
            <when test="courseQueryVO.searchType != 5">
                AND a.line_status = 1
            </when>
            <otherwise>
                <if test="courseQueryVO.line != null">
                    AND a.line_status = #{courseQueryVO.line}
                </if>
            </otherwise>
        </choose>
        <if test="courseQueryVO.grade != null">
            AND a.grade = #{courseQueryVO.grade}
        </if>
        <if test="courseQueryVO.categoryId != null">
            AND a.category_id = #{courseQueryVO.categoryId}
        </if>
        <if test="courseQueryVO.type != null">
            AND a.type = #{courseQueryVO.type}
        </if>
        <if test="courseQueryVO.isVip != null">
            AND a.is_vip = #{courseQueryVO.isVip}
        </if>
        <if test="courseQueryVO.coachId != null">
            AND a.coach_id = #{courseQueryVO.coachId}
        </if>
        <if test="courseQueryVO.courseId != null">
            AND a.id = #{courseQueryVO.courseId}
        </if>
        <if test="courseQueryVO.createId != null">
            AND a.create_id = #{courseQueryVO.createId}
        </if>
        <if test="courseQueryVO.equipmentId != null and courseQueryVO.equipmentId != 3">
            AND a.equipment_id = #{courseQueryVO.equipmentId}
        </if>
        <if test="courseQueryVO.equipmentId != null and courseQueryVO.equipmentId == 3">
            AND a.equipment_id IN
            <foreach collection="courseQueryVO.equipmentIds" item="equipmentId" open="(" separator="," close=")">
                #{equipmentId}
            </foreach>
        </if>
        <if test="courseQueryVO.name != null and courseQueryVO.name != ''">
            AND a.`name` LIKE CONCAT('%', #{courseQueryVO.name}, '%')
        </if>
        <if test="courseQueryVO.beginTime != null">
            AND a.live_time &gt;= #{courseQueryVO.beginTime}
        </if>
        <if test="courseQueryVO.endTime != null">
            AND a.live_time &lt;= #{courseQueryVO.endTime}
        </if>
        ) tmp WHERE 1 = 1
        <if test="courseQueryVO.minNum != null">
            AND tmp.make_num &gt;= #{courseQueryVO.minNum}
        </if>
        <if test="courseQueryVO.maxNum != null">
            AND tmp.make_num &lt;= #{courseQueryVO.maxNum}
        </if>
        <if test="courseQueryVO.tagId != null">
            AND EXISTS (SELECT 1 FROM course_tag_detail WHERE course_id = tmp.id AND tag_id = #{courseQueryVO.tagId})
        </if>
        <if test="courseQueryVO.videoMediaType != null">
            AND tmp.video_media_type = #{courseQueryVO.videoMediaType}
        </if>
        ORDER BY
        <if test="courseQueryVO.searchType == 1">
            IFNULL(tmp.review_end_time, tmp.review_start_time) DESC
        </if>
        <if test="courseQueryVO.searchType == 2">
            tmp.review_start_time ASC
        </if>
        <if test="courseQueryVO.searchType == 3 and courseQueryVO.status != 50">
            tmp.live_time ASC
        </if>
        <if test="courseQueryVO.searchType == 3 and courseQueryVO.status == 50">
            tmp.live_time DESC
        </if>
        <if test="courseQueryVO.searchType == 4">
            tmp.line_status DESC, tmp.live_time DESC
        </if>
        <if test="courseQueryVO.searchType == 5">
            <if test="courseQueryVO.channel != null and courseQueryVO.channel == 1">
                tmp.line_status DESC
                <if test="courseQueryVO.status == null">
                    , tmp.review_end_time DESC
                </if>
                <if test="courseQueryVO.status != null and courseQueryVO.status == 50">
                    , tmp.live_time DESC
                </if>
                <if test="courseQueryVO.status != null and courseQueryVO.status != 50">
                    , tmp.live_time ASC
                </if>
            </if>
            <if test="courseQueryVO.channel != null and courseQueryVO.channel == 2">
                tmp.create_time DESC
            </if>
            <if test="courseQueryVO.channel != null and courseQueryVO.channel == 3">
                tmp.line_status DESC, tmp.sort ASC, IFNULL(tmp.update_time, tmp.create_time) DESC
            </if>
        </if>
    </select>

    <!-- 课程详情 -->
    <select id="detail" resultMap="CourseDetailPOResultMap">
        SELECT c.id,
               c.`name`,
               c.type,
               c.brand_type,
               c.is_vip,
               c.vip_type,
               c.equipment_type,
               (SELECT `name` FROM course_meta WHERE `code` = 'COURSE_TYPE' AND val = c.type)       type_desc,
               c.equipment_id,
               (SELECT type_name FROM equ_equipment_type WHERE id = c.equipment_id)                 equipment_name,
               c.model_id,
               (SELECT type_name FROM equ_equipment_type WHERE id = c.model_id)                     model_name,
               c.cover,
               c.coach_id,
               (SELECT `name` FROM coach_info WHERE id = c.coach_id)                                coach_name,
               c.live_time,
               c.is_free,
               c.free_time,
               c.course_time,
               c.introduce,
               c.part,
               c.crowd,
               c.taboo,
               c.hot_words                                                                          hot_word,
               c.grade,
               (SELECT `name`
                FROM course_meta
                WHERE (CASE WHEN c.type = 1 THEN `code` = 'COURSE_LEVEL' ELSE `code` = 'COURSE_MERIT_LEVEL' END)
                  AND val = c.grade)                                                                grade_desc,
               c.`status`,
               (SELECT `name` FROM course_meta WHERE `code` = 'COURSE_STATUS' AND val = c.`status`) status_desc,
               c.kcal,
               c.distance,
               c.speed,
               c.review_remark,
               c.video_id,
               c.video_media_type,
               c.music_name,
               c.channel,
               c.sort,
               c.race_target,
               c.race_target_tip,
               c.preview_video_generation_type,
               c.preview_video,
               c.category_id,
               c.is_tra
        FROM course c
        WHERE c.id = #{courseId}
    </select>

    <!-- 查询课程列表 -->
    <select id="queryMap" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        tmp.id,
        tmp.`name`,
        tmp.type,
        tmp.cover,
        tmp.coach_id,
        DATE_FORMAT(tmp.live_time, '%Y-%m-%d %H:%i:%s') live_time,
        tmp.course_time,
        tmp.kcal,
        tmp.review_start_time,
        tmp.review_remark,
        tmp.review_end_time,
        tmp.make_num,
        ( SELECT `name` FROM course_meta WHERE `code` = 'COURSE_TYPE' AND val = tmp.type ) type_desc,
        ( SELECT type_name FROM equ_equipment_type WHERE id = tmp.equipment_id ) equipment_name,
        ( SELECT type_name FROM equ_equipment_type WHERE id = tmp.model_id ) model_name,
        ( SELECT `name` FROM coach_info WHERE id = tmp.coach_id ) coach_name,
        ( SELECT `name` FROM course_meta WHERE (CASE WHEN tmp.type = 1 THEN `code` = 'COURSE_LEVEL' ELSE `code` =
        'COURSE_MERIT_LEVEL' END) AND val = tmp.grade ) grade_desc,
        ( CASE WHEN tmp.line_status = 1 THEN ( SELECT `name` FROM course_meta WHERE `code` = 'COURSE_STATUS' AND val =
        tmp.`status` ) ELSE ( SELECT `name` FROM course_meta WHERE `code` = 'COURSE_LINE_STATUS' AND val =
        tmp.line_status ) END ) status_desc
        FROM ( WITH a AS ( SELECT c.*, ( SELECT COUNT(*) FROM course_make WHERE course_id = c.id ) make_num FROM course
        c ) SELECT * FROM a WHERE 1 = 1
        <if test="courseQueryVO.isTra != null">
            AND a.is_tra &lt;= #{courseQueryVO.isTra}
        </if>
        <if test="courseQueryVO.channel != null">
            AND a.channel = #{courseQueryVO.channel}
        </if>
        <choose>
            <when test="courseQueryVO.searchType == 3 or courseQueryVO.searchType == 5">
                <if test="courseQueryVO.channel != null and courseQueryVO.channel == 1">
                    AND a.`status` NOT IN (0, 10, 20)
                </if>
                <choose>
                    <when test="courseQueryVO.status != null and courseQueryVO.status == 50">
                        AND a.`status` &gt;= #{courseQueryVO.status}
                    </when>
                    <when test="courseQueryVO.status != null and courseQueryVO.status == 40">
                        AND a.line_status = 1 AND a.`status` &gt;= 35 AND a.`status` &lt;= #{courseQueryVO.status}
                    </when>
                    <otherwise>
                        <if test="courseQueryVO.status != null">
                            AND a.line_status = 1 AND a.`status` = #{courseQueryVO.status}
                        </if>
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                <if test="courseQueryVO.searchType == 1">
                    AND a.`status` &lt;= 20
                </if>
                <if test="courseQueryVO.searchType == 4">
                    AND a.`status` &gt;= 30
                </if>
                <if test="courseQueryVO.status != null">
                    AND a.`status` = #{courseQueryVO.status}
                </if>
            </otherwise>
        </choose>
        <choose>
            <when test="courseQueryVO.searchType != 5">
                AND a.line_status = 1
            </when>
            <otherwise>
                <if test="courseQueryVO.line != null">
                    AND a.line_status = #{courseQueryVO.line}
                </if>
            </otherwise>
        </choose>
        <if test="courseQueryVO.grade != null">
            AND a.grade = #{courseQueryVO.grade}
        </if>
        <if test="courseQueryVO.type != null">
            AND a.type = #{courseQueryVO.type}
        </if>
        <if test="courseQueryVO.isVip != null">
            AND a.is_vip = #{courseQueryVO.isVip}
        </if>
        <if test="courseQueryVO.coachId != null">
            AND a.coach_id = #{courseQueryVO.coachId}
        </if>
        <if test="courseQueryVO.courseId != null">
            AND a.id = #{courseQueryVO.courseId}
        </if>
        <if test="courseQueryVO.createId != null">
            AND a.create_id = #{courseQueryVO.createId}
        </if>
        <if test="courseQueryVO.equipmentId != null and courseQueryVO.equipmentId != 3">
            AND a.equipment_id = #{courseQueryVO.equipmentId}
        </if>
        <if test="courseQueryVO.equipmentId != null and courseQueryVO.equipmentId == 3">
            AND a.equipment_id IN
            <foreach collection="courseQueryVO.equipmentIds" item="equipmentId" open="(" separator="," close=")">
                #{equipmentId}
            </foreach>
        </if>
        <if test="courseQueryVO.name != null and courseQueryVO.name != ''">
            AND a.`name` LIKE CONCAT('%', #{courseQueryVO.name}, '%')
        </if>
        <if test="courseQueryVO.beginTime != null">
            AND a.live_time &gt;= #{courseQueryVO.beginTime}
        </if>
        <if test="courseQueryVO.endTime != null">
            AND a.live_time &lt;= #{courseQueryVO.endTime}
        </if>
        ) tmp WHERE 1 = 1
        <if test="courseQueryVO.minNum != null">
            AND tmp.make_num &gt;= #{courseQueryVO.minNum}
        </if>
        <if test="courseQueryVO.maxNum != null">
            AND tmp.make_num &lt;= #{courseQueryVO.maxNum}
        </if>
        <if test="courseQueryVO.videoMediaType != null">
            AND tmp.video_media_type = #{courseQueryVO.videoMediaType}
        </if>
        ORDER BY
        <if test="courseQueryVO.searchType == 1">
            IFNULL(tmp.review_end_time, tmp.review_start_time) DESC
        </if>
        <if test="courseQueryVO.searchType == 2">
            tmp.review_start_time ASC
        </if>
        <if test="courseQueryVO.searchType == 3 and courseQueryVO.status != 50">
            tmp.live_time ASC
        </if>
        <if test="courseQueryVO.searchType == 3 and courseQueryVO.status == 50">
            tmp.live_time DESC
        </if>
        <if test="courseQueryVO.searchType == 5">
            <if test="courseQueryVO.channel != null and courseQueryVO.channel == 1">
                tmp.line_status DESC
                <if test="courseQueryVO.status == null">
                    , tmp.review_end_time DESC
                </if>
                <if test="courseQueryVO.status != null and courseQueryVO.status == 50">
                    , tmp.live_time DESC
                </if>
                <if test="courseQueryVO.status != null and courseQueryVO.status != 50">
                    , tmp.live_time ASC
                </if>
            </if>
            <if test="courseQueryVO.channel != null and courseQueryVO.channel == 2">
                tmp.create_time DESC
            </if>
        </if>
    </select>

    <!-- 查询今日直播课 -->
    <select id="queryTodayLive" resultMap="TodayLivePOResultMap">
        SELECT c.id,
               c.`name`,
               c.cover,
               c.is_vip,
               (SELECT type_name FROM equ_equipment_type WHERE id = c.equipment_id)   equipment_name,
               (SELECT icon_images FROM equ_equipment_type WHERE id = c.equipment_id) icon,
               (SELECT tag_icon FROM equ_equipment_type WHERE id = c.equipment_id)    tag_icon,
               (SELECT live_icon FROM equ_equipment_type WHERE id = c.equipment_id)   live_icon,
               (SELECT `name` FROM coach_info WHERE id = c.coach_id)                  coach_name,
               c.live_time,
               c.course_time,
               (SELECT `name`
                FROM course_meta
                WHERE (CASE WHEN c.type = 1 THEN `code` = 'COURSE_LEVEL' ELSE `code` = 'COURSE_MERIT_LEVEL' END)
                  AND val = c.grade)                                                  grade_desc,
               c.kcal,
               c.`status`
        FROM course c
        WHERE c.is_tra &lt;= #{isTra}
          AND c.channel = #{channel}
          AND c.`status` IN (30, 35, 40)
          AND c.live_time &gt;= CONCAT(DATE(NOW()), ' 00:00:00')
          AND c.live_time &lt;= CONCAT(DATE(NOW()), ' 23:59:59')
          AND c.line_status = 1
        ORDER BY c.`status` DESC, c.live_time ASC
    </select>

    <!-- 审核记录列表 -->
    <select id="reviewList" parameterType="com.mrk.yudong.admin.api.course.vo.ReviewQueryVO" resultMap="CoursePOResultMap">
        SELECT
        c.id,
        c.`name`,
        c.cover,
        ( SELECT `name` FROM course_meta WHERE `code` = 'COURSE_TYPE' AND val = c.type ) type_desc,
        ( SELECT type_name FROM equ_equipment_type WHERE id = c.equipment_id ) equipment_name,
        ( SELECT type_name FROM equ_equipment_type WHERE id = c.model_id ) model_name,
        ( SELECT `name` FROM coach_info WHERE id = c.coach_id ) coach_name,
        DATE_FORMAT( c.live_time, '%Y-%m-%d %H:%i:%s' ) live_time,
        c.course_time,
        ( SELECT `name` FROM course_meta WHERE ( CASE WHEN c.type = 1 THEN `code` = 'COURSE_LEVEL' ELSE `code` =
        'COURSE_MERIT_LEVEL' END ) AND val = c.grade
        ) grade_desc,
        c.kcal,
        c.review_remark,
        ( CASE WHEN cr.review = 0 THEN '拒绝' ELSE '通过' END ) status_desc
        FROM
        ( WITH review AS ( SELECT a.*, ROW_NUMBER() OVER ( PARTITION BY a.course_id ORDER BY a.create_time DESC ) rn
        FROM course_review a ) SELECT * FROM review WHERE rn = 1 ) cr LEFT JOIN course c ON cr.course_id = c.id WHERE 1
        = 1
        <if test="reviewQueryVO.isTra != null">
            AND c.is_tra &lt;= #{reviewQueryVO.isTra}
        </if>
        <if test="reviewQueryVO.channel != null">
            AND c.channel = #{reviewQueryVO.channel}
        </if>
        <if test="reviewQueryVO.courseId != null">
            AND c.id = #{reviewQueryVO.courseId}
        </if>
        <if test="reviewQueryVO.equipmentId != null">
            AND c.equipment_id = #{reviewQueryVO.equipmentId}
        </if>
        <if test="reviewQueryVO.coachId != null">
            AND c.coach_id = #{reviewQueryVO.coachId}
        </if>
        <if test="reviewQueryVO.type != null">
            AND c.type = #{reviewQueryVO.type}
        </if>
        <if test="reviewQueryVO.grade != null">
            AND c.grade = #{reviewQueryVO.grade}
        </if>
        <if test="reviewQueryVO.name != null">
            AND c.name LIKE CONCAT('%', #{reviewQueryVO.name}, '%')
        </if>
        <if test="reviewQueryVO.status != null">
            AND cr.review = #{reviewQueryVO.status}
        </if>
        ORDER BY cr.create_time DESC
    </select>

    <!-- 审核课程导出 -->
    <select id="reviewExport" parameterType="com.mrk.yudong.admin.api.course.vo.ReviewQueryVO"
            resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        c.id,
        c.`name`,
        ( SELECT `name` FROM course_meta WHERE `code` = 'COURSE_TYPE' AND val = c.type ) type_desc,
        ( SELECT type_name FROM equ_equipment_type WHERE id = c.equipment_id ) equipment_name,
        ( SELECT type_name FROM equ_equipment_type WHERE id = c.model_id ) model_name,
        ( SELECT `name` FROM coach_info WHERE id = c.coach_id ) coach_name,
        DATE_FORMAT( c.live_time, '%Y-%m-%d %H:%i:%s' ) live_time,
        c.course_time,
        ( SELECT `name` FROM course_meta WHERE ( CASE WHEN c.type = 1 THEN `code` = 'COURSE_LEVEL' ELSE `code` =
        'COURSE_MERIT_LEVEL' END ) AND val = c.grade ) grade_desc,
        c.kcal,
        c.review_remark,
        ( CASE WHEN cr.review = 0 THEN '拒绝' ELSE '通过' END ) status_desc
        FROM
        ( WITH review AS ( SELECT a.*, ROW_NUMBER() OVER ( PARTITION BY a.course_id ORDER BY a.create_time DESC ) rn
        FROM course_review a ) SELECT * FROM review WHERE rn = 1 ) cr
        LEFT JOIN course c ON cr.course_id = c.id WHERE 1 = 1
        <if test="reviewQueryVO.isTra != null">
            AND c.is_tra &lt;= #{reviewQueryVO.isTra}
        </if>
        <if test="reviewQueryVO.channel != null">
            AND c.channel = #{reviewQueryVO.channel}
        </if>
        <if test="reviewQueryVO.courseId != null">
            AND c.id = #{reviewQueryVO.courseId}
        </if>
        <if test="reviewQueryVO.equipmentId != null">
            AND c.equipment_id = #{reviewQueryVO.equipmentId}
        </if>
        <if test="reviewQueryVO.coachId != null">
            AND c.coach_id = #{reviewQueryVO.coachId}
        </if>
        <if test="reviewQueryVO.type != null">
            AND c.type = #{reviewQueryVO.type}
        </if>
        <if test="reviewQueryVO.grade != null">
            AND c.grade = #{reviewQueryVO.grade}
        </if>
        <if test="reviewQueryVO.status != null">
            AND cr.review = #{reviewQueryVO.status}
        </if>
        ORDER BY cr.create_time DESC
    </select>

    <!-- 课程主题查询课程列表 -->
    <select id="themeCourseQuery" parameterType="com.mrk.yudong.admin.api.course.vo.ThemeCourseQueryVO"
            resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        c.id,
        c.`name`,
        c.cover,
        ( SELECT type_name FROM equ_equipment_type WHERE id = c.equipment_id ) equipmentName,
        ( SELECT `name` FROM coach_info WHERE id = c.coach_id ) coachName,
        ( SELECT `name` FROM course_meta WHERE ( CASE WHEN c.type = 1 THEN `code` = 'COURSE_LEVEL' ELSE `code` =
        'COURSE_MERIT_LEVEL' END ) AND val = c.grade ) gradeDesc,
        c.course_time courseTime
        FROM
        course c WHERE c.is_tra &lt;= #{themeCourseQueryVO.isTra} AND c.line_status = 1
        <if test="themeCourseQueryVO.searchType != null and themeCourseQueryVO.searchType == 0">
            AND c.`status` = 60 AND ( ( c.channel = 1 AND DATE( c.live_time ) &lt;= DATE_SUB( CURDATE(), INTERVAL 1 DAY
            ) ) OR c.channel = 2 )
        </if>
        <if test="themeCourseQueryVO.searchType != null and themeCourseQueryVO.searchType == 1">
            AND c.`status` &gt;= 30 AND c.channel != 3
        </if>
        <if test="themeCourseQueryVO.searchType != null and themeCourseQueryVO.searchType == 2">
            AND c.`status` = 60 AND c.channel = 3
        </if>
        <if test="themeCourseQueryVO.equipmentId != null">
            AND c.equipment_id = #{themeCourseQueryVO.equipmentId}
        </if>
        <if test="themeCourseQueryVO.id != null">
            AND c.id = #{themeCourseQueryVO.id}
        </if>
        <if test="themeCourseQueryVO.coachId != null">
            AND c.coach_id = #{themeCourseQueryVO.coachId}
        </if>
        <if test="themeCourseQueryVO.name != null and themeCourseQueryVO.name != ''">
            AND c.`name` LIKE CONCAT('%', #{themeCourseQueryVO.name}, '%')
        </if>
        <if test="themeCourseQueryVO.grade != null">
            AND c.grade = #{themeCourseQueryVO.grade}
        </if>
        <if test="themeCourseQueryVO.searchType != null and themeCourseQueryVO.searchType == 0">
            ORDER BY c.live_time DESC
        </if>
        <if test="themeCourseQueryVO.searchType != null and themeCourseQueryVO.searchType == 1">
            ORDER BY c.create_time DESC
        </if>
    </select>

    <!-- 直播课数量查询 -->
    <select id="liveCount" resultType="com.alibaba.fastjson.JSONObject">
        SELECT COUNT(*)                                                         sumNum,
               IFNULL(SUM(CASE WHEN `status` = 30 THEN 1 ELSE 0 END), 0)        waitNum,
               IFNULL(SUM(CASE WHEN `status` IN (35, 40) THEN 1 ELSE 0 END), 0) moveNum,
               IFNULL(SUM(CASE WHEN `status` IN (50, 60) THEN 1 ELSE 0 END), 0) finishNum
        FROM course
        WHERE is_tra &lt;= #{isTra}
          AND channel = #{channel}
          AND `status` NOT IN (0, 10, 20)
          AND line_status = 1
    </select>

    <!-- 查询教练所属课程 -->
    <select id="queryBuCoachId" resultType="com.alibaba.fastjson.JSONObject">
        SELECT c.id,
               c.`name`,
               c.cover,
               DATE_FORMAT(c.live_time, '%Y-%m-%d %H:%i')                                                          liveTime,
               c.`status`,
               (CASE
                    WHEN c.`status` = 30 THEN (SELECT COUNT(*) FROM course_make WHERE course_id = c.id)
                    ELSE (SELECT COUNT(tmp.user_id)
                          FROM (SELECT user_id FROM course_play WHERE course_id = c.id GROUP BY user_id) tmp) END) num
        FROM course c
        WHERE c.`status` &gt;= 30
          AND c.line_status = 1
          AND c.coach_id = #{coachId}
        ORDER BY c.live_time DESC
    </select>

    <!-- 查询计划课程列表 -->
    <select id="queryPlanCourse" resultMap="PlanCoursePOResultMap">
        SELECT
        c.id,
        c.`name`,
        c.cover,
        ( SELECT type_name from equ_equipment_type where id = c.equipment_id ) type_name,
        ( SELECT `name` FROM coach_info WHERE id = c.coach_id ) coach_name,
        c.live_time,
        ( SELECT `name` FROM course_meta WHERE ( CASE WHEN c.type = 1 THEN `code` = 'COURSE_LEVEL' ELSE `code` =
        'COURSE_MERIT_LEVEL' END ) AND val = c.grade ) grade_desc,
        ( CASE WHEN c.is_vip = 0 THEN '普通' ELSE '会员' END ) vip_desc,
        ( CASE WHEN c.channel = 2 THEN '录播' ELSE ( SELECT `name` FROM course_meta WHERE `code` = 'COURSE_STATUS' AND val
        = c.`status` ) END ) status_desc
        FROM course c WHERE ( NOT EXISTS ( SELECT 1 FROM course_plan_associated WHERE c.id = course_id AND plan_id IN (
        SELECT id FROM course_training_plan WHERE online_status IN (1, 2) ) )
        <if test="planCourseQueryVO.courseIds != null and planCourseQueryVO.courseIds.size > 0">OR c.id IN
            <foreach collection="planCourseQueryVO.courseIds" item="id" open="(" separator="," close=")">#{id}</foreach>
        </if>
        )
        AND c.line_status = 1 AND ( (c.channel = 1 AND c.`status` &gt;= 30) OR (c.channel = 2 AND c.`status` = 60) )
        <if test="planCourseQueryVO.isTra != null">
            AND c.is_tra &lt;= #{planCourseQueryVO.isTra}
        </if>
        <if test="planCourseQueryVO.courseId != null">
            AND c.id = #{planCourseQueryVO.courseId}
        </if>
        <if test="planCourseQueryVO.courseName != null and planCourseQueryVO.courseName != ''">
            AND c.name like CONCAT('%', #{planCourseQueryVO.courseName}, '%')
        </if>
        <if test="planCourseQueryVO.otherCourseIds != null and planCourseQueryVO.otherCourseIds.size > 0">
            AND c.id NOT IN
            <foreach collection="planCourseQueryVO.otherCourseIds" item="id" open="(" separator="," close=")">#{id}
            </foreach>
        </if>
        <if test="planCourseQueryVO.isVip != null">
            AND c.is_vip = #{planCourseQueryVO.isVip}
        </if>
        <if test="planCourseQueryVO.coachId != null">
            AND c.coach_id = #{planCourseQueryVO.coachId}
        </if>
        <if test="planCourseQueryVO.equipmentId != null and planCourseQueryVO.equipmentId != 3">
            AND c.equipment_id = #{planCourseQueryVO.equipmentId}
        </if>
        <if test="planCourseQueryVO.equipmentIds != null and planCourseQueryVO.equipmentIds.size > 0">
            AND c.equipment_id IN
            <foreach collection="planCourseQueryVO.equipmentIds" item="smallId" open="(" separator="," close=")">
                #{smallId}
            </foreach>
        </if>
        <if test="planCourseQueryVO.grade != null">
            AND c.grade = #{planCourseQueryVO.grade}
        </if>
        <if test="planCourseQueryVO.channel != null">
            AND c.channel = #{planCourseQueryVO.channel}
        </if>
        <if test="planCourseQueryVO.tagId != null">
            AND EXISTS (SELECT 1 FROM course_tag_detail WHERE course_id = c.id AND tag_id = #{planCourseQueryVO.tagId})
        </if>
        ORDER BY
        <if test="planCourseQueryVO.ids != null and planCourseQueryVO.ids != ''">c.id NOT IN <foreach
                collection="planCourseQueryVO.courseIds" item="id" open="(" separator="," close=")">#{id}</foreach>,
            c.`status` ASC,
        </if>
        c.id DESC
    </select>

    <!-- 查询课程统计数据 -->
    <select id="queryStatistics" parameterType="com.mrk.yudong.admin.api.course.vo.CourseQueryVO"
            resultType="com.mrk.yudong.admin.api.course.po.CoursePO">
        SELECT
        c.id course_id,
        c.`name`,
        ( SELECT type_name FROM equ_equipment_type WHERE id = c.equipment_id ) equipment_name,
        ( SELECT `name` FROM course_meta WHERE `code` = 'COURSE_TYPE' AND val = c.type ) type_desc,
        ( SELECT `name` FROM course_meta WHERE ( CASE WHEN c.type = 1 THEN `code` = 'COURSE_LEVEL' ELSE `code` =
        'COURSE_MERIT_LEVEL' END ) AND val = c.grade ) grade_desc,
        ( SELECT `name` FROM coach_info WHERE id = c.coach_id ) coach_name,
        ( CASE WHEN c.channel = 1 THEN c.live_time ELSE c.create_time END ) live_time,
        ( CASE WHEN c.line_status = 1 THEN ( SELECT `name` FROM course_meta WHERE `code` = 'COURSE_STATUS' AND val =
        c.`status` ) ELSE ( SELECT `name` FROM course_meta WHERE `code` = 'COURSE_LINE_STATUS' AND val = c.line_status )
        END ) status_desc,
        ( SELECT COUNT(*) FROM course_make WHERE course_id = c.id ) make_num,
        ( SELECT COUNT(DISTINCT user_id) FROM course_play WHERE video_type = 1 AND course_id = c.id ) course_num,
        ( SELECT COUNT(DISTINCT user_id) FROM course_collect WHERE course_id = c.id ) collect_num,
        ( SELECT COUNT(*) FROM course_play WHERE video_type = 1 AND course_id = c.id ) play_num,
        c.share_num,
        ( SELECT COUNT(DISTINCT user_id) FROM course_play WHERE video_type = 1 AND play_status = 1 AND course_id = c.id
        ) live_play_num,
        ( SELECT COUNT(id) FROM log_course_detail WHERE course_id = c.id ) pv_num,
        ( SELECT COUNT(DISTINCT user_id) FROM log_course_detail WHERE course_id = c.id ) uv_num,
        c.`status`,
        ( SELECT TRUNCATE(SUM( IF(cp.play_time >= ( cp.course_time * 0.66 ), 1, 0) ) / COUNT(*) * 100, 2) prop FROM
        course_play cp WHERE cp.video_type = 1 AND cp.course_id = c.id ) prop,
        c.channel,
        c.is_vip
        FROM course c WHERE c.`status` NOT IN (0, 10, 20)
        <if test="courseQueryVO.isTra != null">
            AND c.is_tra &lt;= #{courseQueryVO.isTra}
        </if>
        <if test="courseQueryVO.channel != null">
            AND c.channel = #{courseQueryVO.channel}
        </if>
        <if test="courseQueryVO.status != null">
            AND c.`status` = #{courseQueryVO.status}
        </if>
        <if test="courseQueryVO.liveType != null">
            <if test="courseQueryVO.liveType == 1">
                AND c.`status` = 30
            </if>
            <if test="courseQueryVO.liveType == 2">
                AND c.`status` IN (35, 40)
            </if>
            <if test="courseQueryVO.liveType == 3">
                AND c.`status` IN (50, 60)
            </if>
        </if>
        <if test="courseQueryVO.grade != null">
            AND c.grade = #{courseQueryVO.grade}
        </if>
        <if test="courseQueryVO.type != null">
            AND c.type = #{courseQueryVO.type}
        </if>
        <if test="courseQueryVO.isVip != null">
            AND c.is_vip = #{courseQueryVO.isVip}
        </if>
        <if test="courseQueryVO.coachId != null">
            AND c.coach_id = #{courseQueryVO.coachId}
        </if>
        <if test="courseQueryVO.courseId != null">
            AND c.id = #{courseQueryVO.courseId}
        </if>
        <if test="courseQueryVO.equipmentId != null and courseQueryVO.equipmentId != 3">
            AND c.equipment_id = #{courseQueryVO.equipmentId}
        </if>
        <if test="courseQueryVO.equipmentId != null and courseQueryVO.equipmentId == 3">
            AND c.equipment_id IN
            <foreach collection="courseQueryVO.equipmentIds" item="equipmentId" open="(" separator="," close=")">
                #{equipmentId}
            </foreach>
        </if>
        <if test="courseQueryVO.name != null and courseQueryVO.name != ''">
            AND c.`name` LIKE CONCAT('%', #{courseQueryVO.name}, '%')
        </if>
        <if test="courseQueryVO.beginTime != null">
            AND ( CASE WHEN c.channel = 1 THEN c.live_time &gt;= #{courseQueryVO.beginTime} ELSE c.create_time &gt;=
            #{courseQueryVO.beginTime} END )
        </if>
        <if test="courseQueryVO.endTime != null">
            AND ( CASE WHEN c.channel = 1 THEN c.live_time &lt;= #{courseQueryVO.endTime} ELSE c.create_time &lt;=
            #{courseQueryVO.endTime} END )
        </if>
        <if test="courseQueryVO.activityId != null">
            AND EXISTS ( SELECT 1 FROM course_plan_associated WHERE course_id = c.id AND plan_id IN ( SELECT plan_id
            FROM activity_equip_associated WHERE activity_id = #{courseQueryVO.activityId} GROUP BY plan_id ) )
        </if>
        <if test="courseQueryVO.planId != null">
            AND EXISTS ( SELECT 1 FROM course_plan_associated WHERE course_id = c.id AND plan_id =
            #{courseQueryVO.planId} )
        </if>
        ORDER BY
        <if test="courseQueryVO.isSort != null">
            <if test="courseQueryVO.isSort == 0">
                prop ASC,
            </if>
            <if test="courseQueryVO.isSort == 1">
                prop DESC,
            </if>
        </if>
        c.line_status DESC, FIELD(c.`status`, 40, 35) ASC, c.live_time DESC
    </select>

    <!-- 查询课程统计数据导出 -->
    <select id="queryStatisticsExport" resultType="com.alibaba.fastjson.JSONObject">
        SELECT
        c.id course_id,
        c.`name`,
        ( SELECT type_name FROM equ_equipment_type WHERE id = c.equipment_id ) equipment_name,
        ( SELECT `name` FROM course_meta WHERE `code` = 'COURSE_TYPE' AND val = c.type ) type_desc,
        ( SELECT `name` FROM course_meta WHERE ( CASE WHEN c.type = 1 THEN `code` = 'COURSE_LEVEL' ELSE `code` =
        'COURSE_MERIT_LEVEL' END ) AND val = c.grade ) grade_desc,
        ( SELECT `name` FROM coach_info WHERE id = c.coach_id ) coach_name,
        ( CASE WHEN c.channel = 1 THEN DATE_FORMAT( c.live_time, '%Y-%m-%d %H:%i:%s' ) ELSE DATE_FORMAT( c.create_time,
        '%Y-%m-%d %H:%i:%s' ) END ) live_time,
        ( CASE WHEN c.line_status = 1 THEN ( SELECT `name` FROM course_meta WHERE `code` = 'COURSE_STATUS' AND val =
        c.`status` ) ELSE ( SELECT `name` FROM course_meta WHERE `code` = 'COURSE_LINE_STATUS' AND val = c.line_status )
        END ) status_desc,
        ( SELECT COUNT(*) FROM course_make WHERE course_id = c.id ) make_num,
        ( SELECT COUNT(DISTINCT user_id) FROM course_play WHERE video_type = 1 AND course_id = c.id ) course_num,
        ( SELECT COUNT(DISTINCT user_id) FROM course_collect WHERE course_id = c.id ) collect_num,
        ( SELECT COUNT(*) FROM course_play WHERE video_type = 1 AND course_id = c.id ) play_num,
        c.share_num,
        ( SELECT COUNT(DISTINCT user_id) FROM course_play WHERE video_type = 1 AND play_status = 1 AND course_id = c.id
        ) live_play_num,
        ( SELECT COUNT(id) FROM log_course_detail WHERE course_id = c.id ) pv_num,
        ( SELECT COUNT(DISTINCT user_id) FROM log_course_detail WHERE course_id = c.id ) uv_num,
        c.`status`,
        c.channel
        FROM course c WHERE c.`status` NOT IN (0, 10, 20)
        <if test="courseQueryVO.isTra != null">
            AND c.is_tra &lt;= #{courseQueryVO.isTra}
        </if>
        <if test="courseQueryVO.channel != null">
            AND c.channel = #{courseQueryVO.channel}
        </if>
        <if test="courseQueryVO.status != null">
            AND c.`status` = #{courseQueryVO.status}
        </if>
        <if test="courseQueryVO.liveType != null">
            <if test="courseQueryVO.liveType == 1">
                AND c.`status` = 30
            </if>
            <if test="courseQueryVO.liveType == 2">
                AND c.`status` IN (35, 40)
            </if>
            <if test="courseQueryVO.liveType == 3">
                AND c.`status` IN (50, 60)
            </if>
        </if>
        <if test="courseQueryVO.grade != null">
            AND c.grade = #{courseQueryVO.grade}
        </if>
        <if test="courseQueryVO.type != null">
            AND c.type = #{courseQueryVO.type}
        </if>
        <if test="courseQueryVO.isVip != null">
            AND c.is_vip = #{courseQueryVO.isVip}
        </if>
        <if test="courseQueryVO.coachId != null">
            AND c.coach_id = #{courseQueryVO.coachId}
        </if>
        <if test="courseQueryVO.courseId != null">
            AND c.id = #{courseQueryVO.courseId}
        </if>
        <if test="courseQueryVO.equipmentId != null and courseQueryVO.equipmentId != 3">
            AND c.equipment_id = #{courseQueryVO.equipmentId}
        </if>
        <if test="courseQueryVO.equipmentId != null and courseQueryVO.equipmentId == 3">
            AND c.equipment_id IN
            <foreach collection="courseQueryVO.equipmentIds" item="equipmentId" open="(" separator="," close=")">
                #{equipmentId}
            </foreach>
        </if>
        <if test="courseQueryVO.name != null and courseQueryVO.name != ''">
            AND c.`name` LIKE CONCAT('%', #{courseQueryVO.name}, '%')
        </if>
        <if test="courseQueryVO.beginTime != null">
            AND ( CASE WHEN c.channel = 1 THEN c.live_time &gt;= #{courseQueryVO.beginTime} ELSE c.create_time &gt;=
            #{courseQueryVO.beginTime} END )
        </if>
        <if test="courseQueryVO.endTime != null">
            AND ( CASE WHEN c.channel = 1 THEN c.live_time &lt;= #{courseQueryVO.endTime} ELSE c.create_time &lt;=
            #{courseQueryVO.endTime} END )
        </if>
        <if test="courseQueryVO.activityId != null">
            AND EXISTS ( SELECT 1 FROM course_plan_associated WHERE course_id = c.id AND plan_id IN ( SELECT plan_id
            FROM activity_equip_associated WHERE activity_id = #{courseQueryVO.activityId} GROUP BY plan_id ) )
        </if>
        <if test="courseQueryVO.planId != null">
            AND EXISTS ( SELECT 1 FROM course_plan_associated WHERE course_id = c.id AND plan_id =
            #{courseQueryVO.planId} )
        </if>
        ORDER BY c.line_status DESC, FIELD(c.`status`, 40, 35) ASC, c.live_time DESC
    </select>

    <!-- 查询课程数量 -->
    <select id="getAllCourseNum" resultType="com.mrk.yudong.admin.api.course.po.CourseNumPO">
        select category_id as courseId,
               count(1)    as courseNum
        from course
        Group by category_id
    </select>

    <select id="pageCourseOptions" parameterType="com.mrk.yudong.admin.api.course.query.CourseOptionPageQry" resultMap="CourseOptionBO">
        SELECT
        c.id,
        c.`name`,
        c.cover,
        c.coach_id,
        c.equipment_id,
        c.grade,
        c.course_time,
        ( SELECT type_name FROM equ_equipment_type WHERE id = c.equipment_id ) product_name,
        ( SELECT `name` FROM coach_info WHERE id = c.coach_id ) coach_name,
        ( SELECT `name` FROM course_meta WHERE ( CASE WHEN c.type = 1 THEN `code` = 'COURSE_LEVEL' ELSE `code` =
        'COURSE_MERIT_LEVEL' END ) AND val = c.grade ) grade_desc,
        c.category_id,
        ( SELECT `name` FROM category WHERE id = c.category_id) category_name
        FROM
        course c WHERE
                     c.line_status = 1
                   AND ( (c.channel = 1 AND c.`status` &gt;= 30) OR (c.channel = 2 AND c.`status`
        = 60) )
                   <if test="courseOptionQry.isexcludePlan == 1 ">
                       AND NOT EXISTS ( SELECT 1 FROM course_plan_associated WHERE course_id =
                       c.id )
                   </if>

        <if test="courseOptionQry.equipmentId != null">
            AND c.equipment_id = #{courseOptionQry.equipmentId }
        </if>

        <if test="courseOptionQry.id != null">
            AND c.id = #{courseOptionQry.id}
        </if>

        <if test="courseOptionQry.coachId != null">
            AND c.coach_id = #{courseOptionQry.coachId}
        </if>

        <if test="courseOptionQry.name != null and courseOptionQry.name != ''">
            AND c.`name` LIKE CONCAT('%', #{courseOptionQry.name}, '%')
        </if>

        <if test="courseOptionQry.grade != null">
            AND c.grade = #{courseOptionQry.grade}
        </if>

        <if test="courseOptionQry.categoryId!= null">
            AND c.category_id = #{courseOptionQry.categoryId}
        </if>
        <if test="courseOptionQry.minCourseTime != null">
            AND c.course_time &gt;= #{courseOptionQry.minCourseTime}
        </if>

        <if test="courseOptionQry.maxCourseTime != null">
            AND c.course_time &lt;= #{courseOptionQry.maxCourseTime}
        </if>
        <if test="courseOptionQry.excludeIds != null and courseOptionQry.excludeIds.size > 0">
            AND c.id NOT IN
            <foreach collection="courseOptionQry.excludeIds" item="id" open="(" separator="," close=")">#{id}
            </foreach>
        </if>

        <if test="courseOptionQry.ids != null and courseOptionQry.ids.size > 0">
            AND c.id IN
            <foreach collection="courseOptionQry.ids" item="id" open="(" separator="," close=")">#{id}
            </foreach>
        </if>
        ORDER BY c.create_time DESC
    </select>

</mapper>
