<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.CourseInteractMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.course.model.CourseInteract">
        <id column="id" property="id" />
        <result column="course_id" property="courseId" />
        <result column="interact_id" property="interactId" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="getListByCourseId" resultType="com.mrk.yudong.admin.infrastructure.course.model.InteractInfo">
        SELECT ii.id, ii.`name` FROM course_interact ci INNER JOIN interact_info ii ON ci.interact_id = ii.id WHERE ii.id IS NOT NULL AND ci.course_id = #{courseId} ORDER BY ci.id DESC
    </select>

</mapper>
