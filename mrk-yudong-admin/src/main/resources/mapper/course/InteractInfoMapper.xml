<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.InteractInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.course.model.InteractInfo">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="create_id" property="createId" />
        <result column="create_time" property="createTime" />
        <result column="update_id" property="updateId" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="query" resultType="com.mrk.yudong.admin.infrastructure.course.model.InteractInfo">
        SELECT ii.*, ( SELECT nick_name FROM sys_user WHERE id = ii.create_id ) create_name FROM interact_info ii WHERE 1 = 1
        <if test="interactInfoQueryVO.name != null and interactInfoQueryVO.name != ''">
            AND ii.`name` LIKE CONCAT('%', #{interactInfoQueryVO.name}, '%')
        </if>
        <if test="interactInfoQueryVO.isTra != null">
            AND ii.is_tra &lt;= #{interactInfoQueryVO.isTra}
        </if>
        ORDER BY ii.id DESC
    </select>

</mapper>
