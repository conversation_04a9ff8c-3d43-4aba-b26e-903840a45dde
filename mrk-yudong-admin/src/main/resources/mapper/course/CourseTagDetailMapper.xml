<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.CourseTagDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.course.model.CourseTagDetail">
        <id column="id" property="id"/>
        <result column="course_id" property="courseId"/>
        <result column="top_id" property="topId"/>
        <result column="tag_id" property="tagId"/>
    </resultMap>

    <resultMap id="CourseTagResultMap" type="com.mrk.yudong.admin.infrastructure.course.model.CourseTag">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="parent_id" property="parentId"/>
        <result column="num" property="num"/>
        <result column="is_hide" property="isHide"/>
        <result column="is_tra" property="isTra"/>
        <result column="create_id" property="createId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_id" property="updateId"/>
        <result column="update_name" property="updateName"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 根据课程ID获取标签信息 -->
    <select id="getTagList" resultMap="CourseTagResultMap">
        SELECT ct.*
        FROM course_tag_detail ctd
                 RIGHT JOIN course_tag ct ON ctd.tag_id = ct.id
        WHERE ctd.course_id = #{courseId}
        ORDER BY ctd.id ASC
    </select>

</mapper>
