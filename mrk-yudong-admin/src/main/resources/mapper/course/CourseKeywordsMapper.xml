<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.CourseKeywordsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.course.model.CourseKeywords">
        <id column="id" property="id" />
        <result column="keywords" property="keywords" />
        <result column="the_dimension" property="theDimension" />
        <result column="equip_type_id" property="equipTypeId" />
        <result column="type" property="type" />
        <result column="is_delete" property="isDelete" />
        <result column="create_by" property="createBy" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="query" resultMap="BaseResultMap">
        SELECT ck.*, ( SELECT nick_name FROM sys_user WHERE id = ck.create_by ) create_name FROM course_keywords ck WHERE ck.is_delete = 0 AND (ck.type = 0 OR (1 = 1
        <if test="param.equipTypeId != null">
            AND FIND_IN_SET(#{param.equipTypeId}, ck.equip_type_id)
        </if>
        <if test="param.type != null">
            AND ck.type = #{param.type}
        </if>
        <if test="param.theDimension != null">
            AND ck.the_dimension = #{param.theDimension}
        </if>
        ))
        ORDER BY ck.create_time DESC
    </select>

</mapper>
