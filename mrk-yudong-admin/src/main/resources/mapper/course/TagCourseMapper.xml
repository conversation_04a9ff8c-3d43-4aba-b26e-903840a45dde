<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.TagCourseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.course.model.TagCourse">
        <id column="id" property="id" />
        <result column="module_code" property="moduleCode" />
        <result column="tag_category_id" property="tagCategoryId" />
        <result column="tag_code" property="tagCode" />
        <result column="tag_name" property="tagName" />
        <result column="tag_status" property="tagStatus" />
        <result column="seq" property="seq" />
        <result column="remark" property="remark" />
        <result column="create_id" property="createId" />
        <result column="create_time" property="createTime" />
        <result column="update_id" property="updateId" />
        <result column="update_time" property="updateTime" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

</mapper>
