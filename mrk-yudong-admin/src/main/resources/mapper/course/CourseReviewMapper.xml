<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.course.mapper.CourseReviewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.course.model.CourseReview">
        <id column="id" property="id" />
        <result column="course_id" property="courseId" />
        <result column="sys_user_id" property="sysUserId" />
        <result column="review" property="review" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="count" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM (SELECT cr.course_id FROM course_review cr WHERE EXISTS (SELECT 1 FROM course WHERE id = cr.course_id AND is_tra &lt;= #{isTra}) GROUP BY cr.course_id) tmp
    </select>

</mapper>
