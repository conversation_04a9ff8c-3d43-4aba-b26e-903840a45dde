<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.link.mapper.LinkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.link.model.Link">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="status" property="status" />
        <result column="crowd" property="crowd" />
        <result column="terminal" property="terminal" />
        <result column="product_id" property="productId" />
        <result column="begin_version" property="beginVersion" />
        <result column="end_version" property="endVersion" />
        <result column="begin_time" property="beginTime" />
        <result column="end_time" property="endTime" />
        <result column="is_delete" property="isDelete" />
        <result column="create_id" property="createId" />
        <result column="create_time" property="createTime" />
        <result column="update_id" property="updateId" />
        <result column="update_time" property="updateTime" />
    </resultMap>

</mapper>
