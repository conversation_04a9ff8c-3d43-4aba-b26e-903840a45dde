<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.content.mapper.LiveVideoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.content.model.LiveVideo">
        <id column="id" property="id"/>
        <result column="equip_type_id" property="equipTypeId"/>
        <result column="title" property="title"/>
        <result column="image" property="image"/>
        <result column="video" property="video"/>
        <result column="sort" property="sort"/>
        <result column="online_status" property="onlineStatus"/>
        <result column="is_delete" property="isDelete"/>
        <result column="operation_by" property="operationBy"/>
        <result column="operation_date" property="operationDate"/>
    </resultMap>

    <update id="updateOnlineStatus">
        UPDATE live_video
        SET online_status = #{oper},
            operation_date=NOW()
        where id = #{id}
    </update>


</mapper>
