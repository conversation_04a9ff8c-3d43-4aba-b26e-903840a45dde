<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.sys.mapper.SysUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.sys.model.SysUser">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="nick_name" property="nickName" />
        <result column="type" property="type" />
        <result column="type_desc" property="typeDesc" />
        <result column="status" property="status" />
        <result column="status_desc" property="statusDesc" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_name" property="updateName" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="query" resultMap="BaseResultMap">
        SELECT
            su.*,
            (SELECT `name` FROM sys_meta WHERE `code` = 'SYS_TYPE' AND val = su.type) type_desc,
            (SELECT `name` FROM sys_meta WHERE `code` = 'SYS_STATUS' AND val = su.`status`) status_desc,
            (SELECT nick_name FROM sys_user WHERE id = su.update_by) update_name
        FROM sys_user su WHERE 1 = 1
        <if test="param.name != null and param.name != ''">
            AND su.nick_name LIKE CONCAT('%', #{param.name}, '%')
        </if>
        <if test="param.mobile != null and param.mobile != ''">
            AND su.username LIKE CONCAT('%', #{param.mobile}, '%')
        </if>
        <if test="param.roleId != null and param.roleId != -1">
            AND EXISTS ( SELECT 1 FROM sys_user_role WHERE sys_user_id = su.id AND sys_role_id = #{param.roleId} )
        </if>
        <if test="param.roleId != null and param.roleId == -1">
            AND NOT EXISTS ( SELECT 1 FROM sys_user_role WHERE sys_user_id = su.id )
        </if>
        ORDER BY IFNULL(su.update_time, su.create_time) DESC
    </select>

</mapper>
