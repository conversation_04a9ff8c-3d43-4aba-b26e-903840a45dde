<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mrk.yudong.admin.infrastructure.device.mapper.EquipmentCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.mrk.yudong.admin.infrastructure.device.model.EquipmentCategory">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="type_desc" property="typeDesc" />
        <result column="num" property="num" />
        <result column="cover" property="cover" />
        <result column="icon" property="icon" />
        <result column="status" property="status" />
        <result column="create_id" property="createId" />
        <result column="create_time" property="createTime" />
        <result column="update_id" property="updateId" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 获取列表 -->
    <select id="queryList" resultMap="BaseResultMap">
        SELECT ec.id,
               ec.type,
               ( SELECT `name` FROM equip_dict WHERE dict_key = 'EQUIPMENT_CATEGORY_TYPE' AND `value` = ec.type ) type_desc,
               ( SELECT COUNT(*) FROM equipment_other WHERE category_id = ec.id ) num,
               ec.cover,
               ec.icon
        FROM equipment_category ec WHERE ec.`status` = 1 ORDER BY ec.id ASC
    </select>

</mapper>
