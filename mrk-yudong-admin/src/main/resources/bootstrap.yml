spring:
  application:
    name: yudong-admin
  profiles:
    active:
      - ${spring.profiles.active:dev}
  cloud:
    nacos:
      config:
        file-extension: yml
        server-addr: ${config.server.url:nacos.mrk.local:8848}
        group: DEFAULT_GROUP
        namespace: ${config.namespace:25785862-0aeb-46df-b6fa-57b23313b2b2}
      discovery:
        server-addr: ${config.server.url:nacos.mrk.local:8848}
        group: DEFAULT_GROUP
        namespace: ${config.namespace:25785862-0aeb-46df-b6fa-57b23313b2b2}