package com.mrk.yudong.websocket.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class SendDTO {
    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 请求唯一ID
     */
    private String requestId;

    private String message;

    /**
     * 业务数据
     */
    private Object data;

    public SendDTO(Integer bizType, Object data) {
        this.bizType = bizType;
        this.data = data;
    }

    public SendDTO(Integer bizType, String requestId, Object data) {
        this.bizType = bizType;
        this.requestId = requestId;
        this.data = data;
    }

    public SendDTO(Integer bizType, String requestId, String message) {
        this.bizType = bizType;
        this.requestId = requestId;
        this.message = message;
    }
}
