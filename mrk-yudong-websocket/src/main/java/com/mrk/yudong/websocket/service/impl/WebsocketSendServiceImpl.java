package com.mrk.yudong.websocket.service.impl;

import com.mrk.yudong.share.vo.WebsocketSendVO;
import com.mrk.yudong.websocket.core.WebsocketSender;
import com.mrk.yudong.websocket.service.IWebsocketSendService;
import com.mrk.yudong.websocket.vo.WebsocketSendErrorVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@RequiredArgsConstructor
@Service
public class WebsocketSendServiceImpl implements IWebsocketSendService {

    private final ConcurrentHashMap<String, WebsocketSender> webSocketSessionMap;

    /**
     * 发送信息
     *
     * @param websocketSendVO 长连接发送相关信息
     * <AUTHOR>
     * @date 2022/1/27 14:52
     */
    @Override
    public void send(WebsocketSendVO websocketSendVO) {
        String key = websocketSendVO.getKey();
        Object data = websocketSendVO.getData();
        Boolean isById = websocketSendVO.getIsById();
        if (isById != null && isById) {
            // 指定发送
            String sessionKey = String.format(WebsocketSendVO.SESSION_KEY, key, websocketSendVO.getSessionId());
            WebsocketSender websocketSender = webSocketSessionMap.get(sessionKey);
            if (websocketSender == null) {
                log.warn("=============== 找不到对应的长连接信息 ===============");
                return;
            }

            websocketSender.send(data);
        } else {
            // 全局发送
            if (webSocketSessionMap.size() <= 0) {
                log.warn("=============== 找不到对应的长连接信息 ===============");
                return;
            }

            webSocketSessionMap.forEach((k, v) -> {
                if (k.startsWith(key)) {
                    v.send(data);
                }
            });
        }
    }

    /**
     * 发送信息并关闭连接
     *
     * @param websocketSendVO 长连接发送相关信息
     * <AUTHOR>
     * @date 2022/1/27 14:52
     */
    @Override
    public void sendAndClose(WebsocketSendVO websocketSendVO) {
        if (webSocketSessionMap.size() <= 0) {
            log.warn("=============== 找不到对应的长连接信息 ===============");
            return;
        }

        webSocketSessionMap.forEach((k, v) -> {
            if (k.startsWith(websocketSendVO.getKey())) {
                v.sendAndClose(websocketSendVO.getData());
            }
        });
    }

    /**
     * 发送错误信息并关闭连接
     *
     * @param key     指定KEY
     * @param status  状态码
     * @param message 提示信息
     * <AUTHOR>
     * @date 2022/1/27 14:53
     */
    @Override
    public void error(String key, int status, String message) {
        if (webSocketSessionMap.size() <= 0) {
            log.warn("=============== 找不到对应的长连接信息 ===============");
            return;
        }

        WebsocketSender websocketSender = webSocketSessionMap.get(key);
        if (websocketSender == null) {
            log.warn("=============== 找不到对应的长连接信息 ===============");
            return;
        }

        websocketSender.error(status, message);
    }

    @Override
    public void error(WebsocketSendErrorVO websocketSendErrorVO) {
        if (webSocketSessionMap.size() <= 0) {
            log.warn("=============== 找不到对应的长连接信息 ===============");
            return;
        }

        WebsocketSender websocketSender = webSocketSessionMap.get(websocketSendErrorVO.getKey());
        if (websocketSender == null) {
            log.warn("=============== 找不到对应的长连接信息 ===============");
            return;
        }

        websocketSender.error(websocketSendErrorVO.getStatus(), websocketSendErrorVO.getData());
    }

    @Override
    public void sendMessage(String keyPrefix, Object data) {
        WebsocketSendVO websocketSendVO = new WebsocketSendVO();
        websocketSendVO.setData(data);
        websocketSendVO.setKey(keyPrefix);
        send(websocketSendVO);
    }

}
