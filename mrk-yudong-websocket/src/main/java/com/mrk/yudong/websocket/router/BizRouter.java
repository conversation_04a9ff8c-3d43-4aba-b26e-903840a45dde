package com.mrk.yudong.websocket.router;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import com.merach.sun.common.lang.exception.BusinessException;
import com.mrk.yudong.share.vo.WebsocketSendVO;
import com.mrk.yudong.websocket.dto.ReceiveDTO;
import com.mrk.yudong.websocket.dto.SendDTO;
import com.mrk.yudong.websocket.enums.BizType;
import com.mrk.yudong.websocket.enums.ErrorEnum;
import com.mrk.yudong.websocket.service.BaseBizService;
import com.mrk.yudong.websocket.service.IWebsocketSendService;
import com.mrk.yudong.websocket.vo.WebsocketSendErrorVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Component
public class BizRouter {

    private final Map<String, BaseBizService> bizServiceMap;

    private final IWebsocketSendService websocketSendService;

    public void receiveMessage(ReceiveDTO receiveDTO) {
        try {
            BaseBizService bizService = getBizService(receiveDTO.getBizType());
            WebsocketSendVO websocketSendVO = bizService.handleReceiveMessage(receiveDTO);
            if(ObjectUtil.isNotNull(websocketSendVO)) {
                websocketSendService.send(websocketSendVO);
            }
        } catch (Exception e) {
            log.error("receiveMessage->error", e);
            SendDTO sendDTO = new SendDTO(receiveDTO.getBizType(), receiveDTO.getRequestId(), e.getMessage());
            WebsocketSendErrorVO websocketSendVO = new WebsocketSendErrorVO();
            websocketSendVO.setKey(receiveDTO.getSessionKey());
            websocketSendVO.setData(sendDTO);
            websocketSendVO.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            websocketSendService.error(websocketSendVO);
        }
    }

    public void disConnect(String sessionKey) {
        for (Map.Entry<String, BaseBizService> entry:bizServiceMap.entrySet()) {
            entry.getValue().disConnect(sessionKey);
        }
    }

    private BaseBizService getBizService(Integer bizType) {
        BizType bizTypeEnum = EnumUtil.getBy(BizType::getCode, bizType);
        if(ObjectUtil.isNull(bizTypeEnum)) {
            throw new BusinessException(ErrorEnum.BIZ_TYPE_NOT_FOUND.getErrorCode(), ErrorEnum.BIZ_TYPE_NOT_FOUND.getErrorMessage());
        }
        BaseBizService service = bizServiceMap.get("biz.service." + bizTypeEnum.name());
        if(ObjectUtil.isNull(service)) {
            throw new BusinessException(ErrorEnum.BIZ_TYPE_NOT_FOUND.getErrorCode(), ErrorEnum.BIZ_TYPE_NOT_FOUND.getErrorMessage());
        }
        return service;
    }
}
