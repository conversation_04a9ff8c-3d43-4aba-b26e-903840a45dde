package com.mrk.yudong.websocket.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.share.po.CourseCataloguePO;
import com.mrk.yudong.share.po.CourseLinkPO;
import com.mrk.yudong.share.po.LiveTimePO;
import com.mrk.yudong.share.po.SocketCourseInfoPO;
import com.mrk.yudong.share.util.WebsocketSignUtil;
import com.mrk.yudong.share.vo.WebsocketSendVO;
import com.mrk.yudong.websocket.annotation.WebsocketMapping;
import com.mrk.yudong.websocket.core.R;
import com.mrk.yudong.websocket.core.WebsocketSender;
import com.mrk.yudong.websocket.dto.SendDTO;
import com.mrk.yudong.websocket.feign.CourseFeign;
import com.mrk.yudong.websocket.service.IWebsocketSendService;
import com.mrk.yudong.websocket.util.WebscoketUtil;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.socket.HandshakeInfo;
import org.springframework.web.reactive.socket.WebSocketHandler;
import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@RequiredArgsConstructor
@WebsocketMapping("/course")
@Component
@Deprecated
public class CourseHandler implements WebSocketHandler {

    private final ConcurrentHashMap<String, WebsocketSender> webSocketSessionMap;

    private final IWebsocketSendService websocketSendService;

    private final CourseFeign courseFeign;

    @NonNull
    @Override
    public Mono<Void> handle(WebSocketSession webSocketSession) {
        String id = webSocketSession.getId();
        HandshakeInfo handshakeInfo = webSocketSession.getHandshakeInfo();
        String query = handshakeInfo.getUri().getQuery();
        Map<String, Object> queryMap = WebscoketUtil.buildQueryMap(query);
        if (MapUtil.isEmpty(queryMap)) {
            return WebscoketUtil.error(webSocketSession, 403, "Forbidden");
        }

        Long courseId = MapUtil.getLong(queryMap, "courseId");
        String sign = MapUtil.getStr(queryMap, "sign");
        if (courseId == null || StrUtil.isBlank(sign)) {
            return WebscoketUtil.error(webSocketSession, 400, "BadRequest");
        }

        boolean valid = WebsocketSignUtil.valid(courseId.toString(), sign,true);
        if (!valid) {
            return WebscoketUtil.error(webSocketSession, 403, "Forbidden");
        }

        String sessionKey = String.format(WebsocketSendVO.SESSION_KEY, courseId, id);
        Mono<Void> input = webSocketSession.receive()
                .subscribeOn(Schedulers.boundedElastic())
                .doOnNext(webSocketMessage -> {
                    String payloadAsText = webSocketMessage.getPayloadAsText();
                    log.warn("============= 连接：{} 发送的消息为：{} =============", id, payloadAsText);
                    if (StrUtil.equals(payloadAsText, "1")) {
                        return;
                    }

                    JSONObject dict;
                    try {
                        dict = JSON.parseObject(payloadAsText);
                    } catch (Exception e) {
                        websocketSendService.error(sessionKey, 400, "无效参数");
                        return;
                    }

                    Integer operation = dict.getInteger("operation");
                    if (operation == null || operation != 1) {
                        log.warn("============= 连接：{} 非法操作类型：{} 不做任何处理 =============", id, operation);
                        return;
                    }

                    R courseInfoVO = courseFeign.getCourseInfo(courseId);
                    if (R.isFail(courseInfoVO)) {
                        websocketSendService.error(sessionKey, courseInfoVO.getStatus(), courseInfoVO.getMessage());
                        return;
                    }

                    SocketCourseInfoPO socketCourseInfoPO = BeanUtil.toBean(courseInfoVO.getData(), SocketCourseInfoPO.class);
                    LiveTimePO liveTimePO = this.buildLiveTimePO(socketCourseInfoPO);
                    websocketSendService.send(new WebsocketSendVO(courseId.toString(), id, new SendDTO(0, liveTimePO), true));
                }).then();

        Mono<Void> output = webSocketSession.send(Flux.create(sink -> {
            Dict dict = new Dict(1);
            dict.put("sessionId", id);
            sink.next(webSocketSession.textMessage(JSON.toJSONString(R.ok(dict))));
            webSocketSessionMap.put(sessionKey, new WebsocketSender(webSocketSession, sink));
        })).then();

        return Mono.zip(input, output).doFinally(signalType -> {
            webSocketSessionMap.remove(sessionKey);
            log.warn("============== 连接：{} 断开，key为：{} 状态为：{} ==============", id, sessionKey, signalType);
        }).then();
    }

    private LiveTimePO buildLiveTimePO(SocketCourseInfoPO socketCourseInfoPO) {
        Long courseId = socketCourseInfoPO.getCourseId();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime actualLiveTime = LocalDateTimeUtil.of(socketCourseInfoPO.getActualLiveTime());
        int seconds = (int) actualLiveTime.until(now, ChronoUnit.SECONDS);
        log.warn("========= 课程：{} 已开播：{} 秒 =========", courseId, seconds);

        LiveTimePO liveTimePO = new LiveTimePO().setLiveTime(seconds);
        List<CourseCataloguePO> courseCataloguePOS = socketCourseInfoPO.getCourseCataloguePOS();
        int num = 0;
        List<CourseCataloguePO> list = new ArrayList<>(courseCataloguePOS.size());

        boolean isAfter = true;
        for (CourseCataloguePO courseCataloguePO : courseCataloguePOS) {
            Integer beginTime = courseCataloguePO.getBeginTime();
            Integer endTime = courseCataloguePO.getEndTime();
            boolean isOK = beginTime <= seconds && seconds < endTime;
            if (!isOK && isAfter) {
                num += endTime - beginTime;
                continue;
            }

            int time = (num == 0 ? endTime - seconds : seconds - num);
            List<CourseLinkPO> courseLinkPOS = courseCataloguePO.getCourseLinkPOS();
            if (CollUtil.isEmpty(courseLinkPOS)) {
                liveTimePO.setTime(endTime - time);
                break;
            }

            if (liveTimePO.getTime() == null) {
                for (CourseLinkPO courseLinkPO : courseLinkPOS) {
                    Integer linkBeginTime = courseLinkPO.getBeginTime();
                    Integer linkEndTime = courseLinkPO.getEndTime();
                    boolean isThis = linkBeginTime <= seconds && seconds < linkEndTime;
                    if (!isThis) {
                        continue;
                    }

                    liveTimePO.setTime(linkEndTime - seconds);
                    break;
                }
            }
            list.add(courseCataloguePO);
            isAfter = false;
        }
        liveTimePO.setCourseCataloguePOS(list);

        log.warn("返回直播信息结果：{}", JSON.toJSONString(liveTimePO));
        return liveTimePO;
    }

}
