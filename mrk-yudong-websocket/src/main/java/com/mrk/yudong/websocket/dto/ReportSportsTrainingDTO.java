package com.mrk.yudong.websocket.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/14 18:45
 */
@Data
public class ReportSportsTrainingDTO {

    private Long deviceId;
    private Long deviceRelId;
    private Long originDeviceRelId;
    private Long userId;
    private Long time;
    private BigDecimal totalDistance = BigDecimal.ZERO;
    private BigDecimal resistanceLevel = BigDecimal.ZERO;
    private BigDecimal instantaneousPower = BigDecimal.ZERO;
    private BigDecimal averagePower = BigDecimal.ZERO;
    private BigDecimal totalEnergy = BigDecimal.ZERO;
    private BigDecimal energyPerHour = BigDecimal.ZERO;
    private BigDecimal energyPerMinute = BigDecimal.ZERO;
    private BigDecimal heartRate = BigDecimal.ZERO;
    private BigDecimal metabolicEquivalent = BigDecimal.ZERO;
    private BigDecimal elapsedTime = BigDecimal.ZERO;
    private BigDecimal remainingTime = BigDecimal.ZERO;
    private BigDecimal instantaneousSpeed = BigDecimal.ZERO;
    private BigDecimal averageSpeed = BigDecimal.ZERO;
    private BigDecimal stepPerMinute = BigDecimal.ZERO;
    private BigDecimal averageStepRate = BigDecimal.ZERO;
    private BigDecimal strideCount = BigDecimal.ZERO;
    private BigDecimal inclination = BigDecimal.ZERO;
    private BigDecimal rampAngleSetting = BigDecimal.ZERO;
    private BigDecimal positiveElevationGain = BigDecimal.ZERO;
    private BigDecimal negativeElevationGain = BigDecimal.ZERO;
    private BigDecimal instantaneousCadence = BigDecimal.ZERO;
    private BigDecimal averageCadence = BigDecimal.ZERO;
    private BigDecimal strokeRate = BigDecimal.ZERO;
    private BigDecimal strokeCount = BigDecimal.ZERO;
    private BigDecimal averageStrokeRate = BigDecimal.ZERO;
    private BigDecimal instantaneousPace = BigDecimal.ZERO;
    private BigDecimal averagePace = BigDecimal.ZERO;

    /**
     * 心率消耗
     */
    private BigDecimal rateKcal = BigDecimal.ZERO;

    /**
     * 运动ID
     */
    private Long sportId;

}
