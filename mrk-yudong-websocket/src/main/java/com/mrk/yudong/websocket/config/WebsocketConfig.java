package com.mrk.yudong.websocket.config;

import com.mrk.yudong.share.util.JwtUtil;
import com.mrk.yudong.websocket.core.WebsocketMappingHandlerMapping;
import com.mrk.yudong.websocket.core.WebsocketSender;
import com.mrk.yudong.websocket.properties.BaseProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.web.reactive.HandlerMapping;
import org.springframework.web.reactive.socket.server.support.WebSocketHandlerAdapter;

import java.util.concurrent.ConcurrentHashMap;

@RequiredArgsConstructor
@EnableConfigurationProperties(BaseProperties.class)
@SpringBootConfiguration
public class WebsocketConfig {

    private final BaseProperties baseProperties;

    @Bean
    public HandlerMapping websocketMapping() {
        return new WebsocketMappingHandlerMapping();
    }

    @Bean
    public WebSocketHandlerAdapter webSocketHandlerAdapter() {
        return new WebSocketHandlerAdapter();
    }

    @Bean
    public ConcurrentHashMap<String, WebsocketSender> webSocketSessionMap() {
        return new ConcurrentHashMap<>();
    }

    @Bean
    public JwtUtil jwtUtil() {
        return new JwtUtil(baseProperties.getJwtSecret());
    }

}
