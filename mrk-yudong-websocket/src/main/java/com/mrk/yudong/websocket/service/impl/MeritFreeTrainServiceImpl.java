package com.mrk.yudong.websocket.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import com.merach.sun.common.layer.web.ResDTO;
import com.mrk.yudong.share.bo.SportReportBO;
import com.mrk.yudong.share.bo.StartMeritFreeTrainBO;
import com.mrk.yudong.share.bo.TrainDetailBO;
import com.mrk.yudong.share.vo.WebsocketSendVO;
import com.mrk.yudong.websocket.dto.MeritFreeTrainDTO;
import com.mrk.yudong.websocket.dto.ReceiveDTO;
import com.mrk.yudong.websocket.enums.BizType;
import com.mrk.yudong.websocket.enums.MeritFreeTrainType;
import com.mrk.yudong.websocket.feign.UserFeign;
import com.mrk.yudong.websocket.service.IMeritFreeTrainService;
import com.mrk.yudong.websocket.service.IWebsocketSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.validation.Valid;

@Slf4j
@RequiredArgsConstructor
@Service("biz.service.MERIT_FREE_TRAIN")
public class MeritFreeTrainServiceImpl extends BaseBizServiceImpl implements IMeritFreeTrainService {

    private final UserFeign userFeign;

    private final IWebsocketSendService websocketSendService;

    @Override
    public WebsocketSendVO handleReceiveMessage(ReceiveDTO receiveDTO) {
        if (ObjectUtil.isNull(receiveDTO.getData())) {
            return null;
        }
        MeritFreeTrainDTO meritFreeTrainDTO = receiveDTO.getData().to(MeritFreeTrainDTO.class);
        meritFreeTrainDTO.setUserId(receiveDTO.getUserId());
        if (ObjectUtil.isNull(meritFreeTrainDTO.getType())) {
            log.warn("handleReceiveMessage > message miss type, receiveDTO: {}", receiveDTO);
            websocketSendService.error(buildErrorMessage(receiveDTO, HttpStatus.INTERNAL_SERVER_ERROR.value(), "params is illegal, miss type"));
            return null;
        }
        MeritFreeTrainType type = EnumUtil.getBy(MeritFreeTrainType::getCode, meritFreeTrainDTO.getType());
        if (ObjectUtil.isNull(type)) {
            log.warn("handleReceiveMessage > type not in enum, receiveDTO: {}", receiveDTO);
            websocketSendService.error(buildErrorMessage(receiveDTO, HttpStatus.INTERNAL_SERVER_ERROR.value(), "params is illegal, type error"));
            return null;
        }
        ResDTO<?> result = new ResDTO<>();
        switch (type) {
            case START:
                result = startTrain(BeanUtil.copyProperties(meritFreeTrainDTO, StartMeritFreeTrainBO.class));
                break;
            case END:
                result = endTrain(meritFreeTrainDTO.getSportReportId());
                break;
        }
        ;
        if (result.getStatus() != 200) {
            log.warn("handleReceiveMessage->receiveDTO: {}, result: {}", receiveDTO, result);
            websocketSendService.error(buildErrorMessage(receiveDTO, HttpStatus.INTERNAL_SERVER_ERROR.value(), result.getMessage()));
            return null;
        }
        return buildMessage(receiveDTO.getSessionKey(), receiveDTO.getRequestId(), result.getData());
    }

    @Override
    public void disConnect(String sessionKey) {

    }

    protected Integer getBizType() {
        return BizType.MERIT_FREE_TRAIN.getCode();
    }

    private ResDTO<SportReportBO> startTrain(@Valid StartMeritFreeTrainBO trainBO) {
        return userFeign.startMeritFreeTrain(trainBO);
    }

    private ResDTO<TrainDetailBO> endTrain(Long sportReportId) {
        if (ObjectUtil.isNull(sportReportId)) {
            return ResDTO.fail("缺少参数,结算失败");
        }
        return userFeign.endMeritFreeTrain(sportReportId);
    }

}
