package com.mrk.yudong.websocket.rabbit;

import com.alibaba.fastjson.JSON;
import com.mrk.yudong.share.constant.QueueConstant;
import com.mrk.yudong.share.vo.WebsocketSendVO;
import com.mrk.yudong.websocket.dto.SendDTO;
import com.mrk.yudong.websocket.service.IWebsocketSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class RabbitListener {

    private final IWebsocketSendService websocketSendService;

    @StreamListener(QueueConstant.WEBSOCKET_QUEUE)
    public void websocket(WebsocketSendVO websocketSendVO) {
        websocketSendVO.setData(new SendDTO(0, websocketSendVO.getData()));
        log.warn("=========== websocket监听，消息为：{} ===========", JSON.toJSONString(websocketSendVO));
        websocketSendService.send(websocketSendVO);
    }

    @StreamListener(QueueConstant.CLOSE_WEBSOCKET_QUEUE)
    public void closeWebsocket(WebsocketSendVO websocketSendVO) {
        websocketSendVO.setData(new SendDTO(0, websocketSendVO.getData()));
        log.warn("=========== 关闭websocket监听，消息为：{} ===========", JSON.toJSONString(websocketSendVO));
        websocketSendService.sendAndClose(websocketSendVO);
    }
}
