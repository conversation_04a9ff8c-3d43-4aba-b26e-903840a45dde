package com.mrk.yudong.websocket.feign;

import com.merach.sun.common.layer.web.ResDTO;
import com.mrk.yudong.share.bo.SportReportBO;
import com.mrk.yudong.share.bo.StartMeritFreeTrainBO;
import com.mrk.yudong.share.bo.TrainDetailBO;
import com.mrk.yudong.share.vo.ActionAssessMessageVO;
import com.mrk.yudong.websocket.core.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "yudong-user")
public interface UserFeign {

    /**
     * 开始动态测评
     *
     * @param id 测评ID
     * @return com.mrk.yudong.websocket.core.R
     */
    @PostMapping("/actionAssess/start")
    R startActionAssess(@RequestParam("id") Long id);

    /**
     * 获取超燃脂率和提示
     *
     * @param id 测评ID
     * @return com.mrk.yudong.websocket.core.R
     */
    @PostMapping("/actionAssess/handle")
    ResDTO<ActionAssessMessageVO> actionAssessHandle(@RequestParam("id") Long id);

    /**
     * 剩余20秒触发预提示
     *
     * @param id 测评ID
     * @return com.mrk.yudong.websocket.core.R
     */
    @PostMapping("/actionAssess/tips")
    R unitChangeTips(@RequestParam("id") Long id);

    /**
     * 切换小结
     *
     * @param id 测评ID
     * @return com.mrk.yudong.websocket.core.R
     */
    @PostMapping("/actionAssess/unitChange")
    R unitChange(@RequestParam("id") Long id, @RequestParam("linkId") Long linkId);

    /**
     * 结束动态测评
     *
     * @param id 测评ID
     * @return com.mrk.yudong.websocket.core.R
     */
    @PostMapping("/actionAssess/end")
    R endActionAssess(@RequestParam("id") Long id);

    /**
     * 开始超燃脂自由训练
     */
    @PostMapping("/train/merit-free-train/start")
    ResDTO<SportReportBO> startMeritFreeTrain(@RequestBody StartMeritFreeTrainBO startMeritFreeTrainBO);

    /**
     * 结束超燃脂自由训练
     *
     * @param sportReportId 运动记录ID
     * @return com.mrk.yudong.websocket.core.R
     */
    @PostMapping("/train/merit-free-train/end")
    ResDTO<TrainDetailBO> endMeritFreeTrain(@RequestParam("sportReportId") Long sportReportId);

}
