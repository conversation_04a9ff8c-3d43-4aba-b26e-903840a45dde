package com.mrk.yudong.websocket.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.mrk.yudong.websocket.core.R;
import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: ljx
 * @create: 2022/9/20 18:53
 * @Version 1.0
 **/
public class WebscoketUtil {

    public static Mono<Void> error(WebSocketSession webSocketSession, int status, String message) {
        Mono<Void> output = webSocketSession.send(Flux.create(sink -> sink.next(webSocketSession.textMessage(JSON.toJSONString(R.fail(status, message)))))).then();
        Mono<Void> close = webSocketSession.close();
        return Mono.zip(output, close).then();
    }

    public static Map<String, Object> buildQueryMap(String query) {
        if (StrUtil.isBlank(query)) {
            return null;
        }

        List<String> split = StrUtil.split(query, '&', true, true);
        if (CollUtil.isEmpty(split)) {
            return null;
        }

        Map<String, Object> param = new HashMap<>(split.size());
        split.forEach(s -> {
            String[] strings = s.split("=");
            if (strings.length >= 2) {
                param.put(strings[0], strings[1]);
            }
        });

        return param;
    }
}
