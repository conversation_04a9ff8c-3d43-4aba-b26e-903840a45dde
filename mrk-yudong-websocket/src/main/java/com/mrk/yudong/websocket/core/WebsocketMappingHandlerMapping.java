package com.mrk.yudong.websocket.core;

import com.mrk.yudong.websocket.annotation.WebsocketMapping;
import org.springframework.beans.BeansException;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.reactive.handler.SimpleUrlHandlerMapping;
import org.springframework.web.reactive.socket.WebSocketHandler;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

public class WebsocketMappingHandlerMapping extends SimpleUrlHandlerMapping {

    @Override
    public void initApplicationContext() throws BeansException {
        Map<String, Object> objectMap = super.obtainApplicationContext().getBeansWithAnnotation(WebsocketMapping.class);
        Map<String, WebSocketHandler> handlerMap = new LinkedHashMap<>(objectMap.size());

        objectMap.values().forEach(bean -> {
            if (!(bean instanceof WebSocketHandler)) {
                throw new RuntimeException(String.format("Controller [%s] doesn't implement WebSocketHandler interface.", bean.getClass().getName()));
            }

            WebsocketMapping annotation = AnnotationUtils.getAnnotation(bean.getClass(), WebsocketMapping.class);
            handlerMap.put(Objects.requireNonNull(annotation).value(), (WebSocketHandler) bean);
        });

        super.setOrder(Ordered.HIGHEST_PRECEDENCE);
        super.setUrlMap(handlerMap);
        super.initApplicationContext();
    }
}
