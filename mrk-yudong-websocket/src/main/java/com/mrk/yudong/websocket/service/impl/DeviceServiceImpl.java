package com.mrk.yudong.websocket.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.merach.sun.device.api.ReportApi;
import com.merach.sun.device.dto.cmd.report.HeartRateReportCmd;
import com.merach.sun.device.dto.cmd.report.ReportSportsTrainingCmd;
import com.mrk.yudong.share.constant.RedisKeyConstant;
import com.mrk.yudong.share.vo.WebsocketSendVO;
import com.mrk.yudong.websocket.dto.ReceiveDTO;
import com.mrk.yudong.websocket.dto.ReportSportsTrainingDTO;
import com.mrk.yudong.websocket.enums.BizType;
import com.mrk.yudong.websocket.service.IDeviceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;

@Slf4j
@RequiredArgsConstructor
@Service("biz.service.DEVICE")
public class DeviceServiceImpl extends BaseBizServiceImpl implements IDeviceService {

    private final ReportApi reportApi;

    private final StringRedisTemplate stringRedisTemplate;

    @Override
    public WebsocketSendVO handleReceiveMessage(ReceiveDTO receiveDTO) {
        reportSportsTraining(receiveDTO);
        saveHeartRateReport(receiveDTO);
        this.setRateKcal(receiveDTO);
        return buildMessage(receiveDTO.getSessionKey(), receiveDTO.getRequestId(), new Object());
    }

    @Override
    public void disConnect(String sessionKey) {

    }

    protected Integer getBizType() {
        return BizType.DEVICE.getCode();
    }

    private void reportSportsTraining(ReceiveDTO receiveDTO) {
        if (ObjectUtil.isNull(receiveDTO.getData())) {
            return;
        }
        ReportSportsTrainingCmd cmd = receiveDTO.getData().to(ReportSportsTrainingCmd.class);
        cmd.setOriginDeviceRelId(receiveDTO.getBizId());
        try {
            reportApi.reportSportsTraining(cmd);
        } catch (Exception e) {
            log.warn("运动数据插入失败:{}", e);
        }
    }

    private void saveHeartRateReport(ReceiveDTO receiveDTO) {
        if (ObjectUtil.isNull(receiveDTO.getData())) {
            return;
        }
        HeartRateReportCmd cmd = receiveDTO.getData().to(HeartRateReportCmd.class);
        cmd.setDeviceId(receiveDTO.getBizId());
        if (null == cmd.getRate() || 0 == cmd.getRate()) {
            log.info("rate为0");
            return;
        }

        try {
            log.info("开始插入时序数据库");
            reportApi.reportHeartRate(cmd);
        } catch (Exception e) {
            log.warn("存入时序数据库失败：{}", e.getMessage());
        }
    }

    private void setRateKcal(ReceiveDTO receiveDTO) {
        if (ObjectUtil.isNull(receiveDTO.getData())) {
            return;
        }

        ReportSportsTrainingDTO trainingDTO = receiveDTO.getData().to(ReportSportsTrainingDTO.class);
        Long sportId = trainingDTO.getSportId();
        if (sportId == null || sportId <= 0L) {
            return;
        }

        if (trainingDTO.getRateKcal() == null || trainingDTO.getRateKcal().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        log.warn("userId: {} sportReportId: {} rateKcal: {}", trainingDTO.getUserId(), sportId, trainingDTO.getRateKcal());
        String dataKey = String.format(RedisKeyConstant.SOCKET_UPLOAD_DETAIL_RATE_KCAL, sportId);
        String rateKcalStr = stringRedisTemplate.opsForValue().get(dataKey);
        BigDecimal rateKcal = StrUtil.isBlank(rateKcalStr) ? BigDecimal.ZERO : new BigDecimal(rateKcalStr);
        if (trainingDTO.getRateKcal().compareTo(rateKcal) <= 0) {
            return;
        }

        stringRedisTemplate.opsForValue().set(dataKey, trainingDTO.getRateKcal().toString(), Duration.ofDays(1L));
    }

}
