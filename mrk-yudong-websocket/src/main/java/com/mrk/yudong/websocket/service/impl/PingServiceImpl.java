package com.mrk.yudong.websocket.service.impl;

import com.mrk.yudong.share.vo.WebsocketSendVO;
import com.mrk.yudong.websocket.dto.ReceiveDTO;
import com.mrk.yudong.websocket.dto.SendDTO;
import com.mrk.yudong.websocket.enums.BizType;
import com.mrk.yudong.websocket.service.IPingService;
import com.mrk.yudong.websocket.service.IWebsocketSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service("biz.service.PING")
public class PingServiceImpl extends BaseBizServiceImpl implements IPingService {

    private final IWebsocketSendService websocketSendService;

    @Override
    public WebsocketSendVO handleReceiveMessage(ReceiveDTO receiveDTO) {
        SendDTO sendDTO = new SendDTO(101, receiveDTO.getRequestId(), null);
        WebsocketSendVO websocketSendVO = new WebsocketSendVO();
        websocketSendVO.setData(sendDTO);
        websocketSendVO.setKey(receiveDTO.getSessionKey());
        websocketSendService.send(websocketSendVO);
        return null;
    }

    @Override
    public void disConnect(String sessionKey) {

    }

    protected Integer getBizType() {
        return BizType.PING.getCode();
    }
}
