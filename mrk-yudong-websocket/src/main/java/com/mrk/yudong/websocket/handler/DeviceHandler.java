package com.mrk.yudong.websocket.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.merach.sun.device.api.ReportApi;
import com.merach.sun.device.dto.cmd.report.HeartRateReportCmd;
import com.mrk.yudong.share.util.WebsocketSignUtil;
import com.mrk.yudong.share.vo.WebsocketSendVO;
import com.mrk.yudong.websocket.annotation.WebsocketMapping;
import com.mrk.yudong.websocket.constant.DeviceConstants;
import com.mrk.yudong.websocket.core.R;
import com.mrk.yudong.websocket.core.WebsocketSender;
import com.mrk.yudong.websocket.service.IWebsocketSendService;
import com.mrk.yudong.websocket.util.WebscoketUtil;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.socket.HandshakeInfo;
import org.springframework.web.reactive.socket.WebSocketHandler;
import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@RequiredArgsConstructor
@WebsocketMapping("/device")
@Component
public class DeviceHandler implements WebSocketHandler {

    private final ConcurrentHashMap<String, WebsocketSender> webSocketSessionMap;

    private final IWebsocketSendService websocketSendService;

    private final ReportApi reportApi;

    /**
     * 客户端最近的一次心跳时间集合
     */
    private static ConcurrentHashMap<String, LocalDateTime> heartbeatMap = new ConcurrentHashMap<>();

    @NonNull
    @Override
    public Mono<Void> handle(WebSocketSession webSocketSession) {
        log.info("webSocketSession:{}",JSONObject.toJSONString(webSocketSession));
        String id = webSocketSession.getId();
        HandshakeInfo handshakeInfo = webSocketSession.getHandshakeInfo();
        String query = handshakeInfo.getUri().getQuery();
        Map<String, Object> queryMap = WebscoketUtil.buildQueryMap(query);
        boolean open = webSocketSession.isOpen();
        if (!open) {
            return WebscoketUtil.error(webSocketSession, 404, "scoket close");
        }
        if (MapUtil.isEmpty(queryMap)) {
            return WebscoketUtil.error(webSocketSession, 403, "Forbidden");
        }

        Long deviceId = MapUtil.getLong(queryMap, "deviceId");
        String sign = MapUtil.getStr(queryMap, "sign");
        if (deviceId == null || StrUtil.isBlank(sign)) {
            log.warn("+++++deviceId,{} ,sign{}+++++++", deviceId, sign);
            return WebscoketUtil.error(webSocketSession, 400, "BadRequest");
        }

        boolean valid = WebsocketSignUtil.valid(deviceId.toString(), sign, false);
        if (!valid) {
            return WebscoketUtil.error(webSocketSession, 403, "Forbidden");
        }

        String sessionKey = String.format(WebsocketSendVO.SESSION_KEY, deviceId, id);
        Mono<Void> input = webSocketSession.receive()
                .subscribeOn(Schedulers.boundedElastic())
                .doOnNext(webSocketMessage -> {
                    String payloadAsText = webSocketMessage.getPayloadAsText();
                    log.info("============= 连接：{} 发送的消息为：{} =============", id, payloadAsText);
                    JSONObject dict;
                    try {
                        dict = JSON.parseObject(payloadAsText);
                    } catch (Exception e) {
                        websocketSendService.error(sessionKey, 400, "无效参数");
                        return;
                    }
                    //插入时序数据库
                    dict.put("deviceId",deviceId);
                    saveHeartRateReport(dict);
                    heartbeatMap.put(sessionKey, LocalDateTime.now());
                }).then();

        Mono<Void> output = webSocketSession.send(Flux.create(sink -> {
            log.info("sessionId:{}，开始链接",id);
            Dict dict = new Dict(1);
            dict.put("sessionId", id);
            sink.next(webSocketSession.textMessage(JSON.toJSONString(R.ok(dict))));
            // 连接上来，立即添加一次心跳时间，否则会导致MessageListener开启不了
            webSocketSessionMap.put(sessionKey, new WebsocketSender(webSocketSession, sink));
            heartbeatMap.put(sessionKey, LocalDateTime.now());
        })).then();

        return Mono.zip(input, output).doFinally(signalType -> {
            webSocketSessionMap.remove(sessionKey);
            heartbeatMap.remove(sessionKey);
            log.info("============== 连接：{} 断开，key为：{} 状态为：{} ==============", id, sessionKey, signalType);
        }).then();
    }

    private void saveHeartRateReport(JSONObject dict) {
        Integer rate = dict.getInteger("rate");
        if (null == rate || 0 == rate) {
            log.info("rate为0");
            return;
        }
        Long userId = dict.getLong("userId");
        if(Objects.isNull(userId)){
            log.info("saveHeartRateReport->userId is null");
            return;
        }
        HeartRateReportCmd heartRateReportCmd = new HeartRateReportCmd();
        heartRateReportCmd.setRate(rate);
        heartRateReportCmd.setDeviceId(dict.getLong("deviceId"));
        heartRateReportCmd.setUserId(dict.getLong("userId"));
        heartRateReportCmd.setBattery(dict.getInteger("battery"));
        try {
            log.info("开始插入时序数据库");
            reportApi.reportHeartRate(heartRateReportCmd);
        } catch (Exception e) {
            log.warn("存入时序数据库失败：{}", e.getMessage());
        }

    }


    @Scheduled(cron = "0 */1 * * * ?")
    public void sendWeek() {
        LocalDateTime now = LocalDateTime.now();
        List<String> rmSessionKeyList = new ArrayList<>();
        heartbeatMap.forEach((sessionKey, lastConnectTime) -> {
            boolean webscoketIdleTimeout = isWebscoketIdleTimeout(now, lastConnectTime);
            if (webscoketIdleTimeout) {
                rmSessionKeyList.add(sessionKey);
            }
        });

        if (CollUtil.isNotEmpty(rmSessionKeyList)) {
            rmSessionKeyList.forEach(sessionKey -> {
                WebsocketSendVO websocketSendVO = new WebsocketSendVO();
                websocketSendVO.setKey(sessionKey);
                websocketSendService.sendAndClose(websocketSendVO);
                webSocketSessionMap.remove(sessionKey);
                heartbeatMap.remove(sessionKey);
                log.info("=========清除webSocketSessionMap，sessionKey{}", sessionKey);
            });
        }
    }

    private boolean isWebscoketIdleTimeout(LocalDateTime now, LocalDateTime lastConnectTime) {
        return LocalDateTimeUtil.between(lastConnectTime, now).getSeconds()
                > DeviceConstants.WEBSOCKET_CLOSE_TIME;
    }
}
