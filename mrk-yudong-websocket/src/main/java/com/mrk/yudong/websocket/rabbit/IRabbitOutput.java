package com.mrk.yudong.websocket.rabbit;

import com.mrk.yudong.share.constant.QueueConstant;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

@Component
public interface IRabbitOutput {

    @Output(QueueConstant.WEBSOCKET_QUEUE)
    MessageChannel websocketOutput();

    @Output(QueueConstant.CLOSE_WEBSOCKET_QUEUE)
    MessageChannel closeSocketOutput();

}
