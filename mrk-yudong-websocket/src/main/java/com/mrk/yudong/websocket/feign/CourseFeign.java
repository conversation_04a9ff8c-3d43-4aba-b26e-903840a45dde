package com.mrk.yudong.websocket.feign;

import com.mrk.yudong.websocket.core.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "yudong-course")
public interface CourseFeign {

    /**
     * 获取大屏长连接指定信息
     *
     * @param courseId 课程ID
     * @return com.mrk.yudong.websocket.core.R
     * <AUTHOR>
     * @date 2022/1/27 14:51
     */
    @GetMapping("/socket/info")
    R getCourseInfo(@RequestParam("courseId") Long courseId);

}
