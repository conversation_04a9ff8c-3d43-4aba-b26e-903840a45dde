package com.mrk.yudong.websocket.core;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class R {

    private static final String DEFAULT_OK_MESSAGE = "SUCCESS";

    private static final String DEFAULT_FAIL_MESSAGE = "FAIL";

    private Integer bizType;

    private String requestId;

    private int status;

    private String message;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Object data;

    public static R ok() {
        return R.builder().status(HttpStatus.OK.value()).message(DEFAULT_OK_MESSAGE).build();
    }

    public static R ok(Object data) {
        return R.builder().status(HttpStatus.OK.value()).message(DEFAULT_OK_MESSAGE).data(data).build();
    }

    public static R fail() {
        return R.builder().status(HttpStatus.NOT_IMPLEMENTED.value()).message(DEFAULT_FAIL_MESSAGE).build();
    }

    public static R fail(String message) {
        return R.builder().status(HttpStatus.NOT_IMPLEMENTED.value()).message(message).build();
    }

    public static R fail(int status, String message) {
        return R.builder().status(status).message(message).build();
    }

    public static R paramFail(String message) {
        return R.builder().status(HttpStatus.BAD_REQUEST.value()).message(message).build();
    }

    public static boolean isFail(R r) {
        if (r == null) {
            return true;
        }

        if (r.getStatus() != HttpStatus.OK.value()) {
            return true;
        }

        return false;
    }

}
