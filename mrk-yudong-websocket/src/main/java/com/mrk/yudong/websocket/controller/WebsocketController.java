package com.mrk.yudong.websocket.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.mrk.yudong.share.util.WebsocketSignUtil;
import com.mrk.yudong.share.vo.SocketVO;
import com.mrk.yudong.share.vo.WebsocketSendVO;
import com.mrk.yudong.websocket.core.R;
import com.mrk.yudong.websocket.core.WebsocketSender;
import com.mrk.yudong.websocket.rabbit.IRabbitOutput;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@RequiredArgsConstructor
@RestController
public class WebsocketController {

    private final IRabbitOutput rabbitOutput;

    private final ConcurrentHashMap<String, WebsocketSender> webSocketSessionMap;

    /**
     * 发送长连接消息
     *
     * @param socketVO 长连接信息
     * @return reactor.core.publisher.Mono<com.mrk.yudong.websocket.core.R>
     * <AUTHOR>
     * @date 2022/1/27 14:49
     */
    @PostMapping("/send")
    public Mono<R> send(@RequestBody SocketVO socketVO) {
        log.warn("=== send param: {} ===", JSON.toJSONString(socketVO));

        String key = socketVO.getKey();
        Object data = socketVO.getData();
        if (StrUtil.isBlank(key) || data == null) {
            return Mono.just(R.paramFail("非法请求参数"));
        }

        WebsocketSendVO websocketSendVO = new WebsocketSendVO(key, data, false);
        Message<WebsocketSendVO> message = MessageBuilder.withPayload(websocketSendVO).build();
        rabbitOutput.websocketOutput().send(message);
        return Mono.just(R.ok());
    }

    /**
     * 发送指定长连接消息
     *
     * @param socketVO 长连接信息
     * @return reactor.core.publisher.Mono<com.mrk.yudong.websocket.core.R>
     * <AUTHOR>
     * @date 2022/1/27 14:50
     */
    @PostMapping("/send/id")
    public Mono<R> sendById(@RequestBody SocketVO socketVO) {
        String key = socketVO.getKey();
        String sessionId = socketVO.getSessionId();
        Object data = socketVO.getData();
        if (StrUtil.isBlank(key) || StrUtil.isBlank(sessionId) || data == null) {
            return Mono.just(R.paramFail("非法请求参数"));
        }

        WebsocketSendVO websocketSendVO = new WebsocketSendVO(key, sessionId, data, true);
        Message<WebsocketSendVO> message = MessageBuilder.withPayload(websocketSendVO).build();
        rabbitOutput.websocketOutput().send(message);
        return Mono.just(R.ok());
    }

    /**
     * 关闭长连接
     *
     * @param socketVO 长连接信息
     * @return reactor.core.publisher.Mono<com.mrk.yudong.websocket.core.R>
     * <AUTHOR>
     * @date 2022/1/27 14:50
     */
    @PostMapping("/close")
    public Mono<R> close(@RequestBody SocketVO socketVO) {
        String key = socketVO.getKey();
        Object data = socketVO.getData();
        if (StrUtil.isBlank(key) || data == null) {
            return Mono.just(R.paramFail("非法请求参数"));
        }

        WebsocketSendVO websocketSendVO = new WebsocketSendVO();
        websocketSendVO.setKey(key);
        websocketSendVO.setData(data);
        Message<WebsocketSendVO> message = MessageBuilder.withPayload(websocketSendVO).build();
        rabbitOutput.closeSocketOutput().send(message);
        return Mono.just(R.ok());
    }

    /**
     * 获取长连接信息
     *
     * @return reactor.core.publisher.Mono<com.mrk.yudong.websocket.core.R>
     * <AUTHOR>
     * @date 2022/1/27 14:50
     */
    @GetMapping("/socket")
    public Mono<R> socket() {
        return Mono.just(R.ok(webSocketSessionMap.keys()));
    }

    @GetMapping("/sign")
    public R getSign(Long id) {
        if(ObjectUtil.isNull(id)) {
            return R.fail();
        }
        return R.ok(WebsocketSignUtil.sign(id.toString()));
    }

}
