package com.mrk.yudong.websocket.service.impl;

import com.mrk.yudong.share.vo.WebsocketSendVO;
import com.mrk.yudong.websocket.dto.ReceiveDTO;
import com.mrk.yudong.websocket.dto.SendDTO;
import com.mrk.yudong.websocket.vo.WebsocketSendErrorVO;

public class BaseBizServiceImpl {
    protected WebsocketSendVO buildMessage(String keyPrefix, Object data) {
        SendDTO sendDTO = new SendDTO(getBizType(), data);
        WebsocketSendVO websocketSendVO = new WebsocketSendVO();
        websocketSendVO.setData(sendDTO);
        websocketSendVO.setKey(keyPrefix);
        return websocketSendVO;
    }

    protected WebsocketSendVO buildMessage(String keyPrefix, String requestId, Object data) {
        SendDTO sendDTO = new SendDTO(getBizType(), requestId, data);
        WebsocketSendVO websocketSendVO = new WebsocketSendVO();
        websocketSendVO.setData(sendDTO);
        websocketSendVO.setKey(keyPrefix);
        return websocketSendVO;
    }

    protected WebsocketSendErrorVO buildErrorMessage(ReceiveDTO receiveDTO, int status, String message) {
        SendDTO sendDTO = new SendDTO(getBizType(), receiveDTO.getRequestId(), message);
        WebsocketSendErrorVO websocketSendVO = new WebsocketSendErrorVO();
        websocketSendVO.setKey(receiveDTO.getSessionKey());
        websocketSendVO.setData(sendDTO);
        websocketSendVO.setStatus(status);
        return websocketSendVO;
    }

    protected Integer getBizType() {
        return null;
    }
}
