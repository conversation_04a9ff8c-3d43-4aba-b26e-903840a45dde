package com.mrk.yudong.websocket.service.impl;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import com.mrk.yudong.share.vo.ActionAssessMessageVO;
import com.mrk.yudong.share.vo.WebsocketSendVO;
import com.mrk.yudong.websocket.core.R;
import com.mrk.yudong.websocket.dto.ActionAssessDTO;
import com.mrk.yudong.websocket.dto.ReceiveDTO;
import com.mrk.yudong.websocket.enums.ActionAssessType;
import com.mrk.yudong.websocket.enums.BizType;
import com.mrk.yudong.websocket.feign.UserFeign;
import com.mrk.yudong.websocket.service.IActionAssessService;
import com.mrk.yudong.websocket.service.IWebsocketSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@RequiredArgsConstructor
@Service("biz.service.ACTION_ASSESS")
public class ActionAssessServiceImpl extends BaseBizServiceImpl implements IActionAssessService {

    private final UserFeign userFeign;

    private final IWebsocketSendService websocketSendService;

    private static ConcurrentHashMap<String, Long> actionAssessMap = new ConcurrentHashMap<>();

    @Override
    public WebsocketSendVO handleReceiveMessage(ReceiveDTO receiveDTO) {
        ActionAssessDTO actionAssessDTO = receiveDTO.getData().to(ActionAssessDTO.class);
        ActionAssessType type = EnumUtil.getBy(ActionAssessType::getCode, actionAssessDTO.getType());
        R result = null;
        switch (type) {
            case START:
                result = startActionAssess(receiveDTO);
                break;
            case END:
                result = endActionAssess(receiveDTO);
                break;
            case TIPS:
                result = userFeign.unitChangeTips(actionAssessDTO.getAssessId());
                break;
            case UNIT_CHANGE:
                result = userFeign.unitChange(actionAssessDTO.getAssessId(), actionAssessDTO.getLinkId());
                break;
            case RECONNECT:
                result = reconnectActionAssess(receiveDTO);
                break;
        }

        if (result.getStatus() != 200) {
            log.warn("handleReceiveMessage->receiveDTO: {}, result: {}", receiveDTO, result);
            websocketSendService.error(buildErrorMessage(receiveDTO, HttpStatus.INTERNAL_SERVER_ERROR.value(), "测评异常，请重试"));
            return null;
        }
        return buildMessage(receiveDTO.getSessionKey(), receiveDTO.getRequestId(), result.getData());
    }

    @Override
    public void disConnect(String sessionKey) {
        actionAssessMap.remove(sessionKey);
    }

    @Scheduled(cron = "* * * * * ?")
    public void sendAssess() {
        actionAssessMap.forEach((k, v) -> {
            ActionAssessMessageVO message = userFeign.actionAssessHandle(v).getData();
            if (ObjectUtil.isNull(message)) {
                return;
            }
            if (BigDecimal.valueOf(-1).equals(message.getAdjustValue())) {
                message.setAdjustValue(null);
            }
            websocketSendService.send(buildMessage(k, message));
        });
    }

    protected Integer getBizType() {
        return BizType.ACTION_ASSESS.getCode();
    }

    private R startActionAssess(ReceiveDTO receiveDTO) {
        R result = userFeign.startActionAssess(receiveDTO.getBizId());
        actionAssessMap.put(receiveDTO.getSessionKey(), receiveDTO.getBizId());
        return result;
    }

    private R reconnectActionAssess(ReceiveDTO receiveDTO) {
        if (ObjectUtil.isNull(receiveDTO) || ObjectUtil.isNull(receiveDTO.getBizId())) {
            return R.fail();
        }
        actionAssessMap.put(receiveDTO.getSessionKey(), receiveDTO.getBizId());
        return R.ok();
    }

    private R endActionAssess(ReceiveDTO receiveDTO) {
        actionAssessMap.remove(receiveDTO.getSessionKey());
        return userFeign.endActionAssess(receiveDTO.getBizId());
    }

}
