package com.mrk.yudong.websocket.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mrk.yudong.share.bo.SignInDTO;
import com.mrk.yudong.share.util.JwtUtil;
import com.mrk.yudong.share.util.WebsocketSignUtil;
import com.mrk.yudong.share.vo.WebsocketSendVO;
import com.mrk.yudong.websocket.annotation.WebsocketMapping;
import com.mrk.yudong.websocket.constant.DeviceConstants;
import com.mrk.yudong.websocket.core.R;
import com.mrk.yudong.websocket.core.WebsocketSender;
import com.mrk.yudong.websocket.dto.ReceiveDTO;
import com.mrk.yudong.websocket.router.BizRouter;
import com.mrk.yudong.websocket.service.IWebsocketSendService;
import com.mrk.yudong.websocket.util.WebscoketUtil;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.socket.HandshakeInfo;
import org.springframework.web.reactive.socket.WebSocketHandler;
import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@RequiredArgsConstructor
@WebsocketMapping("/common")
@Component
public class CommonHandler implements WebSocketHandler {

    private static final String sessionKeyPrefix = "Common:";

    private final ConcurrentHashMap<String, WebsocketSender> webSocketSessionMap;

    private final IWebsocketSendService websocketSendService;

    private final BizRouter bizRouter;
    /**
     * 客户端最近的一次心跳时间集合
     */
    private static final ConcurrentHashMap<String, LocalDateTime> heartbeatMap = new ConcurrentHashMap<>();
    private final JwtUtil jwtUtil;

    @NonNull
    @Override
    public Mono<Void> handle(WebSocketSession webSocketSession) {
        log.info("webSocketSession:{}", JSONObject.toJSONString(webSocketSession));
        String id = webSocketSession.getId();
        HandshakeInfo handshakeInfo = webSocketSession.getHandshakeInfo();
        String query = handshakeInfo.getUri().getQuery();
        Map<String, Object> queryMap = WebscoketUtil.buildQueryMap(query);
        boolean open = webSocketSession.isOpen();
        if (!open) {
            return WebscoketUtil.error(webSocketSession, 404, "scoket close");
        }
        if (MapUtil.isEmpty(queryMap)) {
            return WebscoketUtil.error(webSocketSession, 403, "Forbidden");
        }

        Long userId = MapUtil.getLong(queryMap, "userId");
        String sign = MapUtil.getStr(queryMap, "sign");
        String token = MapUtil.getStr(queryMap, "token");
        if (userId == null || (StrUtil.isBlank(sign) && StrUtil.isBlank(token))) {
            log.warn("+++++userId:{} ,sign:{}, token:{}+++++++", userId, sign, token);
            return WebscoketUtil.error(webSocketSession, 400, "BadRequest");
        }

        boolean valid = WebsocketSignUtil.valid(userId.toString(), sign, false);
        if (!valid) {
            valid = validToken(token, userId);
        }
        if (!valid) {
            return WebscoketUtil.error(webSocketSession, 403, "Forbidden");
        }

        String sessionKey = sessionKeyPrefix + String.format(WebsocketSendVO.SESSION_KEY, userId, id);
        Mono<Void> input = webSocketSession.receive()
                .subscribeOn(Schedulers.boundedElastic())
                .doOnNext(webSocketMessage -> {
                    String payloadAsText = webSocketMessage.getPayloadAsText();
                    log.info("============= 连接：{} 发送的消息为：{} =============", id, payloadAsText);
                    ReceiveDTO receiveDTO;
                    try {
                        receiveDTO = JSON.parseObject(payloadAsText, ReceiveDTO.class);
                        receiveDTO.setUserId(userId);
                        receiveDTO.setSessionKey(sessionKey);
                    } catch (Exception e) {
                        websocketSendService.error(sessionKey, 400, "无效参数");
                        return;
                    }
                    bizRouter.receiveMessage(receiveDTO);
                    heartbeatMap.put(sessionKey, LocalDateTime.now());
                }).then();

        Mono<Void> output = webSocketSession.send(Flux.create(sink -> {
            log.info("sessionId:{}，开始链接", id);
            Dict dict = new Dict(1);
            dict.put("sessionId", id);
            dict.put("timestamp", DateUtil.current());
            R r = R.ok(dict);
            r.setRequestId("0");
            sink.next(webSocketSession.textMessage(JSON.toJSONString(r)));
            // 连接上来，立即添加一次心跳时间，否则会导致MessageListener开启不了
            webSocketSessionMap.put(sessionKey, new WebsocketSender(webSocketSession, sink));
            heartbeatMap.put(sessionKey, LocalDateTime.now());
        })).then();

        return Mono.zip(input, output).doFinally(signalType -> {
            bizRouter.disConnect(sessionKey);
            webSocketSessionMap.remove(sessionKey);
            heartbeatMap.remove(sessionKey);
            log.info("============== 连接：{} 断开，key为：{} 状态为：{} ==============", id, sessionKey, signalType);
        }).then();
    }

    @Scheduled(cron = "0 */1 * * * ?")
    public void sendWeek() {
        LocalDateTime now = LocalDateTime.now();
        log.info("connect socket size: {}, time: {}", webSocketSessionMap.size(), now);
        List<String> rmSessionKeyList = new ArrayList<>();
        heartbeatMap.forEach((sessionKey, lastConnectTime) -> {
            boolean webscoketIdleTimeout = isWebscoketIdleTimeout(now, lastConnectTime);
            if (webscoketIdleTimeout) {
                rmSessionKeyList.add(sessionKey);
            }
        });

        if (CollUtil.isNotEmpty(rmSessionKeyList)) {
            rmSessionKeyList.forEach(sessionKey -> {
                bizRouter.disConnect(sessionKey);
                WebsocketSendVO websocketSendVO = new WebsocketSendVO();
                websocketSendVO.setKey(sessionKey);
                websocketSendService.sendAndClose(websocketSendVO);
                webSocketSessionMap.remove(sessionKey);
                heartbeatMap.remove(sessionKey);
                log.info("=========清除webSocketSessionMap，sessionKey{}", sessionKey);
            });
        }
    }

    private boolean isWebscoketIdleTimeout(LocalDateTime now, LocalDateTime lastConnectTime) {
        return LocalDateTimeUtil.between(lastConnectTime, now).getSeconds()
                > DeviceConstants.WEBSOCKET_CLOSE_TIME;
    }

    private boolean validToken(String token, Long userId) {
        if (StrUtil.isBlank(token)) {
            return false;
        }
        SignInDTO signInDTO = jwtUtil.decode(token);
        if (signInDTO.getUserId() == null) {
            signInDTO = jwtUtil.getSignInDTO(token).setIsTra(0);
        }
        return !ObjectUtil.isNull(signInDTO.getUserId()) && signInDTO.getUserId().equals(userId);
    }
}
