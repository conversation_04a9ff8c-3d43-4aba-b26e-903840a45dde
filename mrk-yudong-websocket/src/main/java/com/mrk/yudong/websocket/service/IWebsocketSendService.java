package com.mrk.yudong.websocket.service;

import com.mrk.yudong.share.vo.WebsocketSendVO;
import com.mrk.yudong.websocket.vo.WebsocketSendErrorVO;

public interface IWebsocketSendService {

    /**
     * 发送信息
     *
     * @param websocketSendVO 长连接发送相关信息
     * <AUTHOR>
     * @date 2022/1/27 14:52
     */
    void send(WebsocketSendVO websocketSendVO);

    /**
     * 发送信息并关闭连接
     *
     * @param websocketSendVO 长连接发送相关信息
     * <AUTHOR>
     * @date 2022/1/27 14:52
     */
    void sendAndClose(WebsocketSendVO websocketSendVO);

    /**
     * 发送错误信息并关闭连接
     *
     * @param key     指定KEY
     * @param status  状态码
     * @param message 提示信息
     * <AUTHOR>
     * @date 2022/1/27 14:53
     */
    void error(String key, int status, String message);

    void error(WebsocketSendErrorVO websocketSendErrorVO);

    void sendMessage(String keyPrefix, Object data);
}
