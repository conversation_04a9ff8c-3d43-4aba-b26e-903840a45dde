package com.mrk.yudong.websocket.config;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.cloud.nacos.registry.NacosRegistration;
import com.alibaba.cloud.nacos.registry.NacosServiceRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;

import javax.annotation.PreDestroy;

/**
 * <AUTHOR>
 * @date 2022/3/3 19:27
 */
@Slf4j
@RequiredArgsConstructor
@SpringBootConfiguration
public class HealthConfig {

    private final NacosRegistration nacosRegistration;

    private final NacosServiceRegistry nacosServiceRegistry;

    @PreDestroy
    private void deregisterInstance() {
        NacosDiscoveryProperties nacosDiscoveryProperties = nacosRegistration.getNacosDiscoveryProperties();
        String serviceName = nacosDiscoveryProperties.getService();
        String groupName = nacosDiscoveryProperties.getGroup();
        String clusterName = nacosDiscoveryProperties.getClusterName();
        String ip = nacosDiscoveryProperties.getIp();
        int port = nacosDiscoveryProperties.getPort();

        log.warn("deregister from nacos, serviceName:{}, groupName:{}, clusterName:{}, ip:{}, port:{}", serviceName, groupName, clusterName, ip, port);
        nacosServiceRegistry.deregister(nacosRegistration);
    }

}
