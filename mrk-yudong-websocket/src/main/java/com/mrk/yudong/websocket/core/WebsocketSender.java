package com.mrk.yudong.websocket.core;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.reactive.socket.WebSocketMessage;
import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.publisher.FluxSink;

@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class WebsocketSender {

    private WebSocketSession webSocketSession;

    private FluxSink<WebSocketMessage> sink;

    public void send(Object data) {
        R r = R.ok();
        BeanUtil.copyProperties(data, r);
        log.info("WebsocketSender send: {}",r);
        WebSocketMessage webSocketMessage = this.webSocketSession.textMessage(JSON.toJSONString(r));
        this.sink.next(webSocketMessage);
    }

    public void sendAndClose(Object data) {
        R r = R.ok();
        BeanUtil.copyProperties(data, r);
        log.info("WebsocketSender sendAndClose: {}",r);
        WebSocketMessage webSocketMessage = this.webSocketSession.textMessage(JSON.toJSONString(r));
        this.sink.next(webSocketMessage).complete();
    }

    public void error(int status, String message) {
        log.info("WebsocketSender error, status: {}, message: {}",status,message);
        WebSocketMessage webSocketMessage = this.webSocketSession.textMessage(JSON.toJSONString(R.fail(status, message)));
        this.sink.next(webSocketMessage);
    }

    public void error(int status, Object data) {
        R r = R.fail(status, "");
        BeanUtil.copyProperties(data, r);
        log.info("WebsocketSender error, status: {}, r: {}",status, r);
        WebSocketMessage webSocketMessage = this.webSocketSession.textMessage(JSON.toJSONString(r));
        this.sink.next(webSocketMessage);
    }
    public void close(int status, String message) {
        sink.complete();
    }
}
