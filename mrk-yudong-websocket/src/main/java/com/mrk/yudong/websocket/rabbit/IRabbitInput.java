package com.mrk.yudong.websocket.rabbit;

import com.mrk.yudong.share.constant.QueueConstant;
import org.springframework.cloud.stream.annotation.Input;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.stereotype.Component;

@Component
public interface IRabbitInput {

    @Input(QueueConstant.WEBSOCKET_QUEUE)
    SubscribableChannel websocketInput();

    @Input(QueueConstant.CLOSE_WEBSOCKET_QUEUE)
    SubscribableChannel closeSocketInput();

}
