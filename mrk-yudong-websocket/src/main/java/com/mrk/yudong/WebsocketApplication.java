package com.mrk.yudong;

import com.mrk.yudong.websocket.rabbit.IRabbitInput;
import com.mrk.yudong.websocket.rabbit.IRabbitOutput;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@EnableBinding(value = {IRabbitInput.class, IRabbitOutput.class})
@EnableDiscoveryClient
@SpringBootApplication
@EnableFeignClients(basePackages = { "com.merach.sun.device.api","com.mrk.yudong.websocket.feign"})
@EnableScheduling
public class WebsocketApplication {

    public static void main(String[] args) {
        SpringApplication.run(WebsocketApplication.class, args);
    }

}
