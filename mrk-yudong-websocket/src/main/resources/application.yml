logging:
  config: classpath:logback-spring.xml
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - traceId:%X{EagleEye-TraceID} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - traceId:%X{EagleEye-TraceID} - %msg%n"

server:
  port: 7120


spring:
  application:
    name: yudong-websocket
  profiles:
    active:
      - dev
  output:
    ansi:
      enabled: DETECT
  redis:
    timeout: 3s
    ssl: false
    jedis:
      pool:
        enabled: true
        min-idle: 50
        time-between-eviction-runs: 5s
        max-active: 200
        max-idle: 200
  cloud:
    nacos:
      discovery:
        failure-tolerance-enabled: true
    loadbalancer:
      nacos:
        enabled: true
    stream:
      default-binder: rabbit
      bindings:
        websocket-queue:
          destination: websocket-queue
        close-websocket-queue:
          destination: close-websocket-queue
  main:
    allow-bean-definition-overriding: true


feign:
  client:
    config:
      default:
        connect-timeout: 15000
        read-timeout: 15000
        logger-level: NONE
    refresh-enabled: true
  httpclient:
    enabled: false
    max-connections: 500
    max-connections-per-route: 125
    connection-timeout: 15000
    ok-http:
      read-timeout: 15s
  okhttp:
    enabled: true
  circuitbreaker:
    enabled: true
  compression:
    request:
      enabled: true
    response:
      enabled: true


resilience4j:
  timelimiter:
    configs:
      default:
        timeoutDuration: 15s
        cancelRunningFuture: true
  thread-pool-bulkhead:
    configs:
      default:
        maxThreadPoolSize: 200
        coreThreadPoolSize: 100
        queueCapacity: 4
        keepAliveDuration: 180s


management:
  endpoint:
    health:
      probes:
        enabled: true
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
