FROM registry.cn-hangzhou.aliyuncs.com/merach-prod/jdk:11

ENV TZ=Asia/Shanghai

ENV SPRING_PROFILE_ACTIVE=prod
ENV CONFIG_NAMESPACE=1d2a807f-d885-47c6-9207-0697e897f2f0
ENV CONFIG_SERVER_URL=192.168.9.32:8848

WORKDIR /app

ADD ./target/*.jar app.jar

EXPOSE 8080

RUN ln -sf /usr/share/zoneinfo/${TZ} /etc/localtime && echo '${TZ}' > /etc/timezone

ENTRYPOINT [ "sh", "-c", "java $JAVA_OPTS --add-opens java.base/java.lang=ALL-UNNAMED -XX:+HeapDumpOnOutOfMemoryError -Duser.timezone=${TZ} -Djava.security.egd=file:/dev/./urandom ${JAVA_TOOL_OPTIONS} -Dspring.profiles.active=${SPRING_PROFILE_ACTIVE} -Dconfig.server.url=${CONFIG_SERVER_URL} -Dconfig.namespace=${CONFIG_NAMESPACE} -jar app.jar" ]