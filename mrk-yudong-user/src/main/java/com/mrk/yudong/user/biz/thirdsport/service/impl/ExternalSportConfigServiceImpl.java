package com.mrk.yudong.user.biz.thirdsport.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.mrk.yudong.core.service.BaseServiceImpl;
import com.mrk.yudong.user.mapper.datacenter.ExternalSportConfigMapper;
import com.mrk.yudong.user.model.ExternalSportConfig;
import com.mrk.yudong.user.biz.thirdsport.service.IExternalSportConfigService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 外部运动配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-28
 */
@Service
public class ExternalSportConfigServiceImpl extends BaseServiceImpl<ExternalSportConfigMapper, ExternalSportConfig> implements IExternalSportConfigService {

    @Override
    public ExternalSportConfig getExternalSportConfig(Integer type, Integer terminal) {
        LambdaQueryChainWrapper<ExternalSportConfig> wrapper = new LambdaQueryChainWrapper<>(baseMapper);
        wrapper.eq(ExternalSportConfig::getType, type).eq(ExternalSportConfig::getTerminal, terminal);
        return wrapper.one();
    }

}
